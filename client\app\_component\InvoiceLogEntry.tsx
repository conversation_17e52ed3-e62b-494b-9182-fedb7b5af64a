import { Dialog } from "@radix-ui/react-dialog";
import React from "react";
import { BiSolidTime } from "react-icons/bi";
import { FaUser } from "react-icons/fa";
import { SiTask } from "react-icons/si";

const InvoiceLogEntry = ({ log }: { log: any }) => {
  return (
    <>
      <div className="h-full p-4 flex w-full bg-gray-200/20 shadow-md  rounded-lg border-gray-400">
        <div className="w-full flex items-center px-4 py-1.5 border-r-2  border-grey-400">
          <FaUser className="mr-2 text-main-color w-6 h-6" />
          <span className="font-bold">{log.username}</span>
        </div>
        <div className=" w-full flex items-center border-r-2 px-4  border-grey-400">
          <BiSolidTime className="mr-2 text-main-color w-6 h-6" />
          <span className="font-bold">
            {new Date(log.timestamp).toLocaleString()}
          </span>
        </div>
        <div className="w-full flex items-center px-4">
          <SiTask className="mr-2 text-main-color w-6 h-6" />
          <span className="font-bold">{log.action}</span>
        </div>
      </div>
      <div className="w-[100%] h-[100%] grid grid-cols-2 gap-2 mt-1 items-center py-4">
        <div
          className="w-full h-full bg-gray-200/20 rounded-lg items-center p-4"
          style={{ boxShadow: "5px 0 10px rgb(1, 145, 175, 0.2)" }}
        >
          <p className="font-bold mb-2 uppercase text-main-color">Old Values</p>
          {log.oldValues && Object.entries(log.oldValues).length > 0 ? (
            Object.entries(log.newValues).map(([key, value], index) => (
              <div key={index} className="w-full flex items-center px-4 py-1.5">
                <span className="font-bold">{key}:</span> {/* Display key */}
                <span className="ml-2">
                  {value !== null ? String(value) : "--"}
                </span>{" "}
              </div>
            ))
          ) : (
            <div className="w-full items-center space-items-center p-4 text-center">
              Values Not Found
            </div>
          )}
        </div>
        <div
          className="w-full h-full bg-gray-200/20 rounded-lg items-center p-4"
          style={{ boxShadow: "5px 0px 10px rgb(1, 145, 175, 0.2)" }}
        >
          <p className="font-bold mb-2 uppercase text-main-color">New Values</p>
          {log.newValues && Object.entries(log.newValues).length > 0 ? (
            Object.entries(log.newValues).map(([key, value], index) => (
              <div key={index} className="w-full flex items-center px-4 py-1.5">
                <span className="font-bold">{key}:</span> {/* Display key */}
                <span className="ml-2">
                  {value !== null ? String(value) : "--"}
                </span>{" "}
              </div>
            ))
          ) : (
            <div className="w-full items-center space-items-center p-4 text-center">
              Values Not Found
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default InvoiceLogEntry;
