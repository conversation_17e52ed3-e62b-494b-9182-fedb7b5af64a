import { Request, Response } from "express";
import { updateItem } from "../../../utils/operation";

export const updateDailyPlanning = async (req, res) => {
  const id = req.params.id;
  const userId = req.user_id;
  const fields = {
    daily_planning_date: req.body.daily_planning_date,
    client_id: req.body.client_id,
    user_id: Number(userId),
  };

  const existingDailyPlannning = await prisma.dailyPlanning.findFirst({
    where: {
      client_id: fields.client_id,
      daily_planning_date: fields.daily_planning_date,
      NOT: {
        id: Number(id),
      },
    },
  });
  if (existingDailyPlannning) {
    return res.status(400).json({
      message: "Daily planning template already exists",
    });
  }

  await updateItem({
    model: "dailyPlanning",
    fieldName: "id",
    fields: fields,
    id: Number(id),
    res,
    req,
    successMessage: "Daily Planning has been updated",
  });
};
