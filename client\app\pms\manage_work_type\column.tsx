import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import UpdateWorkType from "./UpdateWorkType";
import DeleteRow from "@/app/_component/DeleteRow";
import { worktype_routes } from "@/lib/routePath";
import { PermissionWrapper } from "@/lib/permissionWrapper";

export interface WorkType {
  work_type: string;
  category: any;
  id: string;
  category_id: any;
  allCategory: any;
  is_work_carrier_specific: boolean; 
  does_it_require_planning_number: boolean; 
  is_backlog_regular_required: boolean;
}

export const column = (permissions: string[], allCategory: any): ColumnDef<WorkType>[] => [
  {
    accessorKey: "Sr. No.",
    header: "Sr. No.",
    cell: ({ row }) => row.index + 1,
  },
  {
    accessorKey: "work_type",
    header: "Work Type",
  },
  {
    accessorKey: "category",
    header: "Category",
    cell: ({ row }) => row.original?.category?.category_name,
    filterFn: (row, columnId, value) => {
      const rowCategory = row.original.category;
      const category = rowCategory?.category_name;
      const filterValue = value.toLowerCase();
      return category?.toLowerCase().includes(filterValue);
    },
  },
  {
    accessorKey: "is_work_carrier_specific",
    header: "Carrier Specific",
    // cell: ({ row }) => {
    //   const value = row.getValue("is_work_carrier_specific");
    //   return value ? "Yes" : "No";
    // },
    accessorFn: (row) => {
      return row?.is_work_carrier_specific ? "Yes" : "No";
    },
    cell: ({ row }) => {
      const carrierSpecific = row?.getValue("is_work_carrier_specific");
      return <>{carrierSpecific}</>;
    },
  },
  {
    accessorKey: "does_it_require_planning_number",
    header: "Actual No",
    // cell: ({ row }) => {
    //   const value = row.getValue("does_it_require_planning_number");
    //   return value ? "Yes" : "No";
    // },
    accessorFn: (row) => {
      return row?.does_it_require_planning_number ? "Yes" : "No";
    },
    cell: ({ row }) => {
      const planningNumber = row?.getValue("does_it_require_planning_number");
      return <>{planningNumber}</>;
    },
  },
  {
    accessorKey: "is_backlog_regular_required",
    header: "Backlog/Regular Required",
    // cell: ({ row }) => {
    //   const value = row.getValue("is_backlog_regular_required");
    //   return value ? "Yes" : "No";
    // },
    accessorFn: (row) => {
      return row?.is_backlog_regular_required ? "Yes" : "No";
    },
    cell: ({ row }) => {
      const backlog = row?.getValue("is_backlog_regular_required");
      return <>{backlog}</>;
    },
  },
  {
    accessorKey: "action",
    header: "Action",
    id: "action",
    cell: ({ row }) => {
      const data = row?.original;

      
      return (
        <div className="flex items-center">
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["update-workType"]}
          >
            <UpdateWorkType data={data} allCategory={allCategory} />
          </PermissionWrapper>
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["delete-workType"]}
          >
            <DeleteRow
              route={`${worktype_routes.DELETE_WORKTYPE}/${data?.id}`}
            />
          </PermissionWrapper>
        </div>
      );
    },
  },
];