import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
      const updatedParams = new URLSearchParams(searchParams);
      if(value){
   
        updatedParams.set("search", value as any); 
     
        replace(`${pathname}?${updatedParams.toString()}`);
      }
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}