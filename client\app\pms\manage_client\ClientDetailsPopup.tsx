"use client";
import React, { useState, useEffect } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import DialogHeading from "@/app/_component/DialogHeading";
import { toast } from "sonner";
import axios from "axios";
import { Plus, Minus, X } from "lucide-react";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";

interface ClientDetailsPopupProps {
  open: boolean;
  setOpen: (val: boolean) => void;
  userData: { username?: string } | any;
  clientId: number | null;
  clientName: string;
  onTriggerAddCustomField: () => void;
}

export default function ClientDetailsPopup({
  open,
  setOpen,
  userData,
  clientId,
  clientName,
  onTriggerAddCustomField,
}: ClientDetailsPopupProps) {
  const router = useRouter();
  const [customFields, setCustomFields] = useState<
    { id: number; name: string }[]
  >([]);
  const [selectedCustomFields, setSelectedCustomFields] = useState<string[]>(
    []
  );
  const [mandatoryFields, setMandatoryFields] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const { handleSubmit } = useForm();

  // To keep track of initial fields fetched from API for change detection
  const [initialMandatoryFields, setInitialMandatoryFields] = useState<
    string[]
  >([]);
  const [initialSelectedCustomFields, setInitialSelectedCustomFields] =
    useState<string[]>([]);

  useEffect(() => {
    if (!open) {
      setSelectedCustomFields([]);
      //setMandatoryFields([]);
      setInitialMandatoryFields([]);
      setInitialSelectedCustomFields([]);
      setSearchQuery("");
      setCustomFields([]);
      return;
    }

    // async function fetchMandatoryFields() {
    //   try {
    //     const res = await axios.get(
    //       `${process.env.NEXT_PUBLIC_BASE_URL}/api/mandatory-fields`
    //     );
    //     const mandFields = res.data.mandatoryFields || [];
    //     setMandatoryFields(mandFields);
    //     setInitialMandatoryFields(mandFields);
    //   } catch (err) {
    //     console.error("Failed to fetch mandatory fields:", err);
    //   }
    // }

    async function fetchCustomFields() {
      try {
        const res = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/custom-fields`
        );
        setCustomFields(
          res.data.sort((a: any, b: any) => a.name.localeCompare(b.name))
        );
      } catch (err) {
        console.error("Failed to load custom fields", err);
        toast.error("Error loading existing custom fields.");
      }
    }

    async function fetchClientFields() {
      if (clientId) {
        try {
          const res = await axios.get(
            `${process.env.NEXT_PUBLIC_BASE_URL}/api/client-custom-fields/${clientId}`
          );

          const { props, custom_fields } = res.data || {};
          if (Array.isArray(props)) {
            setMandatoryFields(props);
            setInitialMandatoryFields(props);
          }
          if (Array.isArray(custom_fields)) {
            const ids = custom_fields.map((field: { id: number | string }) =>
              String(field.id)
            );
            setSelectedCustomFields(ids);
            setInitialSelectedCustomFields(ids);
          }
        } catch (err) {
          console.error("Failed to load client field config", err);
          toast.error("Error loading client's field config.");
        }
      }
    }

    // Fetch in sequence so customFields is set before client fields for better UX
    //fetchMandatoryFields();
    fetchCustomFields().then(() => fetchClientFields());
  }, [open, clientId]);

  const onSubmit = async () => {
    if (!clientId) return toast.error("Client ID is missing.");

    console.log("selectedCustomFields: ", selectedCustomFields);
    try {
      const payload = {
        client_id: clientId,
        custom_fields: selectedCustomFields,
        created_by: userData?.username || "unknown",
        updated_by: userData?.username || "unknown",
      };

      console.log("custom fileds in the payload: ", payload.custom_fields);

      await axios.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/client-custom-fields`,
        payload
      );

      toast.success("Client field headers submitted successfully");
      setOpen(false);
    } catch (err) {
      console.error(err);
      toast.error("Something went wrong while submitting field headers");
    }
  };

  // Disable save button if no changes or no fields selected
  const hasChanges =
    JSON.stringify(initialMandatoryFields) !==
      JSON.stringify(mandatoryFields) ||
    JSON.stringify(initialSelectedCustomFields.sort()) !==
      JSON.stringify(selectedCustomFields.slice().sort());

  const filteredCustomFields = customFields
    .filter(
      (field) =>
        field.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !selectedCustomFields.includes(String(field.id))
    )
    .sort((a, b) => a.name.localeCompare(b.name));

  const selectedCustomFieldsDetails = customFields.filter((field) =>
    selectedCustomFields.includes(String(field.id))
  );

  const handleNavigateToAddCustomFields = () => {
    router.push("/pms/addupdate_custom_fields");
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="md:min-w-[60rem] min-w-[95%] max-h-[90vh]">
        <button
          onClick={() => setOpen(false)}
          className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
          aria-label="Close modal"
          type="button"
        >
          <X size={20} />
        </button>
        <DialogHeading
          title="Add/Update Client's Custom Fields"
          description={
            <span>
              Select or remove custom fields for{" "}
              <span className="inline-block bg-blue-100 text-blue-800 font-semibold px-3 py-1 rounded-full">
                {clientName}
              </span>
            </span>
          }
        />
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Selected Custom Fields */}
            <section
              className="border rounded-lg p-0 flex flex-col h-[400px]"
              aria-label="Selected custom fields"
            >
              {/* Static Header */}
              <div className="bg-white p-4 border-b flex flex-col gap-1">
                <h4 className="text-lg font-semibold mb-2">
                  ✅ Selected Custom Fields
                </h4>
                <div className="text-gray-500 text-sm select-none leading-[38px]">
                  Selected fields appear here. Use the search bar to add more.
                </div>
              </div>

              {/* Scrollable Selected Fields */}
              <div className="overflow-y-auto p-4 space-y-2 flex-1">
                {selectedCustomFieldsDetails.length > 0 ? (
                  selectedCustomFieldsDetails.map((field) => (
                    <div
                      key={field.id}
                      className="flex items-center justify-between bg-blue-50 px-3 py-2 rounded border border-blue-300 text-blue-800 text-sm"
                    >
                      <span>{field.name}</span>
                      <button
                        type="button"
                        onClick={() =>
                          setSelectedCustomFields((prev) =>
                            prev.filter((id) => id !== String(field.id))
                          )
                        }
                        title="Remove field"
                        className="text-red-600 hover:text-red-800"
                        aria-label={`Remove ${field.name}`}
                      >
                        <Minus size={18} />
                      </button>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No fields selected.</p>
                )}
              </div>
            </section>

            {/* Add New Custom Fields */}
            <section
              className="border rounded-lg p-0 flex flex-col h-[400px]"
              aria-label="Add new custom fields"
            >
              {/* Static Heading + Search */}
              <div className="bg-white p-4 border-b flex flex-col gap-1">
                <h3 className="text-lg font-semibold mb-2">
                  ➕ Add New Custom Fields
                </h3>
                <input
                  type="text"
                  placeholder="Search fields..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 border rounded text-sm"
                  aria-label="Search custom fields"
                />
              </div>

              {/* Scrollable Field List */}
              <div className="overflow-y-auto p-4 space-y-2 flex-1">
                {filteredCustomFields.length > 0 ? (
                  filteredCustomFields.map((field) => (
                    <div
                      key={field.id}
                      className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded border text-sm"
                    >
                      <button
                        type="button"
                        onClick={() =>
                          setSelectedCustomFields((prev) => [
                            ...prev,
                            String(field.id),
                          ])
                        }
                        title="Add field"
                        className="text-green-600 hover:text-green-800"
                        aria-label={`Add ${field.name}`}
                      >
                        <Plus size={18} />
                      </button>
                      <span>{field.name}</span>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">
                    No matching custom fields.
                  </p>
                )}
              </div>
            </section>
          </div>

          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <p className="text-sm text-gray-600">
                Can't find a custom field?{" "}
              </p>
              <button
                type="button"
                onClick={handleNavigateToAddCustomFields}
                className="inline-flex items-center gap-1 rounded bg-primary px-3 py-1 text-white hover:bg-primary/90 transition-colors cursor-pointer"
                aria-label="Navigate to Add Custom Fields page"
              >
                <Plus size={16} />
                Add Custom Fields
              </button>
            </div>
            <Button
              type="submit"
              className="px-6"
              disabled={!hasChanges}
            >
              Save Fields
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
