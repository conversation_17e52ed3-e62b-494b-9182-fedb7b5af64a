import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { toast } from "sonner";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { formSubmit } from "@/lib/helpers";
import { client_routes, employee_routes, setup_routes } from "@/lib/routePath";
import { useRouter } from "next/navigation";
import { useState } from "react";

export function ClientCarrierImport() {
  const [openTab, setOpenTab] = useState(false);
  const [errors, setErrors] = useState<any[]>([]);
  const router = useRouter();

  const handleExcelUpload = async (e: any) => {
    const file = e.target.files[0];

    if (!file) {
      toast.error("Please select an Excel file to upload");
      return;
    }

    const formData = new FormData();
    formData.append("file", file);

    try {
      const response = await fetch(setup_routes.EXCEL_SETUP, {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        const responseErrors = Array.isArray(data.errors) ? data.errors : [];
        setErrors(responseErrors); // Set errors to state to display on frontend

        if (data.successCount > 0) {
          toast.success(
            `${data.successCount} new client carrier(s) added successfully!`
          );
        }

        // If no errors and successful imports, close the modal and refresh the page
        if (responseErrors.length === 0 && data.successCount > 0) {
          setOpenTab(false);
          router.refresh();
        }
      } else {
        const data = await response.json();
        toast.error(data.message || "Failed to upload Excel file");
      }
      router.refresh();
    } catch (error) {
      console.error("Error importing Excel:", error);
      toast.error("Error importing Excel file");
    }
  };

  return (
    <Sheet open={openTab} onOpenChange={setOpenTab}>
      <SheetTrigger asChild>
        <Button className="mr-2 " onClick={() => setOpenTab(true)}>
          Import
        </Button>
      </SheetTrigger>
      <SheetContent className="overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Import Client Carrier</SheetTitle>
          <SheetDescription></SheetDescription>
        </SheetHeader>

        {/* File Input */}
        <input
          type="file"
          name="file"
          accept=".xlsx,.xls"
          id="excel-upload"
          className="w-96 mb-2"
          hidden
          onChange={handleExcelUpload} // Correctly bound the event
        />
        {/* Label to trigger file input */}
        <Button
          onClick={() => document.getElementById("excel-upload")?.click()}
        >
          Select Excel File
        </Button>

        {/* Display Errors */}
        <div className="text-red-500 text-sm mt-2">
          {Array.isArray(errors) &&
            errors.map((error, index) => (
              <p key={index} className="mt-2 bg-red-200 rounded-md p-2">
                {error} {/* Display each error message */}
              </p>
            ))}
        </div>
      </SheetContent>
    </Sheet>
  );
}
