import { Response } from "express";
import { updateTransaction } from "./utility";
import { checkUserPermission } from "../../../utils/permissions";

export const updateRolesPermission = async (req, res: Response) => {
  const { name, permission } = req.body;
  const role_id = Number(req.params.id);
  //  (role_id)
  const hasPermission = await checkUserPermission({
    req: req,
    res: res as Response,
    action: "USER MANAGEMENT",
    permissiontype: "ROLE MANAGEMENT",
  });
  if (hasPermission) {
    await updateTransaction({
      model: "Roles",
      fieldName: "id",
      id: role_id,
      data: { name, permission },
      res,
      req,
      logging_relationship: "rolesRole_id",
      successMessage: "Role updated successfully",
    });
  }
};
