model WorkReport {
  id                Int         @id @default(autoincrement())
  date              DateTime    @db.Timestamptz()
  user_id           Int
  user              User?       @relation(fields: [user_id], references: [id], onDelete: Cascade)
  client_id         Int?
  client            Client?     @relation(fields: [client_id], references: [id], onDelete: Cascade)
  carrier_id        Int?
  carrier           Carrier?    @relation(fields: [carrier_id], references: [id], onDelete: Cascade)
  work_type_id      Int?
  work_type         WorkType?   @relation(fields: [work_type_id], references: [id], onDelete: Cascade)
  work_status       WorkStatus?
  task_type         TaskType?
  planning_nummbers String?     @db.VarChar()
  expected_time     DateTime?
  actual_number     Int?
  start_time        DateTime    @db.Timestamptz()
  finish_time       DateTime?   @db.Timestamptz()
  time_spent        Decimal?    @db.Decimal(10, 2)
  pause             DateTime[]
  resume            DateTime[]
  category_id       Int?
  category          Category?   @relation(fields: [category_id], references: [id], onDelete: Cascade)
  notes             String?     @db.<PERSON>ar<PERSON><PERSON>()
  switch_type       Switchtype?
  created_at        DateTime    @default(now())
  updated_at        DateTime    @default(now()) @updatedAt

  @@index([user_id, work_status])
}