import { handleError } from "../../../utils/helpers";
import { getVisibility } from "../visibilty/visibiltyHelper";

export const viewDailyPlanning = async (req, res) => {
  try {
    const data = await prisma.dailyPlanning.findMany({
      include: {
        client: true,
        user: true,
      },
      orderBy: { id: "desc" },
    });
    if (data) {
      return res.status(200).json(data);
    }
    return res.status(400).json([]);
  } catch (error) {
    return handleError(res, error);
  }
};

export const viewSpecificDailyPlanning = async (req, res) => {
  try {
  
    const userId = req.user_id;
    const { page = "1", pageSize = "10", search = "", fDate, tDate } = req.query;

    const pageNumber = Number(page);
    const pageSizeNumber = Number(pageSize);

    if (isNaN(pageNumber) || isNaN(pageSizeNumber)) {
      return res.status(400).json({ message: "Invalid pagination parameters" });
    }

    const from = fDate ? new Date(fDate) : null;
    const to = tDate ? new Date(tDate) : null;

    if (to) to.setHours(23, 59, 59, 999); // Ensure `toDate` covers the full day

    const user_ids = await getVisibility(userId, "DailyPlanning");

    if (!Array.isArray(user_ids)) {
      return res.status(400).json({ message: "Invalid user_ids format" });
    }

    const userIdArray = [...new Set(user_ids.map((u) => u.user_id))];

    const whereClause : any = {
      user_id: { in: userIdArray },
      ...(search ? { client: { client_name: { contains: search, mode: "insensitive" } } } : {}),
      ...(from && to ? { daily_planning_date: { gte: from, lte: to } } : {}),
    };

    const totalRecords = await prisma.dailyPlanning.count({ where: whereClause });

    const data = await prisma.dailyPlanning.findMany({
      where: whereClause,
      orderBy: { id: "desc" },
      skip: (pageNumber - 1) * pageSizeNumber,
      take: pageSizeNumber,
      select: {
        id: true,
        corporation_id: true,
        corporation: true,
        user_id: true,
        user: true,
        daily_planning_date: true,
        client_id: true,
        client: true,
      },
    });

    return res.status(200).json({
      data,
      pagination: {
        totalRecords,
        totalPages: Math.max(1, Math.ceil(totalRecords / pageSizeNumber)), // Ensure at least 1 page
        currentPage: pageNumber,
        pageSize: pageSizeNumber,
      },
    });

  } catch (error) {
    console.error("Error fetching daily planning:", error);
    return handleError(res, error);
  }
};


export const viewDailyPlanningById = async (req, res) => {
  try {
    const { id } = req.params;
    //  (id, "id");
    const data = await prisma.dailyPlanning.findUnique({
      where: {
        id: Number(id),
      },
      include: {
        // DailyPlanningDetails: true,
        client: true,
      },
    });
    //  (data, "data");
    if (data) {
      return res.status(200).json(data);
    }
    return res.status(400).json([]);
  } catch (error) {
    console.log(error);
    return handleError(res, error);
  }
};
