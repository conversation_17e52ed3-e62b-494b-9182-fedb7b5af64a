"use client";
import DataTable from "@/app/_component/DataTable";
import React from "react";
import { Category, Column } from "./Column";


const ViewCategory = ({ data,permissions }: { data: Category[]; permissions: string[] }) => {
  return (
    <div className="w-full">
      <DataTable
        data={data}
        columns={Column(permissions)}
        // filter
        // filter_column="name"
        showColDropDowns
        showPageEntries
        // filter2
        // filter_column2="register1"
        className="w-full"
      />
    </div>
  );
};

export default ViewCategory;
