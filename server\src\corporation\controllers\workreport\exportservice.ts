import { handleError } from "../../../utils/helpers";
import xlsx from "xlsx";

export const exportWorkReportToExcel = async (req, res) => {
  try {
    const {
      fDate,
      tDate,
      Client,
      Carrier,
      Username,
      Work,
      Category,
      Type,
      Workstatus,
      ActualNumber,
      Notes,
      SwitchType
    } = req.query;

    const taskTypeMap = {
      BACKLOG: "BACKLOG",
      REGULAR: "REGULAR",
    };

    const from = fDate ? new Date(fDate) : null;
    const to = tDate ? new Date(tDate) : null;
    if (to) {
      to.setHours(23, 59, 59, 999);
    }

    const whereClause = { AND: [] };
    if (from || to) {
      const dateCondition: any = { date: {} };
      if (from) dateCondition.date.gte = from;
      if (to) dateCondition.date.lte = to;
      whereClause.AND.push(dateCondition);
    }

    const searchConditions = [];

    if (Client) {
      const clientList = Client.split(',').map(item => item.trim());
    
      searchConditions.push({
        OR: clientList.map(clientName => ({
          client: {
            client_name: {
              contains: clientName,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (Carrier) {
      const carrierList = Carrier.split(',').map(item => item.trim());
      searchConditions.push({
        OR: carrierList.map(carrier => ({
        carrier: {
          name: { contains: carrier, mode: "insensitive" },
        },
      }))
    })
  }

    if (Username) {
      const userList = Username.split(',').map(item => item.trim());
      searchConditions.push({
        OR: userList.map(user => ({
        user: {
          username: { contains: user, mode: "insensitive" },
        },
      }))
    })
  }

    if (Work) {
      const workList = Work.split(',').map(item => item.trim());
      searchConditions.push({
        OR: workList.map(work => ({
        work_type: {
          work_type: { contains: work, mode: "insensitive" },
        },
      }))
    })
  }

    if (Category) {
      const categoryList = Category.split(',').map(item => item.trim());
      searchConditions.push({
        OR: categoryList.map(category => ({
        category: {
          category_name: { contains: category, mode: "insensitive" },
        },
      }))
    })
  }

  if (Type) {
    const typeList = Type.split(",").map((item) => item.trim());
    const normalizedType = Type.toUpperCase();
    searchConditions.push({
      OR: typeList.map((type) => ({
        task_type: {
          in: Object.values(taskTypeMap).filter((value) =>
            value.toLowerCase().includes(type.toLowerCase())
          ),
        },
      })),
    });
  }

     if(SwitchType) {
      const normalizedSwitchType = SwitchType.toUpperCase();
      const switchTypeMap = {
        INT: "INT",
        EXT: "EXT",
      };

      searchConditions.push({
        switch_type: {
          in: Object.values(switchTypeMap).filter((value) =>
            value.toLowerCase().includes(normalizedSwitchType.toLowerCase())
          ),
        },
      });
    }

    if (Workstatus) {
      const normalizedWorkStatus = Workstatus.toUpperCase();
      const workStatusMap = {
        STARTED: "STARTED",
        PAUSED: "PAUSED",
        RESUMED: "RESUMED",
        FINISHED: "FINISHED",
      };

      searchConditions.push({
        work_status: {
          in: Object.values(workStatusMap).filter((value) =>
            value.toLowerCase().includes(normalizedWorkStatus.toLowerCase())
          ),
        },
      });
    }
    if (ActualNumber) {
      const actualNumberList = ActualNumber.split(",")
        .map((item) => parseInt(item.trim(), 10))
        .filter((num) => !isNaN(num)); // Remove any invalid numbers
    
      if (actualNumberList.length > 0) {
        searchConditions.push({
          actual_number: {
            in: actualNumberList, // Matches any of the values
          },
        });
      }
    }

    if (Notes) {
      const notesList = Notes.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: notesList.map((note) => ({
        notes: { contains: note, mode: "insensitive" },
      }))
    })
  }

    whereClause.AND.push({ AND: searchConditions });

    // Fetch data from the database
    const data = await prisma.workReport.findMany({
      where: whereClause,
      include: {
        user: true,
        client: true,
        carrier: true,
        work_type: true,
        category: true,
      },
      orderBy: { id: "desc" },
    });

    const datalength = await prisma.workReport.count({ where: whereClause });

    // Format helpers
    const formatDate = (dateString) => {
      if (!dateString) return "N/A";
      const date = new Date(dateString);
      return `${String(date.getDate()).padStart(2, "0")}-${String(
        date.getMonth() + 1
      ).padStart(2, "0")}-${date.getFullYear()}`;
    };

    const formatTime = (timeString) => {
      if (timeString) {
        const date = new Date(timeString);
        if (!isNaN(date.getTime())) {
          return date.toLocaleTimeString("en-US", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: true,
          });
        }
      }
      return "N/A";
    };

    // Format duration in HH:mm:ss (hours, minutes, seconds)
    const formatDuration = (timeSpentInMinutes) => {
      if (timeSpentInMinutes == null || isNaN(timeSpentInMinutes))
        return "00:00:00"; // Return "00:00:00" if time is invalid

      const hours = Math.floor(timeSpentInMinutes / 60); // Calculate hours
      const minutes = Math.floor(timeSpentInMinutes % 60); // Calculate remaining minutes
      const seconds = Math.round((timeSpentInMinutes % 1) * 60); // Calculate remaining seconds

      return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`; // Format as HH:mm:ss
    };

    // Map and format data
    const formattedData = data.map((item) => ({
      date: formatDate(item.date),
      username: item.user?.username || "N/A",
      client_name: item.client?.client_name || "N/A",
      carrier_name: item.carrier?.name || "N/A",
      work_type: item.work_type?.work_type || "N/A",
      category_name: item.category?.category_name || "N/A",
      work_status: item.work_status || "N/A",
      switch_type: item.switch_type || "N/A",
      actual_number: item.actual_number || "N/A",
      start_time: formatTime(item.start_time),
      finish_time: formatTime(item.finish_time),
      time_spent: formatDuration(item.time_spent),
      notes: item.notes || "",
     
    }));

    // ✅ Calculate total time spent in minutes
    let totalTimeSpentMinutes = 0;

    data.forEach((item:any) => {
      if (item.time_spent && !isNaN(item.time_spent)) {
        totalTimeSpentMinutes += parseFloat(item.time_spent); // Add time spent
      }
    });

    // Format total time as HH:mm:ss
    const formatTotalTime = (minutes) => {
      const hours = Math.floor(minutes / 60); // Calculate total hours
      const mins = Math.floor(minutes % 60); // Calculate remaining minutes
      const secs = Math.round((minutes % 1) * 60); // Calculate remaining seconds
      return `${String(hours).padStart(2, "0")}:${String(mins).padStart(2, "0")}:${String(secs).padStart(2, "0")}`; // Return formatted time as HH:mm:ss
    };

    const totalTimeFormatted = formatTotalTime(totalTimeSpentMinutes);

    // ✅ Add final row for total time spent
    formattedData.push({
      date: "",
      username: "",
      client_name: "",
      carrier_name: "",
      work_type: "",
      category_name: "",
      work_status: "",
      switch_type: "",
      actual_number: "",
      start_time: "",
      finish_time: "Total Time Spent:",
      time_spent: totalTimeFormatted, // Total time formatted as HH:mm:ss
      notes: "",
      
    });

    // Export to Excel
    const ws = xlsx.utils.json_to_sheet(formattedData);
    const wb = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(wb, ws, "Work Report");

    const excelBuffer = xlsx.write(wb, { bookType: "xlsx", type: "buffer" });

    if (data.length > 0) {
      res.setHeader(
        "Content-Disposition",
        "attachment; filename=Work_Report.xlsx"
      );
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      return res.send(excelBuffer);
    }

    return res.status(200).json({ data: [], datalength });
  } catch (error) {
    console.error("Error exporting work reports:", error);
    return handleError(res, error);
  }
};