import { Router } from "express";
import { authenticate } from "../../../middleware/authentication";
import { checkPermissionMiddleware } from "../../../middleware/checkPermission";
import { createCategory } from "../../controllers/category/create";
import { viewCategory } from "../../controllers/category/view";
import { updateCategory } from "../../controllers/category/update";
import { deleteCategory } from "../../controllers/category/delete";

const router = Router();

router.post(
  "/create-category",
  authenticate,
  checkPermissionMiddleware("CATEGORY MANAGEMENT", "create-category"),
  createCategory
);
router.get(
  "/get-all-category",
  authenticate,
  checkPermissionMiddleware("CATEGORY MANAGEMENT", "view-category"),
  viewCategory
);
router.put(
  "/update-category/:id",
  authenticate,
  checkPermissionMiddleware("CATEGORY MANAGEMENT", "update-category"),
  updateCategory
);
router.delete(
  "/delete-category/:id",
  authenticate,
  checkPermissionMiddleware("CATEGORY MANAGEMENT", "delete-category"),
  deleteCategory
);

export default router;
