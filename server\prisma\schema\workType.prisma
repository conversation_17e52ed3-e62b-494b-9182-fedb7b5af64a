model WorkType {
  id                              Int          @id @default(autoincrement())
  work_type                       String       @db.VarChar()
  created_at                      DateTime     @default(now())
  updated_at                      DateTime     @default(now()) @updatedAt
  is_work_carrier_specific        <PERSON><PERSON><PERSON>      @default(true)
  does_it_require_planning_number <PERSON><PERSON><PERSON>      @default(true)
  is_backlog_regular_required     <PERSON><PERSON>an      @default(true)
  WorkReport                      WorkReport[]
  category_id                     Int?
  category                        Category?    @relation(fields: [category_id], references: [id], onDelete: Cascade)
}