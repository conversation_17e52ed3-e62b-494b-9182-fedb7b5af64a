import {
  B<PERSON><PERSON>rum<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import React from "react";
interface BreadcrumbProps {
  link: string;
  name: string;
  link1?: string;
  name1?: string;
}

const BreadCrumbs = ({
  breadcrumblist,
}: {
  breadcrumblist: BreadcrumbProps[];
}) => {
  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumblist.map((item: any, index: number) => {
          return (
            <React.Fragment key={index}>
              <BreadcrumbItem key={index}>
                <BreadcrumbLink
                  className=" mt-1 text-sm  "
                  href={item.link}
                >
                  {item.name}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem key={index}>
                <BreadcrumbLink
                  className=" mt-1 text-sm  "
                  href={item.link1}
                >
                  {item.name1}
                </BreadcrumbLink>
              </BreadcrumbItem>
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadCrumbs;
