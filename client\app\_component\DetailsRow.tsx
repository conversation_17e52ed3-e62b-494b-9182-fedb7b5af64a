import { BiSolidDetail } from "react-icons/bi";
import OpenDialogBox from "./OpenDialogBox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@/components/ui/table";
import React from "react";

interface Detail {
  label: string;
  value: string | number | undefined | any[];
}

interface DetailsProps {
  details: Detail[];
  heading?: string;
}

const DetailsRow: React.FC<DetailsProps> = ({ details, heading }) => {
  return (
    
    <OpenDialogBox
      description=""
      triggerButton={<BiSolidDetail className="w-5 h-5 text-gray-500" />}
      heading={`${heading ? heading : "More Details"} `}
      contentClassName="max-w-5xl"
    >
      <>
        <Table>
          <TableBody>
            {details?.map((detail, index) => {
              return (
                <React.Fragment key={index}>
                  <TableRow className="py-1 ">
                    <TableHead className="text-sm h-6 py-2 w-[33%]">
                      {detail?.label}
                    </TableHead>

                    {Array.isArray(detail?.value) ? (
                      <TableCell className="text-sm py-1">
                        {detail?.value?.map((item, i) => (
                          <div key={i}>
                            {Object.entries(item).map(([key, value]) => (
                              <span key={key} className="">
                                <span className="font-semibold text-gray-800 tracking-wider uppercase">
                                  {`${key}:`}
                                </span>
                                {` ${value || "--"}`},{" "}
                              </span>
                            ))}
                          </div>
                        ))}
                      </TableCell>
                    ) : (
                      <TableCell className="text-sm py-1">
                        {detail?.value ? detail?.value : "-"}
                      </TableCell>
                    )}
                  </TableRow>
                </React.Fragment>
              );
            })}
          </TableBody>
        </Table>
      </>
    </OpenDialogBox>
  );
};

export default DetailsRow;
