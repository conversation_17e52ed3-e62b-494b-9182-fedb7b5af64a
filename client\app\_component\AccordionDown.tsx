import React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";

interface AccordionDownProps {
  open: boolean;
  routes: {
    label: string;
    path: string;
  }[];
  icon: React.ReactNode;
  mainLabel: string;
}

const AccordionDown = ({
  open,
  routes,
  icon,
  mainLabel,
}: AccordionDownProps) => {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <>
      <Accordion
        type="single"
        collapsible
        className="border-b-0 border-none mb-2.5"
      >
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="py-0 hover:no-underline font-normal ">
            <div
              className={`hover:text-main-color  ${
                open
                  ? "flex items-center justify-center gap-2 cursor-pointer px-3"
                  : "w-full flex flex-col items-center space-y-3 pr-8"
              } `}
            >
              <div className="text-slate-200">{icon}</div>

              <p className="text-center text-sm capitalize text-slate-200 ">
                {" "}
                {mainLabel}
              </p>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <div
              className={`${
                open ? "px-3 space-y-2 mt-2" : "w-full space-y-2 mt-2 px-1"
              }`}
            >
              {routes.map((item, index) => {
                return (
                  <p
                    key={index}
                    className={`${
                      open
                        ? "text-left  text-sm w-[76%] ml-auto"
                        : "text-center text-xs"
                    }  cursor-pointer hover:text-main-color text-slate-200`}
                    // onClick={() => router.push(item.path)}
                  >
                    <Link href={item.path}>{item.label}</Link>
                  </p>
                );
              })}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </>
  );
};

export default AccordionDown;
