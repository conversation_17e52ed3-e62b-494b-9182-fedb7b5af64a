"use client";
import { location_api } from "@/lib/routePath";
import { useEffect, useState } from "react";

export function useLocation(api: typeof location_api) {
  const [countries, setCountries] = useState<any[]>([]);
  const [states, setStates] = useState<any[]>([]);
  const [cities, setCities] = useState<any[]>([]);

  useEffect(() => {
    async function fetchCountries() {
      try {
        const res = await fetch(api.GET_COUNTRY);
        const data = await res.json();
        setCountries(data);
      } catch (error) {
        console.error("Error fetching countries:", error);
      }
    }
    fetchCountries();
  }, [api.GET_COUNTRY]);

  async function fetchStates(countryName: string) {
    try {
      const res = await fetch(`${api.GET_STATE}/${countryName}`);
      const data = await res.json();
      setStates(data);
    } catch (error) {
      console.error("Error fetching states:", error);
    }
  }

  async function fetchCities(stateName: string) {
    try {
      const res = await fetch(`${api.GET_CITY}/${stateName}`);
      const data = await res.json();
      setCities(data);
    } catch (error) {
      console.error("Error fetching cities:", error);
    }
  }

  return { countries, states, cities, fetchStates, fetchCities };
}
