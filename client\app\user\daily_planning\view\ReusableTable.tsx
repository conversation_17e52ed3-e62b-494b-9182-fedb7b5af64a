import React, { useState, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableFooter,
} from "@/components/ui/table";
import { ChevronUp, ChevronDown } from "lucide-react";

interface TableColumn {
  key: string;
  label: string | React.ReactNode;
  sortable?: boolean;
  align?: 'left' | 'right' | 'center';
  render?: (value: any, row: any) => React.ReactNode;
}

interface ReusableTableProps {
  data: any[];
  columns: TableColumn[];
  defaultSort?: {
    column: string;
    direction: 'asc' | 'desc';
  };
  showFooter?: boolean;
  footerData?: {
    label: string;
    values: Record<string, any>;
  };
}

const ReusableTable = ({
  data,
  columns,
  defaultSort = { column: 'name', direction: 'asc' },
  showFooter = false,
  footerData,
}: ReusableTableProps) => {
  const [sortColumn, setSortColumn] = useState(defaultSort.column);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">(defaultSort.direction);

  const sortedData = useMemo(() => {
    return [...data].sort((a, b) => {
      const aValue = a[sortColumn];
      const bValue = b[sortColumn];
      
      if (typeof aValue === 'string') {
        return sortDirection === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      return sortDirection === "asc"
        ? aValue - bValue
        : bValue - aValue;
    });
  }, [data, sortColumn, sortDirection]);

  const handleSort = (column: string) => {
    if (column === sortColumn) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  return (
    <Table>
      <TableHeader>
        <TableRow className="bg-muted/50">
          {columns.map((column) => (
            <TableHead
              key={column.key}
              className={`${column.sortable ? 'cursor-pointer' : ''} ${
                column.align ? `text-${column.align}` : ''
              }`}
              onClick={() => column.sortable && handleSort(column.key)}
            >
              {column.label}
              {column.sortable && sortColumn === column.key && (
                sortDirection === "asc" ? (
                  <ChevronUp className="inline ml-2 h-4 w-4" />
                ) : (
                  <ChevronDown className="inline ml-2 h-4 w-4" />
                )
              )}
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>

      <TableBody>
        {sortedData.map((row: any, index: number) => (
          <TableRow key={index} className="hover:bg-muted/50 transition-colors">
            {columns.map((column) => (
              <TableCell
                key={column.key}
                className={column.align ? `text-${column.align}` : ''}
              >
                {column.render
                  ? column.render(row[column.key], row)
                  : row[column.key]}
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>

      {showFooter && footerData && (
        <TableFooter>
          <TableRow className="bg-muted/50">
            <TableCell className="font-bold">{footerData.label}</TableCell>
            {columns.slice(1).map((column) => (
              <TableCell
                key={column.key}
                className={`${column.align ? `text-${column.align}` : ''} font-bold`}
              >
                {footerData.values[column.key] || ''}
              </TableCell>
            ))}
          </TableRow>
        </TableFooter>
      )}
    </Table>
  );
};

export default ReusableTable;