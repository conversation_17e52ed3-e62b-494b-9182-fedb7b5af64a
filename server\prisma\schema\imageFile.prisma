model ImageFile {
  id             Int          @id @default(autoincrement())
  size           String
  name           String
  path           String
  type           String
  created_at     DateTime     @default(now()) @db.Timestamptz()
  updated_at     DateTime     @updatedAt @db.Timestamptz()
  user_id        Int
  user           User         @relation(fields: [user_id], references: [id])
  corporation_id Int?
  corporation    Corporation? @relation(fields: [corporation_id], references: [corporation_id])
}
