import { Button } from "@/components/ui/button";
import React from "react";
import { FaCopy } from "react-icons/fa";
interface CopyProps {
  oncopy: () => void;
}
const CopyButton: React.FC<CopyProps> = ({ oncopy }: any) => {
  return (
    <>
      <Button
        type="button"
        className="text-sm w-[12%] h-6
           bg-white ring-1 
           ring-gray-400 hover:ring-main-color rounded-md flex text-gray-400
            hover:bg-white hover:text-main-color hover:scale-105"
        onChange={oncopy}
      >
        <FaCopy className="w-4 h-4 mr-2" />
        To Billed
      </Button>
    </>
  );
};

export default CopyButton;
