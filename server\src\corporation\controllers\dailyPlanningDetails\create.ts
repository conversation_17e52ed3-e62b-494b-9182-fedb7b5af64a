import xlsx from "xlsx";
import fs from "fs";
import { Carrier } from "../carrier/clientCarrier";

type DailyPlanningType = "INVOICE_ENTRY_STATUS" | "STATEMENT_TABLE";

export const createDailyPlanningDetails = async (req, res) => {
  const { corporation_id } = req;
  const { type, entries } = req.body;
  const { id: daily_planning_id } = req.params;

  function parseValidDate(date) {
    if (!date) return null;
    const parsed = new Date(date);
    return isNaN(parsed.getTime()) ? null : parsed;
  }

  try {
    if (!type) return res.status(400).json({ error: "Invalid 'type' field." });
    if (!Array.isArray(entries))
      return res.status(400).json({ error: "Invalid 'entries'" });

    let Dpt = await prisma.dailyPlanningByType.findFirst({
      where: { daily_planning_id: Number(daily_planning_id), type },
    });

    if (!Dpt) {
      Dpt = await prisma.dailyPlanningByType.create({
        data: { daily_planning_id: Number(daily_planning_id), type },
      });
    }

    const dpt_id = Dpt.id;

    const clientData = await prisma.dailyPlanning.findUnique({
      where: { id: Number(daily_planning_id) },
      select: {
        client: {
          select: {
            id: true,
            client_name: true,
          },
        },
        daily_planning_date: true,
      },
    });

    const planningDate = clientData.daily_planning_date;
    const clientId = clientData.client.id;

    const rawData = [];
    const validationErrors = [];

    for (const entry of entries) {
      const carrierId = parseInt(entry.carrier_id, 10);
      const old = parseInt(entry.old, 10) || 0;
      const newCount = parseInt(entry.new, 10) || 0;
      const invoice_entry_total =
        old + newCount || parseInt(entry.total, 10) || null;
      const receiveDateParsed = parseValidDate(entry.receive_date);

      if (receiveDateParsed && receiveDateParsed > planningDate) {
        validationErrors.push(
          `Receive date (${
            receiveDateParsed.toISOString().split("T")[0]
          }) cannot be after planning date (${
            planningDate.toISOString().split("T")[0]
          })`
        );
        continue;
      }

      if (type === "STATEMENT_TABLE") {
        const existingCarrierEntries =
          await prisma.dailyPlanningDetails.findMany({
            where: {
              daily_planning_id: Number(daily_planning_id),
              daily_planning_type_id: dpt_id,
              carrier_id: carrierId,
              send_date: null,
            },
          });

        const normalize = (val) => (val || "").toUpperCase().trim();
        const isDuplicate = existingCarrierEntries.some(
          (e) =>
            e.currency === (entry.currency || null) &&
            normalize(e.shipping_type) === normalize(entry.shipping_type) &&
            normalize(e.division) === normalize(entry.division)
        );

        if (isDuplicate) {
          const carrier = await prisma.carrier.findUnique({
            where: { id: carrierId },
            select: { name: true },
          });
          const carrierName = carrier?.name || `ID ${carrierId}`;
          validationErrors.push(
            `Duplicate entry found for carrier ${carrierName}.`
          );
          continue;
        }

        const previousDate = new Date(planningDate);
        previousDate.setDate(previousDate.getDate() - 1);

        const previousTypeEntry = await prisma.dailyPlanningByType.findFirst({
          where: {
            type: "STATEMENT_TABLE",
            daily_planning: {
              client_id: clientId,
              daily_planning_date: previousDate,
            },
            DailyPlanningDetails: {
              some: { carrier_id: carrierId },
            },
          },
          include: {
            DailyPlanningDetails: true,
            daily_planning: { select: { daily_planning_date: true } },
          },
        });

        if (previousTypeEntry) {
          const normalize = (val) => (val || "").toUpperCase().trim();
          const similarCarrierDetail =
            previousTypeEntry.DailyPlanningDetails.find(
              (d) => d.carrier_id === carrierId && d.send_date === null
            );

          if (similarCarrierDetail) {
            const currencySame =
              (similarCarrierDetail.currency || null) ===
              (entry.currency || null);
            const shippingSame =
              normalize(similarCarrierDetail.shipping_type) ===
              normalize(entry.shipping_type);
            const divisionSame =
              normalize(similarCarrierDetail.division) ===
              normalize(entry.division);

            if (currencySame && shippingSame && divisionSame) {
              const carrier = await prisma.carrier.findUnique({
                where: { id: carrierId },
                select: { name: true },
              });
              const carrierName = carrier?.name || `ID ${carrierId}`;
              validationErrors.push(
                `Previous statement for carrier ${carrierName} is incomplete `
              );
              continue;
            }
          }
        }
      }

      rawData.push({
        daily_planning_id: Number(daily_planning_id),
        carrier_id: carrierId,
        daily_planning_type_id: dpt_id,
        invoice_entry_total,
        receive_by: entry.receive_by || null,
        receive_date: receiveDateParsed,
        currency: entry.currency || null,
        shipping_type: entry.shipping_type
          ? entry.shipping_type.toUpperCase().trim()
          : null,
        division: entry.division ? entry.division.toUpperCase().trim() : null,
        notes: entry.notes || null,
        created_at: new Date(),
        updated_at: new Date(),
        source: "current",
        ute: parseInt(entry.ute, 10) || null,
        old: old || null,
        new: newCount || null,
        no_invoices: parseInt(entry.no_invoices, 10) || null,
        amount_of_invoice: parseFloat(entry.amount_of_invoice) || null,
      });
    }

    if (validationErrors.length > 0) {
      return res.status(400).json({ errors: validationErrors });
    }

    const carrierIds = rawData.map((e) => e.carrier_id);
    // await prisma.dailyPlanningDetails.deleteMany({
    //   where: {
    //     daily_planning_id: Number(daily_planning_id),
    //     carrier_id: { in: carrierIds },
    //     NOT: {
    //       send_date: null,
    //     },
    //   },
    // });

    const result = await prisma.dailyPlanningDetails.createMany({
      data: rawData,
    });

    return res.status(201).json({
      success: true,
      message: `${result.count} records created successfully.`,
    });
  } catch (err) {
    console.error(err);
    return res
      .status(500)
      .json({ error: err.message || "Internal Server Error" });
  }
};

export const excelDailyPlanningDetails = async (req, res) => {
  try {
    const userId = req.user_id;
    function parseExcelDate(dateValue) {
      if (!dateValue) return null;

      if (typeof dateValue === "number") {
        return excelSerialDateToJSDate(dateValue);
      }

      const dateStr = String(dateValue);

      const yyyymmddMatch = dateStr.match(/^(\d{4})-(\d{2})-(\d{2})$/);
      if (yyyymmddMatch) {
        const [_, year, month, day] = yyyymmddMatch;

        const date = new Date(
          Date.UTC(parseInt(year), parseInt(month) - 1, parseInt(day))
        );
        return date;
      }

      return null;
    }

    function excelSerialDateToJSDate(serial) {
      if (!serial) return null;
      const utc_days = Math.floor(serial - 25569);
      const utc_value = utc_days * 86400;
      const date_info = new Date(utc_value * 1000);
      return new Date(
        Date.UTC(
          date_info.getFullYear(),
          date_info.getMonth(),
          date_info.getDate()
        )
      );
    }

    const { type } = req.body;
    const { id: daily_planning_id } = req.params;
    const username = req.body.username;

    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    if (!type) {
      return res.status(400).json({ error: "Invalid 'type' field." });
    }

    let Dpt = await prisma.dailyPlanningByType.findFirst({
      where: {
        daily_planning_id: Number(daily_planning_id),
        type: type,
      },
    });

    if (!Dpt) {
      Dpt = await prisma.dailyPlanningByType.create({
        data: {
          daily_planning_id: Number(daily_planning_id),
          type: type,
        },
      });
    }

    const client = await prisma.dailyPlanning.findUnique({
      where: {
        id: Number(daily_planning_id),
      },
      select: {
        client: {
          include: {
            ClientCarrier: true,
          },
        },
        daily_planning_date: true,
      },
    });

    const dpt_id = Dpt.id;

    const allowedExtensions = [".xls", ".xlsx"];
    const fileExtension = req.file.originalname.split(".").pop();
    if (!allowedExtensions.includes(`.${fileExtension}`)) {
      return res
        .status(400)
        .json({ error: "Invalid file type. Only Excel files are allowed." });
    }

    const workbook = xlsx.readFile(req.file.path);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const data = xlsx.utils.sheet_to_json(worksheet, { header: 1 });

    const headers: any = data[0];
    const rows: any = data.slice(1);
    const errors = new Set();

    if (type === "INVOICE_ENTRY_STATUS") {
      const requiredHeaders = [
        "daily_planning_type",
        "carrier_name",
        "old",
        "new",
        "ute",
      ];

      const isCorrectOrder = requiredHeaders.every(
        (header, index) => headers[index] === header
      );

      if (!isCorrectOrder) {
        errors.add(
          `Headers must be in this exact order: ${requiredHeaders.join(", ")}`
        );
        return res.status(200).json({
          errors: Array.from(errors),
        });
      }

      if (rows.length > 0) {
        const firstRow = rows[0];
        if (firstRow[0] !== "INVOICE_ENTRY_STATUS") {
          errors.add(
            `First row must have "INVOICE_ENTRY_STATUS" in the daily_planning_type column`
          );
          return res.status(200).json({
            errors: Array.from(errors),
          });
        }
      }
    }

    if (type === "STATEMENT_TABLE") {
      const requiredHeaders = [
        "daily_planning_type",
        "carrier_name",
        "shipping_type",
        "division",
        "receive_date",
        "reconcile_date",
        "review_date",
        "send_date",
        "no_invoices",
        "amount_of_invoice",
        "currency",
        "notes",
      ];

      const isCorrectOrder = requiredHeaders.every(
        (header, index) => headers[index] === header
      );

      if (!isCorrectOrder) {
        errors.add(
          `Headers must be in this exact order: ${requiredHeaders.join(", ")}`
        );
        return res.status(200).json({
          errors: Array.from(errors),
        });
      }

      if (rows.length > 0) {
        const firstRow = rows[0];
        if (firstRow[0] !== "STATEMENT_TABLE") {
          errors.add(
            `First row must have "STATEMENT_TABLE" in the daily_planning_type column`
          );
          return res.status(200).json({
            errors: Array.from(errors),
          });
        }
      }
    }

    const validPlanningTypes = new Set([
      "INVOICE_ENTRY_STATUS",
      "STATEMENT_TABLE",
    ]);

    const hasMixedTypes = rows.some((row: any) => {
      const planningTypeName = row[headers.indexOf("daily_planning_type")];
      return planningTypeName !== type;
    });

    if (hasMixedTypes) {
      errors.add(
        ` All data must have the same daily_planning_type as the import type (${type}).`
      );
      return res.status(200).json({
        errors: Array.from(errors),
      });
    }

    const planningDetails = await Promise.all(
      rows.map(async (row: any, index) => {
        const carrierName = row[headers.indexOf("carrier_name")];
        const planningTypeName = row[headers.indexOf("daily_planning_type")];
        const rowIndex = index + 2;

        if (!carrierName || !planningTypeName) {
          errors.add(` Missing carrier or planning type.`);
          return null;
        }

        if (!validPlanningTypes.has(planningTypeName)) {
          errors.add(
            ` Invalid planning type '${planningTypeName}'. Allowed values: ${Array.from(
              validPlanningTypes
            ).join(", ")}`
          );
          return null;
        }

        if (
          type === "INVOICE_ENTRY_STATUS" ||
          planningTypeName === "INVOICE_ENTRY_STATUS"
        ) {
          const old = row[headers.indexOf("old")];
          const newCount = row[headers.indexOf("new")];
          const ute = row[headers.indexOf("ute")];

          // Validate that old, new, and ute are numbers
          if (isNaN(old) || isNaN(newCount) || isNaN(ute)) {
            errors.add(
              ` For INVOICE_ENTRY_STATUS, old, new, and ute must be numbers.`
            );
            return null;
          }

          if (!old || !newCount || !ute) {
            errors.add(
              ` For INVOICE_ENTRY_STATUS, old, new, and ute fields are required.`
            );
            return null;
          }
        }

        if (
          type === "STATEMENT_TABLE" ||
          planningTypeName === "STATEMENT_TABLE"
        ) {
          const shippingType = row[headers.indexOf("shipping_type")];
          const receiveDate = row[headers.indexOf("receive_date")];
          const noInvoices = row[headers.indexOf("no_invoices")];
          const amountOfInvoice = row[headers.indexOf("amount_of_invoice")];
          const currency = row[headers.indexOf("currency")];

          // Validate required fields
          if (
            !shippingType ||
            !receiveDate ||
            !noInvoices ||
            !amountOfInvoice ||
            !currency
          ) {
            errors.add(
              ` For STATEMENT_TABLE, shipping_type, receive_date, no_invoices, amount_of_invoice, and currency fields are required.`
            );
            return null;
          }

          // Validate currency
          const validCurrencies = ["USD", "CAD", "KRW"];
          if (!validCurrencies.includes(currency)) {
            errors.add(
              ` Invalid currency '${currency}'. Allowed values: ${validCurrencies.join(
                ", "
              )}`
            );
            return null;
          }

          // Validate numbers
          if (isNaN(noInvoices) || isNaN(amountOfInvoice)) {
            errors.add(
              ` For STATEMENT_TABLE, no_invoices and amount_of_invoice must be numbers.`
            );
            return null;
          }

          const reviewDate = row[headers.indexOf("review_date")];
          const reconcileDate = row[headers.indexOf("reconcile_date")];
          const sendDate = row[headers.indexOf("send_date")];

          // Validate date dependencies
          if (reviewDate && !receiveDate) {
            errors.add(` review_date cannot be set without receive_date.`);
            return null;
          }

          if (reviewDate && !reconcileDate) {
            errors.add(` review_date cannot be set without reconcile_date.`);
            return null;
          }

          if (reconcileDate && !receiveDate) {
            errors.add(` reconcile_date cannot be set without receive_date.`);
            return null;
          }

          if (sendDate && !receiveDate) {
            errors.add(` send_date cannot be set without receive_date.`);
            return null;
          }

          if (sendDate && !reviewDate) {
            errors.add(` send_date cannot be set without review_date.`);
            return null;
          }

          if (sendDate && !reconcileDate) {
            errors.add(` send_date cannot be set without reconcile_date.`);
            return null;
          }
        }

        const carrier = await prisma.carrier.findFirst({
          where: { name: carrierName },
          select: { id: true },
        });

        if (!carrier) {
          errors.add(` Carrier '${carrierName}' not found.`);
          return null;
        }

        const checkClientCarrier = client.client.ClientCarrier.some(
          (item) => item.carrier_id === carrier?.id
        );

        if (!checkClientCarrier) {
          errors.add(
            ` Client-carrier relationship not found for '${carrierName}'.`
          );
          return null;
        }

        if (type === "STATEMENT_TABLE") {
          const existingCarrierEntries =
            await prisma.dailyPlanningDetails.findMany({
              where: {
                daily_planning_id: Number(daily_planning_id),
                daily_planning_type_id: dpt_id,
                carrier_id: carrier.id,
                send_date: null,
              },
            });

          const normalize = (val) =>
            typeof val === "string" ? val.toUpperCase().trim() : val ?? null;
          const rowCurrency = normalize(row[headers.indexOf("currency")]);
          const rowShippingType = normalize(
            row[headers.indexOf("shipping_type")]
          );
          const rowDivision = normalize(row[headers.indexOf("division")]);

          const isDuplicate = existingCarrierEntries.some((entry) => {
            const entryCurrency = normalize(entry.currency);
            const entryShippingType = normalize(entry.shipping_type);
            const entryDivision = normalize(entry.division);

            return (
              entryCurrency === rowCurrency &&
              entryShippingType === rowShippingType &&
              entryDivision === rowDivision
            );
          });

          if (isDuplicate) {
            const carrierID = await prisma.carrier.findUnique({
              where: { id: carrier.id },
              select: { name: true },
            });
            console.log("carrierID", carrierID);
            const carrierName = carrierID?.name || `ID ${carrier.id}`;
            errors.add(`Duplicate entry found for carrier ${carrierName}.`);
            return null;
          }

          const previousDate = new Date(client.daily_planning_date);
          previousDate.setDate(previousDate.getDate() - 1);

          const previousTypeEntry = await prisma.dailyPlanningByType.findFirst({
            where: {
              type: "STATEMENT_TABLE",
              daily_planning: {
                client_id: client.client.id,
                daily_planning_date: previousDate,
              },
              DailyPlanningDetails: {
                some: {
                  carrier_id: carrier.id,
                },
              },
            },
            include: {
              DailyPlanningDetails: true,
              daily_planning: {
                select: { daily_planning_date: true },
              },
            },
          });

          if (
            previousTypeEntry &&
            client.daily_planning_date >
              previousTypeEntry.daily_planning.daily_planning_date
          ) {
            const normalize = (val) => (val || "").toUpperCase().trim();
            const similarCarrierDetail =
              previousTypeEntry.DailyPlanningDetails.find(
                (detail) =>
                  detail.carrier_id === carrier.id && detail.send_date === null
              );

            if (similarCarrierDetail) {
              const currencySame =
                (similarCarrierDetail.currency || null) ===
                (normalize(row[headers.indexOf("currency")]) || null);
              const shippingSame =
                normalize(similarCarrierDetail.shipping_type) ===
                normalize(row[headers.indexOf("shipping_type")]);
              const divisionSame =
                normalize(similarCarrierDetail.division) ===
                normalize(row[headers.indexOf("division")]);

              if (currencySame && shippingSame && divisionSame) {
                const carrierId = await prisma.carrier.findUnique({
                  where: { id: carrier.id },
                  select: { name: true },
                });
                const carrierName = carrierId?.name || `ID ${carrier.id}`;
                errors.add(
                  `Previous statement for carrier ${carrierName} is incomplete `
                );
                return null;
              }
            }
          }
        }

        // const existing = await prisma.dailyPlanningDetails.findFirst({
        //   where: {
        //     daily_planning_id: Number(daily_planning_id),
        //     carrier_id: carrier.id,
        //     type: planningTypeName,
        //   },
        // });
        // if (existing) {
        //   errors.add(
        //     ` Carrier '${carrierName}' already exists for this planning date.`
        //   );
        //   return null;
        // }

        const receiveDateRaw = row[headers.indexOf("receive_date")];
        const receiveDate = parseExcelDate(receiveDateRaw);

        const reviewDateRaw = row[headers.indexOf("review_date")];
        const reviewDate = parseExcelDate(reviewDateRaw);

        const sendDateRaw = row[headers.indexOf("send_date")];
        const sendDate = parseExcelDate(sendDateRaw);

        const reconcileDateRaw = row[headers.indexOf("reconcile_date")];
        const reconcileDate = parseExcelDate(reconcileDateRaw);

        if (receiveDate && receiveDate > client.daily_planning_date) {
          errors.add(
            `Receive date cannot be after daily planning date (${
              client.daily_planning_date.toISOString().split("T")[0]
            })`
          );
          return null;
        }

        if (reconcileDate) {
          if (reconcileDate < receiveDate) {
            errors.add(
              `Reconcile date cannot be before receive date (${
                receiveDate.toISOString().split("T")[0]
              })`
            );
            return null;
          }
          if (reconcileDate > client.daily_planning_date) {
            errors.add(
              `Reconcile date cannot be after daily planning date (${
                client.daily_planning_date.toISOString().split("T")[0]
              })`
            );
            return null;
          }
        }

        if (reviewDate) {
          if (reviewDate < reconcileDate) {
            errors.add(
              `Review date cannot be before reconcile date (${
                reconcileDate.toISOString().split("T")[0]
              })`
            );
            return null;
          }
          if (reviewDate > client.daily_planning_date) {
            errors.add(
              `Review date cannot be after daily planning date (${
                client.daily_planning_date.toISOString().split("T")[0]
              })`
            );
            return null;
          }
        }

        if (sendDate) {
          if (sendDate < reviewDate) {
            errors.add(
              `Send date cannot be before review date (${
                reviewDate.toISOString().split("T")[0]
              })`
            );
            return null;
          }
          if (sendDate > client.daily_planning_date) {
            errors.add(
              `Send date cannot be after daily planning date (${
                client.daily_planning_date.toISOString().split("T")[0]
              })`
            );
            return null;
          }
        }

        const receive_by = receiveDate ? username : null;
        const reconcile_by = reconcileDate ? username : null;
        const review_by = reviewDate ? username : null;
        const send_by = sendDate ? username : null;

        if (receiveDateRaw && !receiveDate) {
          errors.add(
            `Invalid receive date format. Expected format: YYYY-MM-DD`
          );
          return null;
        }
        if (reviewDateRaw && !reviewDate) {
          errors.add(`Invalid review date format. Expected format: YYYY-MM-DD`);
          return null;
        }
        if (sendDateRaw && !sendDate) {
          errors.add(`Invalid send date format. Expected format: YYYY-MM-DD`);
          return null;
        }
        if (reconcileDateRaw && !reconcileDate) {
          errors.add(
            `Invalid reconcile date format. Expected format: YYYY-MM-DD`
          );
          return null;
        }

        const old = parseInt(row[headers.indexOf("old")]) || 0;
        const newCount = parseInt(row[headers.indexOf("new")]) || 0;
        const invoice_entry_total = old + newCount || null;

        return {
          carrier_id: carrier.id,
          daily_planning_type_id: dpt_id,
          daily_planning_id: Number(daily_planning_id),
          old: old || null,
          new: newCount || null,
          invoice_entry_total,
          shipping_type: row[headers.indexOf("shipping_type")]
            ? row[headers.indexOf("shipping_type")]?.toUpperCase().trim()
            : null,
          division: row[headers.indexOf("division")]
            ? row[headers.indexOf("division")]?.toUpperCase().trim()
            : null,
          receive_date: receiveDate,
          reconcile_date: reconcileDate,
          review_date: reviewDate,
          send_date: sendDate,
          receive_by: receive_by,
          reconcile_by: reconcile_by,
          review_by: review_by,
          send_by: send_by,
          no_invoices: parseInt(row[headers.indexOf("no_invoices")]) || null,
          amount_of_invoice:
            parseFloat(row[headers.indexOf("amount_of_invoice")]) || null,
          currency: row[headers.indexOf("currency")]
            ? row[headers.indexOf("currency")]?.toUpperCase().trim()
            : null,
          ute: parseInt(row[headers.indexOf("ute")]) || null,
          notes: row[headers.indexOf("notes")] || null,
          type: planningTypeName,
          source: "current",
          created_at: new Date(),
          updated_at: new Date(),
        };
      })
    );

    const validPlanningDetails = planningDetails.filter(
      (detail) => detail !== null
    );

    if (validPlanningDetails.length > 0) {
      await prisma.dailyPlanningDetails.createMany({
        data: validPlanningDetails,
      });
    }

    fs.unlink(req.file.path, (err) => {
      if (err) {
        console.error("Error deleting file:", err);
      } else {
        console.log("Uploaded file deleted successfully.");
      }
    });

    if (errors.size > 0) {
      res.status(200).json({
        errors: Array.from(errors),
      });
    } else {
      res.status(200).json({
        message: "Daily planning details imported successfully",
        recordsCreated: validPlanningDetails.length,
      });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Internal Server Error" });
  } finally {
    await prisma.$disconnect();
  }
};
