import { But<PERSON> } from "@/components/ui/button";
import React, { useState, ReactNode } from "react";
import { IoIosCopy } from "react-icons/io";

interface Tab {
  value: string;
  label: string;
  content: ReactNode;
}

interface TabsProps {
  tabs: Tab[];
}
const Tabs: React.FC<TabsProps> = ({ tabs }) => {
  const [activeTab, setActiveTab] = useState(tabs[0].value);

  return (
    <div className="w-full rounded-md  bg-white  ">
      <div className="flex justify-start h-full  w-full">
        <TabsList className="">
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.value}
              className="  text-sm   h-full rounded-t-md p-3 
               data-[state=active]:shadow-md 
             data-[state=active]:shadow- text-gray-500  hover:bg-white
              hover:text-black hover:shadow-md hover:shadow-main-color-foreground/5"
              value={tab.value}
              isActive={activeTab === tab.value}
              onClick={() => setActiveTab(tab.value)}
            >
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
      </div>

      {tabs.map((tab) => (
        <TabsContent
          className=" h-full "
          key={tab.value}
          value={tab.value}
          isActive={activeTab === tab.value}
        >
          {tab.content}
        </TabsContent>
      ))}
    </div>
  );
};

const TabsList: React.FC<{ children: ReactNode; className: any }> = ({
  children,
}) => <div>{children}</div>;

const TabsTrigger: React.FC<{
  value: string;
  isActive: boolean;
  onClick: () => void;
  className: string;
  children: ReactNode;
}> = ({ value, isActive, onClick, className, children }) => (
  <button
    type="button"
    className={`${className} 
    ${
      value === "planned" && isActive
        ? "active bg-main-color-foreground/5 text-gray-400  font-semibold "
        : "bg-white  text-gray-500  font-medium "
    } ${
      value === "billed" && isActive
        ? "bg-white text-gray-700 shadow-md shadow-main-color-foreground/5"
        : ""
    } hover:text-black hover:bg-white '} `}
    onClick={onClick}
  >
    {children}
  </button>
);

const TabsContent: React.FC<{
  value: string;
  isActive: boolean;
  children: ReactNode;
  className: any;
}> = ({ value, isActive, children }) => (
  <div
    className={`${isActive ? "block " : "hidden"} 
  }`}
  >
    {children}
  </div>
);

export default Tabs;
