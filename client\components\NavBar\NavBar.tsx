"use-client";
import React, { useEffect, useState } from "react";
import { But<PERSON> } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import DialogHeading from "@/app/_component/DialogHeading";
import Login from "@/app/(auth)/admin/Login";
import LoginUser from "@/app/user/(auth)/Login";

function NavBar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  // const [isUserPath, setIsUserPath] = useState(false);
  const [isUserPath, setIsUserPath] = useState<boolean | null>(null);
  const navItems = [
    { href: "#", text: "Home" },
    { href: "#", text: "About" },
    { href: "#", text: "Services" },
    { href: "#", text: "Contact" },
  ];
  // useEffect(() => {
  //   const path = window.location.pathname;
  //   if (path === "/user") {
  //     setIsUserPath(true);
  //   } else {
  //     setIsUserPath(false);
  //   }
  // }, []);

  useEffect(() => {
    const path = window.location.pathname;
    setIsUserPath(path === "/user"); // Update state based on path
  }, []);

  // Avoid rendering dependent on client-only state until after hydration
  if (isUserPath === null) {
    return null;
  }

  return (
    <>
      <nav className="bg-white dark:bg-gray-900 fixed w-full z-20 top-0 left-0 border-b border-gray-200 dark:border-gray-600">
        <div className="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4">
          <a
            href="/"
            className="flex items-center space-x-3 rtl:space-x-reverse"
          >
            <span className="self-center text-2xl font-semibold whitespace-nowrap dark:text-white text-primary">
              Oi360
            </span>
          </a>

          <div className="flex md:order-2 space-x-3 rtl:space-x-reverse">
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  type="button"
                  className="text-white bg-primary hover:bg-primary/90 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2"
                >
                  SIGN IN
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeading
                  title="SIGN IN"
                  description="Please enter your details to sign in"
                />
                {isUserPath ? <LoginUser /> : <Login />}
              </DialogContent>
            </Dialog>

            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              type="button"
              className="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600"
              aria-controls="navbar-sticky"
              aria-expanded={isMenuOpen}
            >
              <span className="sr-only">Open main menu</span>
              <svg
                className="w-5 h-5"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 17 14"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M1 1h15M1 7h15M1 13h15"
                />
              </svg>
            </button>
          </div>

          <div
            className={`${
              isMenuOpen ? "block" : "hidden"
            } items-center justify-between w-full md:flex md:w-auto md:order-1`}
            id="navbar-sticky"
          >
            <ul className="flex flex-col p-4 md:p-0 mt-4 font-medium border border-gray-100 rounded-lg bg-gray-50 md:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 md:bg-white dark:bg-gray-800 md:dark:bg-gray-900 dark:border-gray-700">
              {navItems.map((item) => (
                <li key={item.text}>
                  <a
                    href="#"
                    className="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-primary md:p-0 md:dark:hover:text-primary dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700"
                  >
                    {item.text}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </nav>
    </>
  );
}

export default NavBar;