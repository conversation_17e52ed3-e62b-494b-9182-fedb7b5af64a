"use client";
import FormInput from "@/app/_component/FormInput";
import FormRadio from "@/app/_component/FormRadio";
import FormTimePicker from "@/app/_component/FormTimePicker";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { formSubmit, getAllData } from "@/lib/helpers";
import { carrier_routes, workreport_routes } from "@/lib/routePath";
import { convertTimeToDate } from "@/lib/swrFetching";
import useDynamicForm from "@/lib/useDynamicForm";
import { addTaskSchemaPlus } from "@/lib/zodSchema";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { DateTime } from "luxon";
import { useSession } from "@/lib/useSession";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { get } from "http";

interface WorkType {
  id: number;
  work_type: string;
  created_at: string;
  updated_at: string;
  category: string;
  is_work_carrier_specific: boolean;
  does_it_require_planning_number: boolean;
}

interface TaskDetails {
  description?: string | undefined;
  clientName: string;
  carrierName?: string;
  workType: string;
  category: string;
  planningNumber?: string;
  date: Date;
  startTime: Date;
  endTime?: Date;
}

export const AddTask = ({
  selectedClient,
  selectedCarrier,
  setIsTimerRunning,
  setElapsedTime,
  isTimerRunning,
  setSeletedWorkType,
  workTypes,
  allClient,
  allCarrier,
  setSelectedClient,
  setSelectedCarrier,
  previousSelectedClient,
  previousSelectedCarrier,
  seletedWorkType,
  setIsAddTaskOpen,
  isAddTaskOpen,
  setTaskCreated,
  taskCreatd,
  userData,
  allUser,
}: any) => {
  const [showPlanningNumber, setShowPlanningNumber] = useState(false);
  const [task, setTask] = useState<TaskDetails>();
  const [currentSelectedWorkType, setCurrentSelectedWorkType] =
    useState<any>("");
  const [error, setError] = useState<string>("");
  const [errorPlanningNumber, setErrorPlanningNumber] = useState<string>("");
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [selectedValue, setSelectedValue] = useState("");
  const selectRef = useRef<HTMLDivElement | null>(null);
  // const [selectedWorkType, setSelectedWorkType] = useState<string>("");
  const [isLogoutConfirmationOpen, setIsLogoutConfirmationOpen] =
    useState(false);
  const { checkSessionToken, isSessionValid } = useSession(userData);
  const router = useRouter();
  const [carrierByClient, setCarrierByClient] = useState([]);

  const { form } = useDynamicForm(addTaskSchemaPlus, {
    description: undefined,
    date: DateTime.now().toISODate(),
    clientName: previousSelectedClient?.client_id?.toString() || "",
    carrierName:
      previousSelectedCarrier?.carrier_id?.toString() ||
      previousSelectedCarrier?.carrier?.id?.toString() ||
      "",
    workType: "",
    category: 0,
    planningNumber: "",
    startTime: new Date().toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    }),
    endTime: new Date().toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    }),
    timerDuration: "",
    duration: "",
    task_type: "",
  });

  const { setValue, watch } = form;
  const workType = watch("workType");
  const clientId = watch("clientName");
  const carrierId = watch("carrierName");
  //  (clientId);

  const selectedWorkType = workTypes.find(
    (work) => work.id.toString() === workType
  );

  allClient.sort((a, b) => a.client_name.localeCompare(b.client_name));

  workTypes.sort((a, b) => a.work_type.localeCompare(b.work_type));

  const getDedicatedClient = (userData: any) => {
    if (!userData?.userClients) return [];
    return userData.userClients
      .map((uc: any) => uc.client)
      .filter((client: any) => {
        return client?.id && client?.client_name;
      });
  };

  const isDedicatedClient = (selectedClient: any, userData: any) => {
    if (!userData?.userClients || !selectedClient) return false;
    return userData.userClients.some(
      (uc: any) => uc.clientId.toString() === selectedClient
    );
  };

  const getCSA = (userData: any, allUser: any[]) => {
    if (!userData || !allUser || !Array.isArray(allUser)) return null;

    if (userData.level > 3) return null;

    const parentUser = allUser.find((u) => u.id === userData.parent_id);
    if (!parentUser) return null;

    if (parentUser?.level === 3) return parentUser;
    if (parentUser.level === 2) {
      return getCSA(parentUser, allUser);
    }
    return null;
  };

  const csaUser = getCSA(userData, allUser);

  const onSubmit = async (values: any) => {
    const sessionValid = await checkSessionToken();

    if (!sessionValid || !isSessionValid) {
      setIsLogoutConfirmationOpen(true);
      return;
    }

    if (
      currentSelectedWorkType?.is_backlog_regular_required &&
      !values.task_type
    ) {
      setError("Task type is required when backlog/regular is required.");
      return;
    }

    if (
      currentSelectedWorkType.does_it_require_planning_number &&
      !values.planningNumber
    ) {
      setErrorPlanningNumber("Actual Number is required.");
      return;
    } else if (currentSelectedWorkType.does_it_require_planning_number) {
      if (values.planningNumber === "0" || values.planningNumber === "") {
        setErrorPlanningNumber("Actual Number should be greater than 0.");
        return;
      }
    }

    let switch_type: "INT" | "EXT" | null = null;

    if (userData.level < 3) {
      const dc = getDedicatedClient(userData);
      const clientId = parseInt(values.clientName);
      if (dc.map((c: any) => c.id).includes(clientId)) {
        switch_type = null;
      } else {
        const csa = getCSA(userData, allUser);
        const internalClients = getDedicatedClient(csa);
        if (internalClients.map((c: any) => c.id).includes(clientId)) {
          switch_type = "INT";
        } else {
          switch_type = "EXT";
        }
      }
    } else {
      switch_type = null;
    }
    try {
      const submittedValues = {
        ...values,
        planning_nummbers: values.planningNumber,
        date: new Date(values.date),
        client_id: parseInt(values.clientName),
        carrier_id: parseInt(values.carrierName),
        start_time: DateTime.fromFormat(values.startTime, "hh:mm a").toISO(),
        end_time: DateTime.fromFormat(values.endTime, "hh:mm a").toISO(),
        notes: values.description,
        actual_number: currentSelectedWorkType.does_it_require_planning_number
          ? Number(values.planningNumber)
          : null,
        task_type: currentSelectedWorkType?.is_backlog_regular_required
          ? values.task_type
          : null,
        work_type_id: parseInt(values.workType),
        switch_type,
      };
      //  (submittedValues);

      if (!submittedValues.client_id) {
        toast.error("Please select a client");
        return;
      }

      const data = await formSubmit(
        workreport_routes.CREATE_WORKREPORT,
        "POST",
        submittedValues,
        "/user/tracker"
      );

      // if (data.success) {
      //   setTask(submittedValues);
      //   setStartTime(new Date());
      //   setIsTimerRunning(true);
      //   setElapsedTime(0);

      //   setSelectedCarrier(submittedValues.carrierName);

      //   localStorage.setItem("workType", JSON.stringify(parseInt(workType)));
      //   router.refresh();
      // }

      //  (data, "data");

      if (data.success) {
        toast.success("Task has been created successfully.");
        setTaskCreated(!taskCreatd);
        setError("");
        setErrorPlanningNumber("");
        setCurrentSelectedWorkType(null);
        form.reset();
        router.refresh();
        setValue("description", "");
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  // useEffect(() => {
  //   router.refresh();
  // }, [isTimerRunning]);

  const handleStop = () => {
    setIsTimerRunning(false);
    setElapsedTime(0);
    if (task && startTime) {
      const endTime = new Date();
      const updatedTask = { ...task, endTime };

      //  ("Task updated with end time:", updatedTask);
    }
  };

  const handleChange = async (id: string) => {
    try {
      const carrierByClientData = await getAllData(
        `${carrier_routes.GET_CARRIER_BY_CLIENT}/${id}`
      );
      //  (carrierByClientData);

      carrierByClientData.sort((a, b) =>
        a.carrier?.name.localeCompare(b.carrier.name)
      );

      setCarrierByClient(carrierByClientData);
    } catch (error) {
      console.error("Error fetching carrier data:", error);
    }
  };

  const handleWorkTypeChange = (workType: string) => {
    const selectedWorkType = workTypes.find(
      (work) => work.id.toString() === workType
    );

    setCurrentSelectedWorkType(selectedWorkType);

    setSeletedWorkType(selectedWorkType);

    if (selectedWorkType) {
      setValue("category", selectedWorkType.category_id);
      setShowPlanningNumber(selectedWorkType.does_it_require_planning_number);
    } else {
      setShowPlanningNumber(false);
    }
  };

  return (
    <>
      {isLogoutConfirmationOpen && (
        <Dialog open={isLogoutConfirmationOpen}>
          <DialogContent>
            <div className="text-center">
              <p className="mb-4">
                You are already logged in on another device.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      )}
      <div className="w-full ">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(
              (values) => onSubmit(values)
              // (errors) => console.error("Validation Errors:", errors)
            )}
          >
            <div>
              <div
                className={`grid ${
                  selectedWorkType?.is_backlog_regular_required
                    ? "grid-cols-2 lg:grid-cols-5"
                    : "grid-cols-2 lg:grid-cols-4"
                }   md:w-full items-center gap-3 `}
              >
                <FormInput
                  label="Date"
                  form={form}
                  name="date"
                  type="date"
                  isRequired
                />
                <SelectComp
                  form={form}
                  name="clientName"
                  label="Select Client"
                  placeholder="Select Client"
                  isRequired
                  onValueChange={(e) => {
                    handleChange(e);
                    setSelectedClient(e);
                  }}
                >
                  {allClient.map((c) => (
                    <SelectItem key={c.id} value={c.id.toString()}>
                      {c.client_name}
                    </SelectItem>
                  ))}
                </SelectComp>

                <SelectComp
                  form={form}
                  name="carrierName"
                  label="Select Carrier"
                  placeholder="Select Carrier"
                  isRequired
                  onValueChange={(e) => {
                    setSelectedCarrier(e);
                  }}
                >
                  <SelectItem value={"N/A"}>N/A</SelectItem>
                  {carrierByClient.map((c) => (
                    <SelectItem
                      key={c.carrier.id}
                      value={c.carrier.id?.toString()}
                    >
                      {c.carrier.name}
                    </SelectItem>
                  ))}
                </SelectComp>

                <SelectComp
                  form={form}
                  label="Work Type"
                  name="workType"
                  placeholder="Select Work Type"
                  isRequired
                  className="w-full rounded-md"
                  onValueChange={(e) => handleWorkTypeChange(e)}
                >
                  {workTypes &&
                    workTypes.map((workType) => (
                      <SelectItem
                        value={workType.id.toString()}
                        key={workType.id}
                        ref={selectRef}
                      >
                        {workType.work_type}
                      </SelectItem>
                    ))}
                </SelectComp>

                {currentSelectedWorkType?.is_backlog_regular_required && (
                  <span className="flex flex-col gap-2">
                    <FormRadio
                      form={form}
                      name="task_type"
                      label=""
                      options={[
                        { value: "REGULAR", label: "Regular" },
                        { value: "BACKLOG", label: "Backlog" },
                      ]}
                      className="pt-5 flex justify-center "
                    />
                    {error && <p className="text-red-500 text-xs">{error}</p>}
                  </span>
                )}
              </div>
              <div
                className={`grid ${
                  selectedWorkType?.does_it_require_planning_number
                    ? "grid-cols-2 lg:grid-cols-5"
                    : "grid-cols-2 lg:grid-cols-4"
                }   md:w-full items-center gap-3 `}
              >
                <FormTimePicker
                  form={form}
                  label="Start Time"
                  name="startTime"
                  placeholder="hh:mm AM/PM"
                  isRequired
                />

                <FormTimePicker
                  form={form}
                  label="End Time"
                  name="endTime"
                  placeholder="hh:mm AM/PM"
                  isRequired
                />

                {currentSelectedWorkType?.does_it_require_planning_number && (
                  <span className="flex flex-col gap-2">
                    <FormInput
                      label="Actual Number"
                      form={form}
                      name="planningNumber"
                      type="number"
                      isRequired
                    />
                    {errorPlanningNumber && (
                      <p className="text-red-500 text-xs">
                        {errorPlanningNumber}
                      </p>
                    )}
                  </span>
                )}

                <FormInput
                  label="Notes"
                  form={form}
                  name="description"
                  type="text"
                />
                <SubmitBtn
                  className="mt-5 md:mt-1 w-full bg-primary/80 hover:bg-primary/90 text-white font-medium rounded-md transition-colors"
                  text="Submit"
                />
              </div>

              {/* {showPlanningNumber && (
              <FormInput
                label="Planning Number"
                form={form}
                name="planningNumber"
                placeholder="Enter Planning Number"
                type="number"
                className="w-full bg-gray-50 rounded-md"
              />
            )} */}
            </div>
          </form>
        </Form>
      </div>
    </>
  );
};
