

import { useEffect, useState } from "react";

const UserHierarchy = (hrUserId, allEmployee, userlevel, userData,hrUser) => {
  const [userTree, setUserTree] = useState({
    loggedInUser: null,
    parentUser: null,
    grandParentUser: null,
    grandGrandParentUser: null,
    childrenUsers: [],
    loggedInUserTitle: null,
    parentUserTitle: null,
    grandParentUserTitle: null,
    grandGrandParentUserTitle: null,
    childrenUsersTitles: [],
  });

  const getSubChildren = async (parentId, allEmployee, userlevel) => {
    const subChildren = allEmployee.filter(
      (user) => user.parent_id === parentId
    );

    return Promise.all(
      subChildren.map(async (subChild) => {
        const title =
          userlevel.find((level) => level.level === subChild.level)?.title ||
          "";

        const subChildWithSubChildren = {
          id: subChild.id,
          username: subChild.username || "",
          firstName: subChild.firstName || "",
          lastName: subChild.lastName || "",
          level: subChild.level,
          title,
          parent_id: subChild.parent_id,
          subChildren: [],
        };

        const hasChildren = allEmployee.some(
          (u) => u.parent_id === subChild.id
        );
        if (hasChildren) {
          subChildWithSubChildren.subChildren = await getSubChildren(
            subChild.id,
            allEmployee,
            userlevel
          );
        }

        return subChildWithSubChildren;
      })
    );
  };

  const getParentChain = (user, allEmployee, userlevel) => {
    const chain = [];
    let current = user;

    while (current?.parent_id) {
      const parent = allEmployee.find((u) => u.id === current.parent_id);
      if (!parent) break;

      chain.unshift({
        ...parent,
        title:
          userlevel.find((level) => level.level === parent.level)?.title || "",
      });

      current = parent;
    }

    return chain;
  };

  useEffect(() => {
    const fetchUserTreeData = async () => {
      try {
        if (!userData?.id || !Array.isArray(allEmployee)) return;

        const loggedInUser = 
        allEmployee.find(
          (user) => user.id === hrUserId 
        );
        console.log(loggedInUser)
        const parentChain = getParentChain(hrUserId, allEmployee, userlevel);

        const [grandGrandParentUser, grandParentUser, parentUser] = parentChain;

        const loggedInUserTitle =
          userlevel.find((level) => level.level === loggedInUser?.level)
            ?.title || "";

        const childrenUsers = allEmployee.filter(
          (user) => user.parent_id === hrUserId
        );

        const childrenUsersWithSubChildren = await Promise.all(
          childrenUsers.map(async (child) => {
            const childTitle =
              userlevel.find((level) => level.level === child.level)?.title ||
              "";

            const subChildren = await getSubChildren(
              child.id,
              allEmployee,
              userlevel
            );

            return {
              id: child.id,
              username: child.username || "",
              firstName: child.firstName || "",
              lastName: child.lastName || "",
              level: child.level,
              title: childTitle,
              parent_id: child.parent_id,
              subChildren,
            };
          })
        );

        setUserTree({
          loggedInUser,
          parentUser,
          grandParentUser,
          grandGrandParentUser,
          childrenUsers,
          loggedInUserTitle,
          parentUserTitle: parentUser?.title || "",
          grandParentUserTitle: grandParentUser?.title || "",
          grandGrandParentUserTitle: grandGrandParentUser?.title || "",
          childrenUsersTitles: childrenUsersWithSubChildren,
        });
      } catch (error) {
        console.error("Failed to fetch user data:", error);
      }
    };

    if (hrUserId) {
      fetchUserTreeData();
    }
  }, [hrUserId]);

  return userTree;
};

export default UserHierarchy;
