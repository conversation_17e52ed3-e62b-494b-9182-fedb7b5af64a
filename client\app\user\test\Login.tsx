"use client";
import FormInput from "@/app/_component/FormInput";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { Form } from "@/components/ui/form";
import { employee_routes } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { LoginSchema } from "@/lib/zodSchema";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { toast } from "sonner";
import { CiUser } from "react-icons/ci";
import { IoLockClosedOutline } from "react-icons/io5";
const Login = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { form, startTransition } = useDynamicForm(LoginSchema, {
    username: "",
    password: "",
  });
  async function onSubmit(values: { username: string; password: string }) {
    try {
      setIsLoading(true);
      const formData = {
        username: values.username,
        password: values.password,
      };
      //  (formData);
      const route = employee_routes.LOGIN_USERS;

      startTransition(async () => {
        const res = await fetch(route, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify(formData),
        });
        const data = await res.json();

        if (data.success === true) {
          toast.success(data.message);
          router.push("/user/tracker");
        } else {
          toast.error(data.error || "An error occurred while login user.");
        }
        setIsLoading(false);
      });
    } catch (error) {
      toast.error("Login failed");
      console.error(error);
      setIsLoading(false);
    }
  }
  return (
    <Form {...form}>
      <div className="w-full max-w-md bg-white rounded-2xl shadow-xl p-6 md:p-8 border border-slate-100 backdrop-blur-sm bg-white/90">
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className=" h-full flex items-center justify-center"
        >
          <div className=" w-full max-w-sm p-4">
            <div className="text-center mb-8">
              <div className="inline-block p-3 bg-blue-50 rounded-full mb-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-600"
                >
                  <path d="M2 18v3c0 .6.4 1 1 1h4v-3h3v-3h2l1.4-1.4a6.5 6.5 0 1 0-4-4Z" />
                  <circle cx="16.5" cy="7.5" r=".5" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-slate-800">Welcome</h2>
              <p className="text-slate-500 mt-2">
                Sign in to access your Oi360 platform 
              </p>
            </div>
            <div>
              <FormInput
                form={form}
                label="OpsID"
                name="username"
                type="text"
                icon={{
                  Component: CiUser,
                  position: "left",
                }}
              />
              <FormInput
                form={form}
                label="OpsKey"
                name="password"
                type="password"
                icon={{
                  Component: IoLockClosedOutline,
                  position: "left",
                }}
              />
              {/* <SubmitBtn className="w-full" text="Submit" /> */}
              <SubmitBtn
                className="w-full py-6 text-base mt-4 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white shadow-md hover:shadow-lg transition-all duration-200 rounded-xl"
                text={isLoading ? "Signing in..." : "Sign in"}
              />
            </div>
          </div>
        </form>
      </div>
    </Form>
  );
};

export default Login;
