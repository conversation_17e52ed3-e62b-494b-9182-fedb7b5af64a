import { Request, Response, NextFunction } from "express";
import path from "path";
import fs from "fs";
import { calculateAges, parseCSVLine } from "./create";

export const processCSVTwoTen = async (dataBuffer, req, res) => {
  const { id } = req.params;
  try {
    if (!dataBuffer) {
      return res.status(400).send("No file data found.");
    }

    const dailyPlanningDetails = await prisma.dailyPlanning.findFirst({
      where: { id: Number(id) },
    });

    const client = await prisma.dailyPlanning.findFirst({
      where: {
        id: Number(id),
        client_id: dailyPlanningDetails.client_id,
      },
      include: {
        client: {
          include: {
            ClientCarrier: true,
          },
        },
      },
    });

    const text = fs.readFileSync(dataBuffer, "utf-8");
    const lines = text.split("\n");

    if (lines.length < 2) {
      return res.status(400).send("CSV is empty or has no data rows.");
    }

    const headers = lines[0].split("\t");

    const requiredColumns = ["Carrier", "Status", "Invoice Date"];
    const missingColumns = requiredColumns.filter(
      (col) => !headers.includes(col)
    );

    if (missingColumns.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Invalid CSV format. Missing columns: ${missingColumns.join(
          ", "
        )}`,
      });
    }

    const columnIndexes = {
      carrier: headers.indexOf("Carrier"),
      status: headers.indexOf("Status"),
      invoiceDate: headers.indexOf("Invoice Date"),
    };

    const newData: {
      [key: string]: { [key: string]: { count: number; dates: string[] } };
    } = {};

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split("\t");
      if (values.length === 0) continue;

      const carrier = values[columnIndexes.carrier]?.trim();
      const status = values[columnIndexes.status]?.trim();
      const invoiceDateStr = values[columnIndexes.invoiceDate]?.trim();

      if (!carrier || !status || !invoiceDateStr) continue;

      newData[carrier] = newData[carrier] || {};
      newData[carrier][status] = newData[carrier][status] || {
        count: 0,
        dates: [],
      };

      newData[carrier][status].count++;
      newData[carrier][status].dates.push(invoiceDateStr);
    }

    //  ("Processed Data:", newData);

    const statusMappings = {
      SUCCESS: { type: "TWO_TEN_SUCCESS", dataKey: "two_ten_success" },
      "MATCH FAILED": { type: "TWO_TEN_M_F", dataKey: "two_ten_m_f" },
      HOLD: { type: "TWO_TEN_HOLD", dataKey: "two_ten_hold" },
      "IMPORT ADDITIONAL": {
        type: "TWO_TEN_IMPORT_ADDITIONAL",
        dataKey: "two_ten_import_additional",
      },
      ERROR: { type: "TWO_TEN_ERROR", dataKey: "two_ten_error" },
      "MANUAL MATCH": {
        type: "TWO_TEN_MANUAL_MATCH",
        dataKey: "two_ten_manual_match",
      },
    };
    await prisma.dailyPlanningDetails.deleteMany({
      where: { daily_planning_id: Number(id), source: "uploadViaEdi" },
    });
    const error = [];
    for (const [carrier, statuses] of Object.entries(newData)) {
      for (const [status, { count, dates }] of Object.entries(statuses)) {
        const mapping = statusMappings[status];
        if (!mapping) {
          continue;
        }

        const { type, dataKey } = mapping;


        let Dpt = await prisma.dailyPlanningByType.findFirst({
          where: { daily_planning_id: Number(id), type },
        });

        if (!Dpt) {
          Dpt = await prisma.dailyPlanningByType.create({
            data: { daily_planning_id: Number(id), type },
          });
        }
        
        const carrier_name = await prisma.carrier.findFirst({
          where: { name: carrier },
          select: { id: true },
        });

        if (!carrier_name) {
          error.push(`Carrier not found: ${carrier}`);
          continue;
        }

        const cheackClientCarrier = client.client.ClientCarrier.some(
          (item) => item.carrier_id === carrier_name?.id
        );

        if (!cheackClientCarrier) {
          error.push(`Client carrier not found: ${carrier}`);
          // console.warn(`Carrier not found: ${carrier}`);
          continue;
        }

        const ages = calculateAges(dates);

        await prisma.dailyPlanningDetails.create({
          data: {
            carrier_id: Number(carrier_name.id),
            daily_planning_id: Number(id),
            daily_planning_type_id: Dpt.id,
            [dataKey]: count || null,
            age: ages,
            type,
            source: "uploadViaEdi",
          },
        });
      }
    }

   
    return res.status(200).json({
      success: true,
      message: "CSV processed successfully.",
      error,
    });
  } catch (error) {
    console.error("Error processing file:", error);
    return res.status(500).json({
      success: false,
      error: "Error processing CSV.",
    });
  } finally {
    fs.unlinkSync(dataBuffer);
  }
};
export const uploadCSVTwoTen = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  if (!req.file) {
    res.status(400).json({ success: false, error: "No file uploaded." });
    return;
  }

  try {
    const relativeFilePath = req.file.path;
    const filePath = path.join(__dirname, "../../../../", relativeFilePath);
    await processCSVTwoTen(filePath, req, res);
  } catch (error) {
    console.error("Error processing file:", error);
    next(error);
  }
};
