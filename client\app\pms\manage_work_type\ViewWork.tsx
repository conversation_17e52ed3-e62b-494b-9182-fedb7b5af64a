"use client";
import DataTable from "@/app/_component/DataTable";
import React from "react";
import { WorkType, column } from "./column";

const ViewWork = ({ data, permissions, allCategory }: { data: WorkType[]; permissions: string[]; allCategory: any }) => {
  
  return (
    <div>
      <DataTable
        data={data}
        columns={column(permissions, allCategory)}
        // filter
        // filter_column="work_type"
        showColDropDowns
        showPageEntries
        // filter2
        // filter_column2="category"
      />
    </div>
  );
};

export default ViewWork;