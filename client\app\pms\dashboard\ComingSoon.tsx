"use client";
import React, { useEffect, useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  PieChart,
  Pie,
  Cell,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LabelList,
} from "recharts";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

export interface Client {
  client_name: string;
  owner_name: string;
  country: string;
  client_id: any;
  corporation_id: any;
  created_at: any;
  updated_at: any;
  // permissions: string[];
}

export interface Carrier {
  name: string;
  register1: string;
  code: string;
  country: string;
  state: string;
  city: string;
  address: string;
  phone: string;
  postalcode: string;
  carrier_id: any;
}

type CSAData = {
  name: string;
  workDone: number;
  pendingWork: number;
};

type ClientCarrierData = {
  name: string;
  workDone: number;
  pendingWork: number;
};

type DataByTimePeriod = {
  year: CSAData[] | ClientCarrierData[];
  month: CSAData[] | ClientCarrierData[];
  week: CSAData[] | ClientCarrierData[];
};

const ComingSoon = ({
  allClient,
  allCarrier,
  allEmployee,
  allworkreport,
  allDailyPlanning,
}: {
  allClient: Client[];
  allCarrier: Carrier[];
  allEmployee: any;
  allworkreport: any;
  allDailyPlanning: any;
}) => {
  const [timePeriod, setTimePeriod] = useState<"year" | "month" | "week">(
    "month"
  );
  const [timePeriodStatus, setTimePeriodStatus] = useState<
    "year" | "month" | "week" | "day"
  >("month");
  const [workType, setWorkType] = useState<"CSA" | "ClientCarrier">("CSA");
  const [DailyPlanning, setdailyplanning] = useState<
    "createdstatus" | "workload" | "workstatus"
  >("createdstatus");
  const [allpendingwork, setallpendingwork] = useState([]);
  const [allworkdone, setallworkdone] = useState([]);
  const [pendingwork, setpendingwork] = useState([]);
  const [workdone, setworkdone] = useState([]);
  const [clientCarrierdata, setClientCarrierdata] = useState([]);
  const [chartdata, setChartData] = useState<any>([]);
  const [planningData, setPlanningData] = useState<any[]>([]);
  const [selectedClientName, setSelectedClientName] = useState<number | null>(
    null
  );
  const data: DataByTimePeriod = {
    year: [
      { name: "CSA 1", workDone: 100, pendingWork: 50 },
      { name: "CSA 2", workDone: 150, pendingWork: 30 },
      { name: "CSA 3", workDone: 80, pendingWork: 40 },
      { name: "CSA 4", workDone: 120, pendingWork: 60 },
      { name: "CSA 5", workDone: 90, pendingWork: 70 },
    ],
    month: [
      { name: "CSA 1", workDone: 10, pendingWork: 5 },
      { name: "CSA 2", workDone: 15, pendingWork: 3 },
      { name: "CSA 3", workDone: 8, pendingWork: 4 },
      { name: "CSA 4", workDone: 12, pendingWork: 6 },
      { name: "CSA 5", workDone: 9, pendingWork: 7 },
    ],
    week: [
      { name: "CSA 1", workDone: 2, pendingWork: 1 },
      { name: "CSA 2", workDone: 3, pendingWork: 1 },
      { name: "CSA 3", workDone: 2, pendingWork: 1 },
      { name: "CSA 4", workDone: 4, pendingWork: 2 },
      { name: "CSA 5", workDone: 3, pendingWork: 3 },
    ],
  };

  useEffect(() => {
    if (allClient.length > 0) {
      if (selectedClientName === null) {
        setSelectedClientName(allClient[0].client_id);
      }
    }
  }, [allClient, selectedClientName]);

  useEffect(() => {
    if (DailyPlanning === "createdstatus") {
      const processUserData = () => {
        const allEmployeeUserIds: Set<number> = new Set(
          allEmployee.map((employee: any) => employee.user_id)
        );
        //  (allEmployeeUserIds);
        const presentUserIds: Set<number> = new Set();
  
        // Filter data by created_at and timePeriodStatus
        const filteredDailyPlanning = filterByCreatedAtPeriod(allDailyPlanning, timePeriodStatus);
  
        filteredDailyPlanning.forEach((item: any) => {
          const userId = item.User.user_id;
          presentUserIds.add(userId);
        });
        const absentUserIds = Array.from(allEmployeeUserIds).filter(
          (userId) => !presentUserIds.has(userId)
        );
        const pieChartData = [
          { name: "Created", value: presentUserIds.size },
          { name: "Not Created", value: absentUserIds.length },
        ];
        setPlanningData(pieChartData);
      };
      processUserData();
    }
  }, [allDailyPlanning, allEmployee, DailyPlanning, timePeriodStatus]);
 


  const filterByCreatedAtPeriod = (data: any[], period: string) => {
    const now = new Date();
    
    return data.filter((item: any) => {
      const itemDate = new Date(item.created_at);
  
      if (period === "year") {
        return itemDate.getFullYear() === now.getFullYear();
      }
      if (period === "month") {
        return (
          itemDate.getMonth() === now.getMonth() &&
          itemDate.getFullYear() === now.getFullYear()
        );
      }
      if (period === "week") {
        const startOfWeek = now.getDate() - now.getDay();
        const endOfWeek = startOfWeek + 6;
        const startOfWeekDate = new Date(now.setDate(startOfWeek));
        const endOfWeekDate = new Date(now.setDate(endOfWeek));
        return itemDate >= startOfWeekDate && itemDate <= endOfWeekDate;
      }
      if (period === "day") {
        return itemDate.toDateString() === now.toDateString();
      }
      return false;
    });
  };
  
  const calculatePercentage = (value: number, total: number) => {
    return ((value / total) * 100).toFixed(2); // Round to 2 decimal places
  };

  // Calculate total value to compute percentages
  const total = planningData.reduce((acc, curr) => acc + curr.value, 0);

  // Update planning data to include percentage
  const updatedPlanningData = planningData.map((item) => ({
    ...item,
    percentage: calculatePercentage(item.value, total),
  }));

  const filterByPeriod = (data: any[], period: string) => {
    const now = new Date();

    return data.filter((item) => {
      const itemDate = new Date(item.daily_planning_date);

      if (period === "year") {
        return itemDate.getFullYear() === now.getFullYear();
      }
      if (period === "month") {
        return (
          itemDate.getMonth() === now.getMonth() &&
          itemDate.getFullYear() === now.getFullYear()
        );
      }
      if (period === "week") {
        const startOfWeek = now.getDate() - now.getDay();
        const endOfWeek = startOfWeek + 6;
        const startOfWeekDate = new Date(now.setDate(startOfWeek));
        const endOfWeekDate = new Date(now.setDate(endOfWeek));
        return itemDate >= startOfWeekDate && itemDate <= endOfWeekDate;
      }
      if (period === "day") {
        return itemDate.toDateString() === now.toDateString();
      }
      return false;
    });
  };

  const filterReportsByPeriod = (reports: any, period: any, dateKey: any) => {
    return reports.filter((report: any) => {
      const reportDate = new Date(report[dateKey]);
      const now = new Date();

      if (period === "year") {
        return reportDate.getFullYear() === now.getFullYear();
      } else if (period === "month") {
        return (
          reportDate.getMonth() === now.getMonth() &&
          reportDate.getFullYear() === now.getFullYear()
        );
      } else if (period === "week") {
        const startOfWeek = now.getDate() - now.getDay();
        const endOfWeek = startOfWeek + 6;
        const startOfWeekDate = new Date(now.setDate(startOfWeek));
        const endOfWeekDate = new Date(now.setDate(endOfWeek));

        return reportDate >= startOfWeekDate && reportDate <= endOfWeekDate;
      }
      return false;
    });
  };

  const processCarrierData = (
    allCarrier: any,
    allworkreport: any,
    timePeriod: any,
    selectedClientName: number
  ) => {
    return allCarrier.map((carrier: any) => {
      const filteredReports = filterReportsByPeriod(
        carrier.WorkReport,
        timePeriod,
        "date"
      );

      const filteredForClient = selectedClientName
        ? filteredReports.filter(
            (report: any) => report.client_id === selectedClientName
          )
        : filteredReports;

      const finishedReports = filteredForClient.filter(
        (report: any) => report.work_status === "FINISHED"
      );
      const pendingReports = filteredForClient.filter(
        (report: any) => report.work_status !== "FINISHED"
      );

      // setworkdone(finishedReports);
      setpendingwork(pendingReports);

      return {
        name: carrier.name,
        workDone: finishedReports.length || 0,
        pendingWork: pendingReports.length || 0,
      };
    });
  };

  const getUserColor = (index: number) => {
    const colors = [
      "#37a183", // Green
      "#db7442", // Orange
      "#254054", // Blue
      "#dfb878", // Yellow
      "#9c27b0", // golden
      "#eb8344", // Orange
      "#00bcd4", // Cyan
      "#e91e63", // Pink
      "#795548", // Brown
    ];
    return colors[index % colors.length];
  };

  const dailyPlanningData: DataByTimePeriod = {
    year: [
      { name: "Week 1", workDone: 40, pendingWork: 15 },
      { name: "Week 2", workDone: 60, pendingWork: 20 },
      { name: "Week 3", workDone: 50, pendingWork: 10 },
      { name: "Week 4", workDone: 45, pendingWork: 25 },
    ],
    month: [
      { name: "Week 1", workDone: 8, pendingWork: 3 },
      { name: "Week 2", workDone: 12, pendingWork: 4 },
      { name: "Week 3", workDone: 10, pendingWork: 2 },
      { name: "Week 4", workDone: 9, pendingWork: 5 },
    ],
    week: [
      { name: "Day 1", workDone: 2, pendingWork: 1 },
      { name: "Day 2", workDone: 3, pendingWork: 1 },
      { name: "Day 3", workDone: 2, pendingWork: 1 },
      { name: "Day 4", workDone: 3, pendingWork: 2 },
      { name: "Day 5", workDone: 4, pendingWork: 1 },
    ],
  };

  const chartConfig = {
    desktop: {
      label: "Desktop",
      color: "hsl(var(--chart-1))",
    },
  } satisfies ChartConfig;
  const dailyChartData = dailyPlanningData[timePeriod];
  useEffect(() => {
    if (workType === "CSA") {
      setChartData(data[timePeriod]);
    } else {
      setChartData(clientCarrierdata);
    }
  }, []);

  useEffect(() => {
    if (selectedClientName) {
      setClientCarrierdata(
        processCarrierData(
          allCarrier,
          allworkreport,
          timePeriod,
          selectedClientName
        )
      );
    } else {
      // setworkdone(
      //   allworkreport.filter((report: any) => report.work_status === "FINISHED")
      // );
      setpendingwork(
        allworkreport.filter((report: any) => report.work_status !== "FINISHED")
      );
    }
  }, [
    selectedClientName,
    timePeriod,
    allCarrier,
    allworkreport,
    processCarrierData,
  ]);

  useEffect(() => {
    setallworkdone(
      allworkreport.filter((report: any) => report.work_status === "FINISHED")
    );
    setallpendingwork(
      allworkreport.filter((report: any) => report.work_status !== "FINISHED")
    );
  }, [allworkreport, setallworkdone, setallpendingwork]);

  const barSize = Math.max(50, 200 / chartdata.length); 
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 p-6">
      {/* <div className="bg-white p-6 rounded-xl shadow-md border border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-gray-500 text-sm">Employee</h3>
          <span className="text-green-600 bg-green-100 px-2 py-1 rounded-full text-xs">
            2025
          </span>
        </div>
        <p className="text-2xl font-bold text-gray-900 mt-2">
          {allEmployee.length}
        </p>
      </div> */}
      {/* <div className="bg-white p-6 rounded-xl shadow-md border border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-gray-500 text-sm">Clients</h3>
          <span className="text-green-600 bg-green-100 px-2 py-1 rounded-full text-xs">
            {/* {allEmployee.length}  2025
          </span>
        </div>
        <p className="text-2xl font-bold text-gray-900 mt-2">
          {allClient.length}
        </p>
      </div>
      <div className="bg-white p-6 rounded-xl shadow-md border border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-gray-500 text-sm">Carriers</h3>
          <span className="text-green-600 bg-green-100 px-2 py-1 rounded-full text-xs">
            {/* {allEmployee.length} 2025
          </span>
        </div>
        <p className="text-2xl font-bold text-gray-900 mt-2">
          {allCarrier.length}
        </p>
      </div>
      <div className="bg-white p-6 rounded-xl shadow-md border border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-gray-500 text-sm">Work Done</h3>
          <span className="text-green-600 bg-green-100 px-2 py-1 rounded-full text-xs">
            {/* {allEmployee.length}  2025
          </span>
        </div>
        <p className="text-2xl font-bold text-gray-900 mt-2">
          {allworkdone.length}
        </p>
      </div>
      <div className="bg-white p-6 rounded-xl shadow-md border border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-gray-500 text-sm">Pending Work</h3>
          <span className="text-green-600 bg-green-100 px-2 py-1 rounded-full text-xs">
            {/* {allEmployee.length} 2025
          </span>
        </div>
        <p className="text-2xl font-bold text-gray-900 mt-2">
          {allpendingwork.length}
        </p>
      </div> */}

      {/* Chart for selected data */}
      <Card className="col-span-1 sm:col-span-2 md:col-span-3 lg:col-span-5 bg-white shadow-lg hover:shadow-xl transition duration-300">
        <div className="flex flex-row space-y-4">
          <div className="mt-4 w-3/4">
            <CardHeader>
              <CardTitle className="text-xl font-semibold">
                Select Work Type
              </CardTitle>
              <div className="flex space-x-5">
                <select
                  className="p-2 border rounded-md max-w-md"
                  value={workType}
                  onChange={(e) =>
                    setWorkType(e.target.value as "CSA" | "ClientCarrier")
                  }
                >
                  <option value="CSA">CSA Work Comparison</option>
                  <option value="ClientCarrier">Client/Carrier Work </option>
                </select>
                {workType === "ClientCarrier" && (
                  <select
                    className="p-2 border rounded-md max-w-md"
                    value={selectedClientName?.toString()}
                    onChange={(e) => {
                      const selectedValue = Number(e.target.value);
                      const selectedClient = allClient.find(
                        (c) => c.client_id === selectedValue
                      );
                      //  if (selectedClient) {
                      setSelectedClientName(selectedValue);
                      //  }
                    }}
                  >
                    <option value="">Select a Client</option>
                    {allClient.map((c) => (
                      <option key={c.client_id} value={c.client_id}>
                        {c.client_name}
                      </option>
                    ))}
                  </select>
                )}
                <select
                  className="p-2 border rounded-md max-w-md"
                  value={timePeriod}
                  onChange={(e) =>
                    setTimePeriod(e.target.value as "year" | "month" | "week")
                  }
                >
                  <option value="year">Year</option>
                  <option value="month">Month</option>
                  <option value="week">Week</option>
                </select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col space-y-4">
                <ResponsiveContainer width="100%" height={300}>
                  <ChartContainer config={chartConfig}>
                    <BarChart data={chartdata}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="name"
                        tickLine={false}
                        axisLine={false}
                        height={60} 
                      />
                      <YAxis
                        tickLine={false}
                        domain={[0, 100]}
                        tickFormatter={(value) => `${value}%`}
                        tickCount={6}
                        tickMargin={10}
                        axisLine={false}
                      />
                      <ChartTooltip
                        cursor={false}
                        content={<ChartTooltipContent hideLabel />}
                      />

                      <Tooltip />
                      <Legend />
                      <Bar dataKey="workDone" fill="#37a183" radius={8}  barSize={barSize}/>
                      <LabelList
                        position="top"
                        offset={12}
                        className="fill-foreground"
                        fontSize={12}
                      />
                      <Bar dataKey="pendingWork" fill="#f44336" radius={8} />
                    </BarChart>
                  </ChartContainer>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </div>
          <div className="w-1/2">
            <CardHeader>
              <CardTitle className="text-xl font-semibold">
                Select Daily Planning{" "}
              </CardTitle>
              <div className="flex space-x-5">
                <select
                  className="p-2 border rounded-md max-w-md"
                  value={DailyPlanning}
                  onChange={(e) =>
                    setdailyplanning(
                      e.target.value as
                        | "createdstatus"
                        | "workload"
                        | "workstatus"
                    )
                  }
                >
                  <option value="createdstatus">
                    Daily Planning Work Created Status
                  </option>
                  <option value="workload">Daily Planning Work Load</option>
                  <option value="workstatus">Daily Planning Work Status</option>
                </select>

                <select
                  className="p-2 border rounded-md max-w-md"
                  value={timePeriodStatus}
                  onChange={(e) =>
                    setTimePeriodStatus(
                      e.target.value as "year" | "month" | "week" | "day"
                    )
                  }
                >
                  <option value="year">Year</option>
                  <option value="month">Month</option>
                  <option value="week">Week</option>
                  <option value="week">Day</option>
                </select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col space-y-4">
                {DailyPlanning === "createdstatus" &&
                  updatedPlanningData.length > 0 && (
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={updatedPlanningData}
                          dataKey="value"
                          outerRadius={100}
                          innerRadius={60}
                          // label
                        >
                          {updatedPlanningData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={getUserColor(index)}
                            />
                          ))}
                        </Pie>
                        <Tooltip
                          content={({ payload }) => {
                            if (!payload || payload.length === 0) return null;
                            const { name, value, percentage } =
                              payload[0]?.payload || {};
                            return (
                              <div>
                                <strong>{`${percentage}% ${name}`}</strong>
                              </div>
                            );
                          }}
                        />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  )}

                {DailyPlanning === "workstatus" && planningData.length > 0 && (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={updatedPlanningData}
                        dataKey="value"
                        outerRadius={100}
                        innerRadius={60}
                        label
                      >
                        {planningData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={getUserColor(index)}
                          />
                        ))}
                      </Pie>
                      <Tooltip
                        content={({ payload }) => {
                          if (!payload || payload.length === 0) return null;
                          const { name, value } = payload[0]?.payload || {};
                          return (
                            <div>
                              <strong>{name}</strong>
                              <br />
                              <span>{`Count: ${value}`}</span>
                            </div>
                          );
                        }}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                )}

                {DailyPlanning === "workload" && planningData.length > 0 && (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={planningData}
                        dataKey="value"
                        outerRadius={100}
                        innerRadius={60}
                        label
                      >
                        {planningData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={getUserColor(index)}
                          />
                        ))}
                      </Pie>
                      <Tooltip
                        content={({ payload }) => {
                          if (!payload || payload.length === 0) return null;
                          const { name, value } = payload[0]?.payload || {};
                          return (
                            <div>
                              <strong>{name}</strong>
                              <br />
                              <span>{`Count: ${value}`}</span>
                            </div>
                          );
                        }}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </div>
            </CardContent>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ComingSoon;
