"use client";

import { useState, useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Download, ChevronUp, ChevronDown } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const ViewPFTable = ({ dailyPlanningDetailsPF }: any) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortColumn, setSortColumn] = useState("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [priorityFilter, setPriorityFilter] = useState("All");

  const carrierAgeCounts = dailyPlanningDetailsPF?.carrierAgeCounts;

  const aggregatedData = useMemo(() => {
    const acc: any = {};

    dailyPlanningDetailsPF.data?.forEach((item: any) => {
      item.DailyPlanningDetails.forEach((detail: any) => {
        const carrierKey = `${detail.carrier.name}_${detail.carrier_id}`;

        if (!acc[carrierKey]) {
          acc[carrierKey] = {
            name: detail.carrier.name,
            totalPfStatus: 0,
          };
        }

        acc[carrierKey].totalPfStatus += detail.pf_status || 0;
      });
    });

    return acc;
  }, [dailyPlanningDetailsPF?.data]);

  const filteredCarriers = useMemo(() => {
    return Object.values(aggregatedData)
      .filter((carrier: any) =>
        carrier.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .filter((carrier: any) => {
        if (priorityFilter === "All") return true;
        const carrierData = Object.values(carrierAgeCounts).find(
          (item: any) =>
            item.name.trim().toLowerCase() === carrier.name.trim().toLowerCase()
        );
        return (
          carrierData &&
          Object.values(carrierData).some(
            (value: any) => value.priority === priorityFilter
          )
        );
      })
      .sort((a: any, b: any) => {
        if (sortColumn === "name") {
          return sortDirection === "asc"
            ? a.name.localeCompare(b.name)
            : b.name.localeCompare(a.name);
        } else {
          return sortDirection === "asc"
            ? a.totalPfStatus - b.totalPfStatus
            : b.totalPfStatus - a.totalPfStatus;
        }
      });
  }, [
    aggregatedData,
    searchTerm,
    sortColumn,
    sortDirection,
    priorityFilter,
    carrierAgeCounts,
  ]);

  const totals: any = useMemo(() => {
    return filteredCarriers.reduce(
      (totals: any, carrier: any) => {
        totals.totalPfStatus += carrier.totalPfStatus;
        return totals;
      },
      { totalPfStatus: 0 }
    );
  }, [filteredCarriers]);

  const handleSort = (column: string) => {
    if (column === sortColumn) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  const formatBucket = (key: string) => {
    const bucketMap: any = {
      zerotoseven: "[0-7]",
      eighttofifteen: "[8-15]",
      sixteentothirty: "[16-30]",
      thirtyonetosixty: "[31-60]",
      sixtyonetoninety: "[61-90]",
      ninetyonetohundredandtwenty: "[91-120]",
      hundredandtwentyplus: "[120+]",
    };
    return bucketMap[key] || key;
  };

  const getPriorityBuckets = (priority: string, carrierData: any) => {
    const buckets = Object.entries(carrierData)
      .filter(([key, value]: any) => value && value.priority === priority)
      .map(([key, value]: any) => ({
        range: formatBucket(key),
        count: value.count,
      }));

    if (buckets.length === 0) return "-";

    return (
      <div className="flex flex-wrap gap-2 justify-end">
        {buckets.map(({ range, count }, idx) => (
          <span
            key={idx}
            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              priority === "High"
                ? "bg-red-100 text-red-700"
                : priority === "Medium"
                ? "bg-yellow-100 text-yellow-700"
                : "bg-green-100 text-green-700"
            }`}
          >
            {range}: {count}
          </span>
        ))}
      </div>
    );
  };

  return (
    <div className="">
      {/* <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search carriers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>
      </div> */}
      <div>
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50">
              <TableHead
                className="w-[200px] cursor-pointer"
                onClick={() => handleSort("name")}
              >
                Carrier
                {sortColumn === "name" &&
                  (sortDirection === "asc" ? (
                    <ChevronUp className="inline ml-2 h-4 w-4" />
                  ) : (
                    <ChevronDown className="inline ml-2 h-4 w-4" />
                  ))}
              </TableHead>
              <TableHead
                className="text-right cursor-pointer"
                onClick={() => handleSort("totalPfStatus")}
              >
                PF
                {sortColumn === "totalPfStatus" &&
                  (sortDirection === "asc" ? (
                    <ChevronUp className="inline ml-2 h-4 w-4" />
                  ) : (
                    <ChevronDown className="inline ml-2 h-4 w-4" />
                  ))}
              </TableHead>
              <TableHead className="text-right w-[250px]">High</TableHead>
              <TableHead className="text-right w-[250px]">Medium</TableHead>
              <TableHead className="text-right w-[250px]">Low</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCarriers.map((carrier: any, index: number) => {
              const carrierData = Object.values(carrierAgeCounts).find(
                (item: any) =>
                  item.name.trim().toLowerCase() ===
                  carrier.name.trim().toLowerCase()
              );

              if (!carrierData) {
                console.warn(`No data found for carrier: ${carrier.name}`);
                return null;
              }

              return (
                <TableRow
                  key={index}
                  className="hover:bg-muted/50 transition-colors"
                >
                  <TableCell className="font-medium">{carrier.name}</TableCell>
                  <TableCell className="text-right">
                    <span className="font-semibold text-primary bg-primary/10 px-2 py-1 rounded">
                      {carrier.totalPfStatus}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    {getPriorityBuckets("High", carrierData)}
                  </TableCell>
                  <TableCell className="text-right">
                    {getPriorityBuckets("Medium", carrierData)}
                  </TableCell>
                  <TableCell className="text-right">
                    {getPriorityBuckets("Low", carrierData)}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
          <TableFooter>
            <TableRow className="bg-muted/50">
              <TableCell className="font-bold">Total</TableCell>
              <TableCell className="text-right font-bold text-primary">
                {totals.totalPfStatus}
              </TableCell>
              <TableCell />
              <TableCell />
              <TableCell />
            </TableRow>
          </TableFooter>
        </Table>
      </div>
    </div>
  );
};

export default ViewPFTable;
