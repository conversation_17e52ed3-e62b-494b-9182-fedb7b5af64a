"use client";

import { useMemo, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ChevronUp, ChevronDown } from "lucide-react";
import { Badge } from "@/components/ui/badge";

const CustomTableForDP = ({ tableData, typeTable, heading }: any) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortColumn, setSortColumn] = useState("name");
  const [priorityFilter, setPriorityFilter] = useState("All");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const carrierAgeCounts = tableData?.carrierAgeCounts;

  const aggregatedData = useMemo(() => {
    const acc: any = {};

    tableData?.data?.forEach((item: any) => {
      item.DailyPlanningDetails?.forEach((detail: any) => {
        const carrierKey = `${detail.carrier.name}_${detail.carrier_id}`;

        if (!acc[carrierKey]) {
          acc[carrierKey] = {
            name: detail.carrier.name,
            [typeTable]: 0,
          };
        }

        acc[carrierKey][typeTable] += detail[typeTable] || 0;
      });
    });

    return acc;
  }, [tableData?.data]);

  const getBucketKey = (ageRange: string) => {
    const bucketKeyMap: { [key: string]: string } = {
      "120+": "hundredandtwentyplus",
      "91-120": "ninetyonetohundredandtwenty",
      "61-90": "sixtyonetoninety",
      "31-60": "thirtyonetosixty",
      "16-30": "sixteentothirty",
      "8-15": "eighttofifteen",
      "0-7": "zerotoseven",
    };
    return bucketKeyMap[ageRange];
  };

  const filteredCarriers = useMemo(() => {
    return (
      aggregatedData &&
      Object.values(aggregatedData)
        .filter((carrier: any) =>
          carrier.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
        .filter((carrier: any) => {
          if (priorityFilter === "All") return true;
          const carrierData = Object.values(carrierAgeCounts).find(
            (item: any) =>
              item.name.trim().toLowerCase() ===
              carrier.name.trim().toLowerCase()
          );
          return (
            carrierData &&
            Object.values(carrierData).some(
              (value: any) => value.priority === priorityFilter
            )
          );
        })
        .sort((a: any, b: any) => {
          if (sortColumn === "name") {
            return sortDirection === "asc"
              ? a.name.localeCompare(b.name)
              : b.name.localeCompare(a.name);
          } else {
            return sortDirection === "asc"
              ? a.correct - b.correct
              : b.correct - a.correct;
          }
        })
    );
  }, [
    aggregatedData,
    searchTerm,
    sortColumn,
    sortDirection,
    priorityFilter,
    carrierAgeCounts,
  ]);

  const totals: any = useMemo(() => {
    if (!filteredCarriers) return { [typeTable]: 0 };

    const result = {
      [typeTable]: 0,
      "120+": 0,
      "91-120": 0,
      "61-90": 0,
      "31-60": 0,
      "16-30": 0,
      "8-15": 0,
      "0-7": 0,
    };

    filteredCarriers.forEach((carrier: any) => {
      result[typeTable] += carrier[typeTable];

      // Add counts for each age bucket
      const carrierData = Object.values(carrierAgeCounts).find(
        (item: any) =>
          item.name.trim().toLowerCase() === carrier.name.trim().toLowerCase()
      );

      if (carrierData) {
        const ageBuckets = [
          "120+",
          "91-120",
          "61-90",
          "31-60",
          "16-30",
          "8-15",
          "0-7",
        ];
        ageBuckets.forEach((range) => {
          const bucketKey = getBucketKey(range);
          const count = carrierData[bucketKey]?.count || 0;
          result[range] += Number.parseInt(count) || 0;
        });
      }
    });

    return result;
  }, [filteredCarriers, typeTable, carrierAgeCounts]);

  const handleSort = (column: string) => {
    if (column === sortColumn) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  const formatBucket = (key: string) => {
    const bucketMap: any = {
      zerotoseven: "[0-7]",
      eighttofifteen: "[8-15]",
      sixteentothirty: "[16-30]",
      thirtyonetosixty: "[31-60]",
      sixtyonetoninety: "[61-90]",
      ninetyonetohundredandtwenty: "[91-120]",
      hundredandtwentyplus: "[120+]",
    };
    return bucketMap[key] || key;
  };

  // Helper function to get priority for an age range
  const getPriority = (ageRange: string, carrierData: any) => {
    if (!carrierData) return "";
    const bucketKey = getBucketKey(ageRange);
    return carrierData[bucketKey]?.priority || "";
  };

  const getBucketCount = (ageRange: string, carrierData: any) => {
    if (!carrierData) return "-";

    const bucketKeyMap: { [key: string]: string } = {
      "120+": "hundredandtwentyplus",
      "91-120": "ninetyonetohundredandtwenty",
      "61-90": "sixtyonetoninety",
      "31-60": "thirtyonetosixty",
      "16-30": "sixteentothirty",
      "8-15": "eighttofifteen",
      "0-7": "zerotoseven",
    };

    const bucketKey = bucketKeyMap[ageRange];
    return carrierData[bucketKey]?.count || "-";
  };

  return (
    <div className="">
      <div className="flex justify-between items-center">
        {/* <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search carriers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div> */}
      </div>
      <Table>
        <TableHeader>
          <TableRow className="bg-muted/50">
            <TableHead
              className="w-[200px] cursor-pointer"
              onClick={() => handleSort("name")}
            >
              Carrier
              {sortColumn === "name" &&
                (sortDirection === "asc" ? (
                  <ChevronUp className="inline ml-2 h-4 w-4" />
                ) : (
                  <ChevronDown className="inline ml-2 h-4 w-4" />
                ))}
            </TableHead>
            <TableHead
              className="text-right cursor-pointer"
              onClick={() => handleSort("tableType")}
            >
              {heading}
              {sortColumn === "tableType" &&
                (sortDirection === "asc" ? (
                  <ChevronUp className="inline ml-2 h-4 w-4" />
                ) : (
                  <ChevronDown className="inline ml-2 h-4 w-4" />
                ))}
            </TableHead>
            <TableHead className="text-center  ">120+</TableHead>
            <TableHead className="text-center ">91-120</TableHead>
            <TableHead className="text-center"> 61-90</TableHead>
            <TableHead className="text-center"> 31-60</TableHead>
            <TableHead className="text-center"> 16-30</TableHead>
            <TableHead className="text-center"> 8-15</TableHead>
            <TableHead className="text-center"> 0-7</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredCarriers?.map((carrier: any, index: number) => {
            const carrierData = Object.values(carrierAgeCounts).find(
              (item: any) =>
                item.name.trim().toLowerCase() ===
                carrier.name.trim().toLowerCase()
            );

            return (
              <TableRow
                key={index}
                className="hover:bg-muted/50 transition-colors"
              >
                <TableCell className="font-medium">{carrier.name}</TableCell>
                <TableCell className="text-right">
                  <span className="font-semibold text-primary bg-primary/10 px-2 py-1 rounded">
                    {carrier[typeTable]}
                  </span>
                </TableCell>
                <TableCell className="text-center">
                  {getPriority("120+", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("120+", carrierData) === "High"
                          ? "bg-red-500/80"
                          : getPriority("120+", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("120+", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("120+", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell className="text-center">
                  {getPriority("91-120", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("91-120", carrierData) === "High"
                          ? "bg-red-500/80"
                          : getPriority("91-120", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("91-120", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("91-120", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell className="text-center">
                  {getPriority("61-90", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("61-90", carrierData) === "High"
                          ? "bg-red-500/80"
                          : getPriority("61-90", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("61-90", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("61-90", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>

                <TableCell className="text-center">
                  {getPriority("31-60", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("31-60", carrierData) === "High"
                          ? "bg-red-500/80"
                          : getPriority("31-60", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("31-60", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("31-60", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell className="text-center">
                  {getPriority("16-30", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("16-30", carrierData) === "High"
                          ? "bg-red-500/80"
                          : getPriority("16-30", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("16-30", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("16-30", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell className="text-center">
                  {getPriority("8-15", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("8-15", carrierData) === "High"
                          ? "bg-red-500/80"
                          : getPriority("8-15", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("8-15", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("8-15", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell className="text-center">
                  {getPriority("0-7", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("0-7", carrierData) === "High"
                          ? "bg-red-500/80"
                          : getPriority("0-7", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("0-7", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("0-7", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
        <TableFooter>
          <TableRow className="bg-muted/50">
            <TableCell className="font-bold">Total</TableCell>
            <TableCell className="text-right font-bold text-primary">
              {totals[typeTable]}
            </TableCell>
            <TableCell className="text-center font-bold">
              {totals["120+"] > 0 ? totals["120+"] : "-"}
            </TableCell>
            <TableCell className="text-center font-bold">
              {totals["91-120"] > 0 ? totals["91-120"] : "-"}
            </TableCell>
            <TableCell className="text-center font-bold">
              {totals["61-90"] > 0 ? totals["61-90"] : "-"}
            </TableCell>
            <TableCell className="text-center font-bold">
              {totals["31-60"] > 0 ? totals["31-60"] : "-"}
            </TableCell>
            <TableCell className="text-center font-bold">
              {totals["16-30"] > 0 ? totals["16-30"] : "-"}
            </TableCell>
            <TableCell className="text-center font-bold">
              {totals["8-15"] > 0 ? totals["8-15"] : "-"}
            </TableCell>
            <TableCell className="text-center font-bold">
              {totals["0-7"] > 0 ? totals["0-7"] : "-"}
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </div>
  );
};

export default CustomTableForDP;