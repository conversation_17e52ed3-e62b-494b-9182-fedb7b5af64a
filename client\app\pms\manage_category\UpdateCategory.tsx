"use client";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { formSubmit } from "@/lib/helpers";
import { carrier_routes, category_routes, location_api } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { createCarrierSchema, createCategorySchema } from "@/lib/zodSchema";
import React, { useState } from "react";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import DialogHeading from "@/app/_component/DialogHeading";
import TriggerButton from "@/app/_component/TriggerButton";
import { useRouter } from "next/navigation";

const UpdateCategory = ({ data }: any) => {
 
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const router = useRouter();

  const { form } = useDynamicForm(createCategorySchema, {
    name: data?.category_name || "",
  });

  async function onSubmit(values: any) {
    try {
      const formData = {
        name: values.name.toUpperCase(),
      };

      const res = await formSubmit(
        `${category_routes.UPDATE_CATEGORY}/${data.id}`,
        "PUT",
        formData
      );
   
      if (res.success) {
        toast.success(res.message);
        router.refresh();
        form.reset()
        setIsDialogOpen(false);
      } else {
        toast.error(res.error || "An error occurred while adding the category.");
      }
    } catch (error) {
      toast.error("An error occurred while adding the category.");
      console.error(error);
    }
  }
  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger onClick={() => setIsDialogOpen(true)}>
          <TriggerButton type="edit" />
        </DialogTrigger>
        <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
          <DialogHeading
            title="Update Category"
            description="Update category details"
          />
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="grid grid-cols-1 gap-5">
                <FormInput
                  form={form}
                  label="Category Name"
                  name="name"
                  type="text"
                  isRequired
                />
              </div>
              <SubmitBtn
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                text="Update"
              />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdateCategory;
