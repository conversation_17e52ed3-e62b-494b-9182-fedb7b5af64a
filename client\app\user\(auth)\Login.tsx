"use client";
import { Form } from "@/components/ui/form";
import useDynamicForm from "@/lib/useDynamicForm";
import { LoginSchema } from "@/lib/zodSchema";
import React, { useState } from "react";
import FormInput from "../../_component/FormInput";
import SubmitBtn from "../../_component/SubmitBtn";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { formSubmit } from "@/lib/helpers";
import { employee_routes } from "@/lib/routePath";

const LoginUser = () => {
  const router = useRouter();
  const { form, startTransition } = useDynamicForm(LoginSchema, {
    username: "",
    password: "",
  });
  async function onSubmit(values: { username: string; password: string }) {
    try {
      const formData = {
        username: values.username,
        password: values.password,
      };

      const route = employee_routes.LOGIN_USERS;

      startTransition(async () => {
        const res = await fetch(route, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify(values),
        });
        const data = await res.json();
        //  ;
        if (data.success === true) {
          toast.success(data.message);
          router.push("/user/tracker");
        } else {
           console.log(data.error);
          toast.error(data.error || "An error occurred while login user.");
        }
      });
    } catch (error) {
      toast.error("Login failed");
      console.error(error);
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <h2 className="text-lg font-semibold text-center">User Login</h2>
          <FormInput form={form} label="Username" name="username" type="text" />
          <FormInput
            form={form}
            label="Password:"
            name="password"
            type="password"
          />
          <SubmitBtn className="w-full" text="Submit" />
        </form>
      </Form>
    </>
  );
};

export default LoginUser;
