import { Request, Response } from "express";
import { PrismaClient, FieldType } from "@prisma/client";

const prisma = new PrismaClient();

export interface AuthenticatedRequest extends Request {
  user?: { username: string };
}

// Post /api/client-custom-fields/order
export const updateCustomFieldOrder = async (req: Request, res: Response) => {
  try {
    const { client_id, custom_fields } = req.body;

    if (!client_id || !Array.isArray(custom_fields)) {
      return res
        .status(400)
        .json({ error: "Missing client_id or invalid custom_fields" });
    }

    // First, get all the custom fields to ensure they exist
    const allFields = await prisma.customField.findMany({
      where: {
        id: {
          in: custom_fields
        }
      }
    });

    // Map of field IDs to ensure they all exist
    const fieldMap = new Map(allFields.map(field => [field.id, field]));
    const validFieldIds = custom_fields.filter(id => fieldMap.has(id));

    if (validFieldIds.length === 0) {
      return res.status(400).json({ error: "No valid custom fields provided" });
    }

    // Save the arrangement in the client_custom_field_arrangements table
    const updatedArrangements = await prisma.$transaction(async (tx) => {

      // First, delete all existing arrangements for this client
      await tx.clientCustomFieldArrangement.deleteMany({
        where: { client_id: client_id }
      });

      // Then, create new arrangement entries with explicit order
      const createdArrangements = [];
      for (let i = 0; i < validFieldIds.length; i++) {
        const arrangement = await tx.clientCustomFieldArrangement.create({
          data: {
            client_id: client_id,
            custom_field_id: validFieldIds[i],
            order: i + 1, // 1-based ordering
          },
          include: {
            CustomField: {
              select: {
                id: true,
                name: true,
                type: true,
              }
            }
          }
        });
        createdArrangements.push(arrangement);
      }

      return createdArrangements;
    });

    res.status(200).json({ success: true, arrangements: updatedArrangements });
  } catch (err) {
    console.error("Error updating custom field order:", err);
    res.status(500).json({ error: "Server error", details: err });
  }
};
