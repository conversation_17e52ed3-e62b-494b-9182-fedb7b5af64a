"use client";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import FormTextarea from "@/app/_component/TextArea";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { formSubmit } from "@/lib/helpers";
import { client_routes, location_api } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { createClientSchema } from "@/lib/zodSchema";
import React, { use, useEffect, useState } from "react";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FaPlus } from "react-icons/fa";
import DialogHeading from "@/app/_component/DialogHeading";
import TriggerButton from "@/app/_component/TriggerButton";
import { useRouter } from "next/navigation";

const UpdateClient = ({ data, allBranch, allUser, allAssociate }: any) => {
  //  (allUser)
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const router = useRouter();

  console.log(data);

  const { form } = useDynamicForm(createClientSchema, {
    associate: data?.associate?.id.toString() || "",
    client_name: data?.client_name || "",
    // ownership: data?.ownership.toString() || "",
    ownership: data?.ownership?.id.toString() || "",
    branch: data?.branch?.id.toString() || "",
  });
  //

  // const branch_name = form.watch("branch");

  // useEffect(() => {
  //   if (branch_name) {
  //     fetchStates(branch_name);
  //   }
  // }, []);

  async function onSubmit(values: any) {
    try {
      const formData = {
        associateId: Number(values.associate),
        client_name: values.client_name.toUpperCase(),
        // ownership: values.ownership.toUpperCase(),
        ownership: Number(values.ownership),
        branch_id: Number(values.branch),
      };

      const res = await formSubmit(
        `${client_routes.UPDATE_CLIENT}/${data.id}`,
        "PUT",
        formData
      );

      if (res.success) {
        toast.success(res.message);
        router.refresh();
        setIsDialogOpen(false);
        form.reset();
      } else {
        toast.error(res.error || "An error occurred while adding the client.");
      }
    } catch (error) {
      toast.error("An error occurred while updating the client.");
      console.error(error);
    }
  }

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger onClick={() => setIsDialogOpen(true)} title="Update">
          <TriggerButton type="edit" />
        </DialogTrigger>

        <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
          <DialogHeading
            title="Update Client"
            description="Update client details.."
          />
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(
                (values) => onSubmit(values),
                (errors) => console.error("Validation Errors:", errors)
              )}
            >
              <div className="grid grid-cols-2 gap-5 items-center">
                <SelectComp
                  form={form}
                  label="Associate"
                  name="associate"
                  placeholder="Select Associate"
                  isRequired
                >
                  {allAssociate.map((associates: any) => (
                    <SelectItem
                      value={associates?.id.toString()}
                      key={associates.id}
                    >
                      {associates.name}
                    </SelectItem>
                  ))}
                </SelectComp>

                <FormInput
                  form={form}
                  label="Client Name"
                  name="client_name"
                  type="text"
                  isRequired
                />
              </div>

              <div className="grid grid-cols-2 gap-5 items-center">
                {/* <FormInput
                  form={form}
                  label="Ownership"
                  name="ownership"
                  type="text"
                  isRequired
                /> */}

                <SelectComp
                  form={form}
                  label="Ownership"
                  name="ownership"
                  placeholder="Select Ownership"
                  isRequired
                >
                  {allUser
                    .filter((user: any) => user.level === 3)
                    .map((user: any) => (
                      <SelectItem value={user.id?.toString()} key={user.id}>
                        {user.username}
                      </SelectItem>
                    ))}
                </SelectComp>
                <SelectComp
                  form={form}
                  label="Branch"
                  name="branch"
                  placeholder="Select Branch"
                  className="mt-3"
                  isRequired
                >
                  {allBranch.map((branch: any) => (
                    <SelectItem value={branch.id.toString()} key={branch.id}>
                      {branch.branch_name}
                    </SelectItem>
                  ))}
                </SelectComp>
              </div>

              <SubmitBtn
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                text="Submit"
              />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdateClient;
