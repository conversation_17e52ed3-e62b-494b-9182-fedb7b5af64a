import React from "react";
import { getAllData, getCookie, PermissionWrapper } from "@/lib/helpers";
import {
  carrier_routes,
  client_routes,
  daily_planning,
  employee_routes,
  workreport_routes,
} from "@/lib/routePath";
import ComingSoon from "@/app/pms/dashboard/ComingSoon";
import DashBoardHeader from "./DashBoardHeader";
import { DashBoardChart } from "./DashBoardChart";
import { DashBoardPieChart } from "./DashBoardPieChart";
import { DashBoardlegendChart } from "./DashBoardLegendChart";
import { PieChartWorkLoad } from "./PieChartWorkLoad";
import ComingSoonCard from "@/app/_component/ComingsoonCard";

const Page = async () => {
  const allEmployee = await getAllData(employee_routes.GETALL_USERS);
  // const allClient = await getAllData(client_routes.GETALL_CLIENT);
  // const allCarrier = await getAllData(carrier_routes.GETALL_CARRIER);
  // const allworkreport = await getAllData(workreport_routes.GETALL_WORKREPORT);
  const allDailyPlanning = await getAllData(
    daily_planning.GETALL_DAILY_PLANNING
  );
  // const workDone = allworkreport.filter(
  //   (report: any) => report.work_status === "FINISHED"
  // );
  // const workPending = allworkreport.filter(
  //   (report: any) => report.work_status !== "FINISHED"

  //);

  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.module
    ) || [];

  const corporationCookie = await getCookie("corporationtoken");

  // If corporationCookie doesn't exist, manually remove 'allow_all' from permissions
  // let permissions = userPermissions;
  const permissions = corporationCookie ? ["allow_all"] : userPermissions;
  return (
    <div className="px-2 mt-4 flex justify-center items-center h-full border-primary shadow-lg">
      {/* <DashBoardHeader
        allEmployee={allEmployee}
        allClient={allClient}
        allCarrier={allCarrier}
        allworkreport={allworkreport}
        allDailyPlanning={allDailyPlanning}
        workDone={workDone}
        workPending={workPending}
      />

      <div className="grid grid-cols-1 gap-2">
        <div className="w-full grid grid-cols-2 md:grid-cols-2 gap-2">
          <DashBoardPieChart />
          <PieChartWorkLoad />
        </div>
        <div className="w-full grid grid-cols-2 md:grid-cols-2 gap-2">
          <DashBoardlegendChart />

          <DashBoardChart />
        </div>
      </div> */}

      {/* <ComingSoon
        allEmployee={allEmployee}
        allClient={allClient}
        allCarrier={allCarrier}
        allworkreport={allworkreport}
        allDailyPlanning={allDailyPlanning}
      /> */}

      <ComingSoonCard />
    </div>
  );
};

export default Page;
