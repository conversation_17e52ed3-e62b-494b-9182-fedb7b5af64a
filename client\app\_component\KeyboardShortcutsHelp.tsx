"use client";

import React from "react";
import { usePathname } from "next/navigation";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Keyboard, ArrowUp, ArrowDown } from "lucide-react";

interface KeyboardShortcut {
  keys: string[];
  description: string;
  category: string;
}

interface KeyboardShortcutsHelpProps {
  isOpen: boolean;
  onClose: () => void;
}

const KeyboardShortcutsHelp: React.FC<KeyboardShortcutsHelpProps> = ({
  isOpen,
  onClose,
}) => {
  const pathname = usePathname();

  // Get page-specific shortcuts based on current route
  const getShortcutsForPage = (): KeyboardShortcut[] => {
    const commonShortcuts: KeyboardShortcut[] = [
      // Global Help
      {
        keys: ["Shift", "?"],
        description: "Show keyboard shortcuts",
        category: "Help",
      },
      {
        keys: ["Esc"],
        description: "Close dialogs/popups",
        category: "Help",
      },
    ];

    // TrackSheets specific shortcuts
    if (pathname?.includes("/trackSheets")) {
      return [
        // Form Navigation
        {
          keys: ["Tab"],
          description: "Navigate to next field",
          category: "Navigation",
        },
        {
          keys: ["Shift", "Tab"],
          description: "Navigate to previous field",
          category: "Navigation",
        },
        {
          keys: ["↑", "↓"],
          description: "Navigate between entries",
          category: "Navigation",
        },

        // Form Actions
        {
          keys: ["Enter"],
          description: "Save/Create TrackSheet",
          category: "Actions",
        },
        {
          keys: ["Ctrl", "S"],
          description: "Save/Create TrackSheet (alternative)",
          category: "Actions",
        },
        {
          keys: ["Shift", "Enter"],
          description: "Add new entry",
          category: "Actions",
        },

        ...commonShortcuts,
      ];
    }

    // Custom Fields specific shortcuts
    if (pathname?.includes("/custom_fields") || pathname?.includes("/arrange_custom_fields")) {
      return [
        // Navigation
        {
          keys: ["Tab"],
          description: "Navigate between form fields",
          category: "Navigation",
        },
        {
          keys: ["Shift", "Tab"],
          description: "Navigate to previous field",
          category: "Navigation",
        },

        // Actions
        {
          keys: ["Ctrl", "S"],
          description: "Save changes",
          category: "Actions",
        },
        {
          keys: ["Enter"],
          description: "Submit form",
          category: "Actions",
        },

        ...commonShortcuts,
      ];
    }

    // General/Default shortcuts for other pages
    return [
      // General Navigation
      {
        keys: ["Tab"],
        description: "Navigate between interactive elements",
        category: "Navigation",
      },
      {
        keys: ["Shift", "Tab"],
        description: "Navigate to previous element",
        category: "Navigation",
      },
      {
        keys: ["Enter"],
        description: "Activate focused element",
        category: "Navigation",
      },

      // General Actions
      {
        keys: ["Ctrl", "S"],
        description: "Save (where applicable)",
        category: "Actions",
      },

      ...commonShortcuts,
    ];
  };

  const shortcuts = getShortcutsForPage();

  // Get page title for context
  const getPageTitle = (): string => {
    if (pathname?.includes("/trackSheets")) {
      return "TrackSheets";
    }
    if (pathname?.includes("/custom_fields") || pathname?.includes("/arrange_custom_fields")) {
      return "Custom Fields";
    }
    return "General";
  };

  const pageTitle = getPageTitle();

  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, KeyboardShortcut[]>);

  const renderKey = (key: string) => {
    const keyIcons: Record<string, React.ReactNode> = {
      "Tab": "Tab",
      "Enter": "Enter",
      "↑": <ArrowUp className="w-3 h-3" />,
      "↓": <ArrowDown className="w-3 h-3" />,
      "Ctrl": "Ctrl",
      "Shift": "Shift",
      "Esc": "Esc",
      "S": "S",
      "?": "?",
    };

    return (
      <Badge
        variant="outline"
        className="px-2 py-1 text-xs font-mono bg-gray-100 border-gray-300 text-gray-700 min-w-[32px] justify-center"
      >
        {keyIcons[key] || key}
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg font-semibold">
            <Keyboard className="w-5 h-5 text-blue-600" />
            Keyboard Shortcuts - {pageTitle}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 mt-4">
          {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
            <div key={category}>
              <h3 className="text-sm font-semibold text-gray-900 mb-3 uppercase tracking-wide">
                {category}
              </h3>

              <div className="space-y-2">
                {categoryShortcuts.map((shortcut, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between py-2 px-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <span className="text-sm text-gray-700 flex-1">
                      {shortcut.description}
                    </span>

                    <div className="flex items-center gap-1">
                      {shortcut.keys.map((key, keyIndex) => (
                        <React.Fragment key={keyIndex}>
                          {keyIndex > 0 && (
                            <span className="text-gray-400 text-xs mx-1">+</span>
                          )}
                          {renderKey(key)}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              {category !== "Help" && <Separator className="mt-4" />}
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Pro Tip:</p>
              <p>
                Use <Badge variant="outline" className="mx-1 text-xs">Tab</Badge> to navigate
                between form fields efficiently. The + and - buttons are excluded from tab navigation
                for a smoother experience.
              </p>
            </div>
          </div>
        </div>

        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            Press <Badge variant="outline" className="mx-1 text-xs">Esc</Badge> to close this dialog
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default KeyboardShortcutsHelp;
