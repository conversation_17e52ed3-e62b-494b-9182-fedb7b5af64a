import React, { useContext, useMemo, useState } from "react";
import Column, { WorkReport } from "./column";
import { getAllData } from "@/lib/helpers";
import { workreport_routes } from "@/lib/routePath";
import { WorkReportContext } from "./WorkReportContext";
import { formatDuration } from "@/lib/swrFetching";
import { Input } from "@/components/ui/input";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import DataGridTable from "@/app/_component/DataGridTable";

const ViewWorkReport = ({ dataProp, permissions, totaldatalength }: any) => {
  const {
    fromDate,
    setFromDate,
    toDate,
    setToDate,
    deleteData,
    setDeletedData,
  } = useContext(WorkReportContext);

  // State to store the fetched data
  const [data, setData] = useState<WorkReport[]>([]);
  const [totallength, setTotallength] = useState<number>(0);
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const pathname = usePathname();
  const { replace } = useRouter();

  // const searchParams = useSearchParams();
  const pageSize = Number(searchParams.get("pageSize"));
  const page = searchParams.get("page");
  const [totals, setTotals] = useState<{
    actualNumber: number;
    timeSpent: number;
  }>({
    actualNumber: 0,
    timeSpent: 0,
  });
  // const { setFromDate, fromDate, toDate, setToDate } =
  //   useContext(WorkReportContext);
  const [previousData, setPreviousData] = useState<WorkReport[]>([]);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       const response = await getAllData(
  //         `${workreport_routes.GETALL_WORKREPORT}/?fromDate=${fromDate}&&toDate=${toDate}`
  //       );
  //       setData(response);

  //       calculateTotals(response);
  //     } catch (error) {
  //       console.error("Error fetching data:", error);
  //     }
  //   };

  //   fetchData();
  // }, [fromDate, toDate, deleteData,dataProp,setData]);

  const calculateTotals = (filteredData: WorkReport[]) => {
    const actualNumberTotal = filteredData.reduce((acc: any, item: any) => {
      const actualNumber = parseFloat(item.actual_number || "0");
      return acc + actualNumber;
    }, 0);

    const timeSpentTotal = filteredData.reduce((acc: any, item: any) => {
      const timeSpent = item.time_spent ? parseFloat(item.time_spent) : 0;
      return acc + timeSpent;
    }, 0);

    setTotals({ actualNumber: actualNumberTotal, timeSpent: timeSpentTotal });
  };

  useMemo(() => {
    if (dataProp?.data) {
      calculateTotals(dataProp.data);
    }
  }, [dataProp]);

  const totalview = (
    // <div className="fixed w-1/2 ">
    <div className="flex top-14 justify-start text-center gap-1 w-full">
      <div className="mb-3 w-[300px]  border-dashed border  tracking-widest border-gray-500 rounded-lg text-center p-2  items-center space-x-6">
        <p className="text-s">
          Time Spent:{" "}
          <span className="font-normal ">
            {formatDuration(totals.timeSpent)}
          </span>
        </p>
      </div>
      <div className="mb-3 w-[300px]  border-dashed border  tracking-widest border-gray-500 rounded-lg text-center p-2  items-center space-x-6">
        <p className="text-s">Actual Number: {totals.actualNumber}</p>
      </div>
    </div>
    // </div>
  );
  const handleDateChange = async (field: "fDate" | "tDate", value: string) => {
    if (field === "fDate") {
      setFromDate(value);
      //  (value)
      params.set("fDate", value?.toString()); // set pageSize query
      replace(`${pathname}?${params.toString()}`);
      // await fetchData(value, tDate); // Fetch using updated fDate
    } else {
      setToDate(value);
      params.set("tDate", value?.toString()); // set pageSize query
      replace(`${pathname}?${params.toString()}`);
      // await fetchData(fDate, value); // Fetch using updated tDate
    }
  };
  const filterview = (
    <div className="flex gap-2 mb-4">
      <Input
        type="date"
        value={fromDate}
        onChange={(e) => handleDateChange("fDate", e.target.value)}
        className="w-full dark:bg-gray-700 !outline-main-color"
      />
      <Input
        type="date"
        value={toDate}
        onChange={(e) => handleDateChange("tDate", e.target.value)}
        className="w-full dark:bg-gray-700 !outline-main-color"
      />
    </div>
  );
  const handleFilteredDataChange = (filteredData: WorkReport[]) => {
    if (JSON.stringify(filteredData) !== JSON.stringify(previousData)) {
      calculateTotals(filteredData);
      setPreviousData(filteredData);
    }
  };

  return (
    <div className="w-full ">
      <DataGridTable
        data={dataProp.data}
        columns={Column(permissions, setDeletedData, deleteData)}
        // filter
        // filter_column="carriername"
        showColDropDowns
        showPageEntries
        // filter2
        // filter_column2="clientname"
        filter3={true}
        filter3view={filterview}
        className="w-full"
        total={true}
        totalview={totalview}
        onFilteredDataChange={handleFilteredDataChange}
        pageSize={pageSize}
        totalPages={totallength ? totallength : totaldatalength}
      />
    </div>
  );
};

export default ViewWorkReport;
