import { isValid, parse } from "date-fns";
import { handleError } from "../../../utils/helpers";
import { getVisibility } from "../visibilty/visibiltyHelper";

//     const whereClause: { AND?: { date: { gte?: Date; lte?: Date } } } = {};

//     if (from && to) {
//       whereClause.AND = {
//         date: {
//           gte: from,
//           lte: to,
//         },
//       };
//     } else if (from) {
//       whereClause.AND.date = {
//         gte: from,
//       };
//     } else if (to) {
//       whereClause.AND.date = {
//         lte: to,
//       };
//     }

// controllers/workreportController.js
export const viewWorkreport = async (req, res) => {
  try {
    const {
      fDate,
      tDate,
      page,
      pageSize,
      Client,
      Carrier,
      Username,
      Work,
      Category,
      Type,
      Workstatus,
      ActualNumber,
      Notes,
      SwitchType
    } = req.query;

    const taskTypeMap = {
      BACKLOG: "BACKLOG",
      REGULAR: "REGULAR",
    };

    const take = Number(pageSize);
    const skip = (Number(page) - 1) * Number(pageSize);

    const from = fDate ? new Date(fDate) : null;
    const to = tDate ? new Date(tDate) : null;

    if (to) {
      to.setHours(23, 59, 59, 999); // Set end of day
    }

    const whereClause: { AND?: any[] } = { AND: [] };

    // Handle Date Range
    if (from || to) {
      const dateCondition: any = { date: {} };
      if (from) dateCondition.date.gte = from;
      if (to) dateCondition.date.lte = to;
      whereClause.AND.push(dateCondition);
    }

    // Search conditions for filtering
    const searchConditions: any[] = [];

    if (Client) {
      const clientList = Client.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: clientList.map((clientName) => ({
          client: {
            client_name: {
              contains: clientName,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (Carrier) {
      const carrierList = Carrier.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: carrierList.map((carrier) => ({
          carrier: {
            name: {
              contains: carrier,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (Username) {
      const userList = Username.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: userList.map((user) => ({
          user: {
            username: {
              contains: user,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (Work) {
      const workList = Work.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: workList.map((work) => ({
          work_type: {
            work_type: {
              contains: work,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (Category) {
      const categoryList = Category.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: categoryList.map((category) => ({
          category: {
            category_name: {
              contains: category,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (Type) {
      const typeList = Type.split(",").map((item) => item.trim());
      const normalizedType = Type.toUpperCase();
      searchConditions.push({
        OR: typeList.map((type) => ({
          task_type: {
            in: Object.values(taskTypeMap).filter((value) =>
              value.toLowerCase().includes(type.toLowerCase())
            ),
          },
        })),
      });
    }

    if(SwitchType) {
      const normalizedSwitchType = SwitchType.toUpperCase();
      const switchTypeMap = {
        INT: "INT",
        EXT: "EXT",
      };

      searchConditions.push({
        switch_type: {
          in: Object.values(switchTypeMap).filter((value) =>
            value.toLowerCase().includes(normalizedSwitchType.toLowerCase())
          ),
        },
      });
    }
    
    if (Workstatus) {
      const normalizedWorkStatus = Workstatus.toUpperCase();
      const workStatusMap = {
        STARTED: "STARTED",
        PAUSED: "PAUSED",
        RESUMED: "RESUMED",
        FINISHED: "FINISHED",
      };

      searchConditions.push({
        work_status: {
          in: Object.values(workStatusMap).filter((value) =>
            value.toLowerCase().includes(normalizedWorkStatus.toLowerCase())
          ),
        },
      });
    }

    if (ActualNumber) {
      const actualNumberList = ActualNumber.split(",")
        .map((item) => parseInt(item.trim(), 10))
        .filter((num) => !isNaN(num)); // Remove any invalid numbers
    
      if (actualNumberList.length > 0) {
        searchConditions.push({
          actual_number: {
            in: actualNumberList, // Matches any of the values
          },
        });
      }
    }
    

    if (Notes) {
      const notesList = Notes.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: notesList.map((note) => ({
          notes: {
            contains: note,
            mode: "insensitive",
          },
        })),
      });
    }
    // if (Client) {
    //   const clientList = Client.split(',').map(item => item.trim());

    //   searchConditions.push({
    //     OR: clientList.map(clientName => ({
    //       client: {
    //         client_name: {
    //           contains: clientName,
    //           mode: "insensitive",
    //         },
    //       },
    //     })),
    //   });
    // }

    // Combine search conditions into the whereClause
    if (searchConditions.length > 0) {
      whereClause.AND.push({
        AND: searchConditions, // Add all the conditions under OR
      });
    }

    // Fetching data with filters and pagination
    const data = await prisma.workReport.findMany({
      where: whereClause,
      take: page ? take : undefined,
      skip: page ? skip : undefined,
      include: {
        user: true,
        client: true,
        carrier: true,
        work_type: true,
        category: true,
      },
      orderBy: {
        id: "desc",
      },
    });

    // Counting total rows to support pagination
    const datalength = await prisma.workReport.count({
      where: whereClause,
    });

    // Return data if available
    if (data.length > 0) {
      return res.status(200).json({ data, datalength });
    }

    // Return empty response if no data is found
    return res.status(400).json([]);
  } catch (error) {
    return handleError(res, error);
  }
};

// export const viewWorkreport = async (req, res) => {
//   try {
//     const {
//       fDate,
//       tDate,
//       Client,
//       Carrier,
//       Username,
//       Work,
//       Category,
//       Type,
//       Workstatus,
//       ActualNumber,
//       Notes,
//       page,
//       pageSize,
//     } = req.query;

//     const taskTypeMap = {
//       BACKLOG: "BACKLOG",
//       REGULAR: "REGULAR",
//     };

//     const from = fDate ? new Date(fDate) : null;
//     const to = tDate ? new Date(tDate) : null;

//     if (to) {
//       to.setHours(23, 59, 59, 999); // End of the day
//     }

//     // Pagination: skip is the offset, take is the number of items per page
//     const skip = (Number(page) - 1) * Number(pageSize);
//     const take = Number(pageSize);

//     // Initialize the whereClause as an empty object
//     const whereClause = { AND: [] };

//     // Add date range filter if from or to date is specified
//     if (from || to) {
//       const dateCondition: any = { date: {} };
//       if (from) dateCondition.date.gte = from;
//       if (to) dateCondition.date.lte = to;
//       whereClause.AND.push(dateCondition);
//     }

//     // Define the search conditions for various fields
//     const searchConditions = [];

//     if (Client) {
//       searchConditions.push({
//         client: {
//           client_name: {
//             contains: Client,
//             mode: "insensitive",
//           },
//         },
//       });
//     }

//     if (Carrier) {
//       searchConditions.push({
//         carrier: {
//           name: {
//             contains: Carrier,
//             mode: "insensitive",
//           },
//         },
//       });
//     }

//     if (Username) {
//       searchConditions.push({
//         user: {
//           username: {
//             contains: Username,
//             mode: "insensitive",
//           },
//         },
//       });
//     }

//     if (Work) {
//       searchConditions.push({
//         work_type: {
//           work_type: {
//             contains: Work,
//             mode: "insensitive",
//           },
//         },
//       });
//     }

//     if (Category) {
//       searchConditions.push({
//         category: {
//           category_name: {
//             contains: Category,
//             mode: "insensitive",
//           },
//         },
//       });
//     }

//     if (Type) {
//       const normalizedType = Type.toUpperCase();
//       searchConditions.push({
//         task_type: {
//           in: Object.values(taskTypeMap).filter((value) =>
//             value.toLowerCase().includes(normalizedType.toLowerCase())
//           ),
//         },
//       });
//     }

//     if (Workstatus) {
//       const normalizedWorkStatus = Workstatus.toUpperCase();
//       const workStatusMap = {
//         STARTED: "STARTED",
//         PAUSED: "PAUSED",
//         RESUMED: "RESUMED",
//         FINISHED: "FINISHED",
//       };

//       searchConditions.push({
//         work_status: {
//           in: Object.values(workStatusMap).filter((value) =>
//             value.toLowerCase().includes(normalizedWorkStatus.toLowerCase())
//           ),
//         },
//       });
//     }

//     if (ActualNumber) {
//       const actualNumberInt = parseInt(ActualNumber, 10);
//       if (!isNaN(actualNumberInt)) {
//         searchConditions.push({
//           actual_number: actualNumberInt,
//         });
//       }
//     }

//     if (Notes) {
//       searchConditions.push({
//         notes: {
//           contains: Notes,
//           mode: "insensitive",
//         },
//       });
//     }

//     // Combine all search conditions
//     if (searchConditions.length > 0) {
//       whereClause.AND.push({ AND: searchConditions });
//     }

//     // Fetch data from the database with pagination and filtering
//     const data = await prisma.workReport.findMany({
//       where: whereClause,
//       take: take,
//       skip: skip,
//       include: {
//         user: true,
//         client: true,
//         carrier: true,
//         work_type: true,
//         category: true,
//       },
//       orderBy: {
//         id: "desc", // Ordering by ID descending, change as needed
//       },
//     });

//     // Get total row count for pagination (with applied filters)
//     const totalRows = await prisma.workReport.count({
//       where: whereClause,
//     });

//     // Return paginated data and total rows
//     return res.status(200).json({
//       data,
//       totalRows,
//     });
//   } catch (error) {
//     console.error("Error fetching work report:", error);
//     return res.status(500).json({ error: "Server error" });
//   }
// };

export const viewclientWorkreport = async (req, res) => {
  try {
    const client_name = req.body.client_name;

    if (!client_name) {
      return res.status(400).json({ message: "Client name is required" });
    }

    const client = await prisma.client.findFirst({
      where: { client_name: client_name },
    });

    if (!client) {
      return res.status(404).json({ message: "Client not found" });
    }

    const data = await prisma.workReport.findMany({
      where: { client_id: client.id },
      include: {
        user: true,
        carrier: true,
        work_type: true,
      },
    });

    if (data.length > 0) {
      return res.status(200).json({ success: true, data: data });
    }

    return res
      .status(404)
      .json({ message: "No work reports found for this client" });
  } catch (error) {
    return handleError(res, error);
  }
};

export const viewuserWorkreport = async (req, res) => {
  try {
    const username = req.body.username;

    if (!username) {
      return res.status(400).json({ message: "user name is required" });
    }

    const user = await prisma.user.findFirst({
      where: { username: username },
    });

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    const data = await prisma.workReport.findMany({
      where: { user_id: user.id },
      include: {
        client: true,
        carrier: true,
        work_type: true,
      },
    });

    if (data.length > 0) {
      return res.status(200).json({ success: true, data: data });
    }

    return res
      .status(404)
      .json({ message: "No work reports found for this user" });
  } catch (error) {
    return handleError(res, error);
  }
};

export const viewworktypeWorkreport = async (req, res) => {
  try {
    const work_type = req.body.work_type;

    if (!work_type) {
      return res.status(400).json({ message: "work type is required" });
    }

    const workType = await prisma.workType.findFirst({
      where: { work_type: work_type },
    });

    if (!workType) {
      return res.status(404).json({ message: "Work type not found" });
    }

    const data = await prisma.workReport.findMany({
      // where: { work_type : workType.work_type },
      where: {
        work_type: {
          work_type: work_type,
        },
      },
      include: {
        client: true,
        carrier: true,
        user: true,
      },
    });

    if (data.length > 0) {
      return res.status(200).json({ success: true, data: data });
    }

    return res
      .status(404)
      .json({ message: "No work reports found for this workType" });
  } catch (error) {
    return handleError(res, error);
  }
};

export const viewcategoryWorkreport = async (req, res) => {
  try {
    const { category } = req.body;

    const validCategories = ["AUDIT", "ENTRY", "REPORT"];
    if (!category || !validCategories.includes(category)) {
      return res.status(400).json({ message: "Valid category is required" });
    }

    const data = await prisma.workReport.findMany({
      where: { category: category },
      include: {
        user: true,
        client: true,
        carrier: true,
        work_type: true,
      },
    });

    if (data.length > 0) {
      return res.status(200).json({ success: true, data });
    }

    return res
      .status(404)
      .json({ message: "No work reports found for this category" });
  } catch (error) {
    return handleError(res, error);
  }
};

export const getCurrentUserWorkReport = async (req, res) => {
  try {
    const {
      fDate,
      tDate,
      page,
      pageSize,
      Username,
      Client,
      Carrier,
      WorkType,
      Category,
      Type,
      // StartTime,
      // EndTime,
      Workstatus,
      // TimeSpent,
      ActualNumber,
      Notes,
    } = req.query;

    const taskTypeMap = {
      BACKLOG: "BACKLOG",
      REGULAR: "REGULAR",
    };

    const take = Number(pageSize);
    const skip = (Number(page) - 1) * Number(pageSize);

    const from = fDate ? new Date(fDate) : null;
    const to = tDate ? new Date(tDate) : null;
    if (to) {
      to.setHours(23, 59, 59, 999);
    }

    const whereClause: { AND?: any[] } = { AND: [] };

    if (from || to) {
      const dateCondition: any = { date: {} };
      if (from) dateCondition.date.gte = from;
      if (to) dateCondition.date.lte = to;
      whereClause.AND.push(dateCondition);
    }

    const searchConditions: any[] = [];

    if (Client) {
      const clientList = Client.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: clientList.map((clientName) => ({
          client: {
            client_name: {
              contains: clientName,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (Carrier) {
      const carrierList = Carrier.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: carrierList.map((carrier) => ({
          carrier: {
            name: {
              contains: carrier,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (Username) {
      const userList = Username.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: userList.map((user) => ({
          user: {
            username: {
              contains: user,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (WorkType) {
      const workTypeList = WorkType.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: workTypeList.map((workType) => ({
          work_type: {
            work_type: {
              contains: workType,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (Category) {
      const categoryList = Category.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: categoryList.map((category) => ({
          category: {
            category_name: {
              contains: Category,
              mode: "insensitive",
            },
          },
        })),
      });
    }
    // if (Type) {
    //   searchConditions.push({
    //     task_type: {
    //       equals: Type,
    //     },
    //   });
    // }

    if (Type) {
      const typeList = Type.split(",").map((item) => item.trim());
      const normalizedType = Type.toUpperCase();
      searchConditions.push({
        OR: typeList.map((type) => ({
          task_type: {
            in: Object.values(taskTypeMap).filter((value) =>
              value.toLowerCase().includes(type.toLowerCase())
            ),
          },
        })),
      });
    }

    // if (StartTime) {
    //   const [hour, minute, period] = StartTime.split(':');
    //   const timeInt = parseInt(hour, 10);

    //   const adjustedHour = (period === 'PM' && timeInt !== 12) ? timeInt + 12 : timeInt;

    //   const targetStartTime = new Date();
    //   targetStartTime.setHours(adjustedHour, minute, 0, 0);

    //   const endStartTime = new Date(targetStartTime);
    //   endStartTime.setMinutes(targetStartTime.getMinutes() + 59);

    //   searchConditions.push({
    //     start_time: {
    //       gte: targetStartTime,
    //       lte: endStartTime,
    //     },
    //   });
    // }

    // if (EndTime) {
    //   searchConditions.push({
    //     finish_time: {
    //       contains: EndTime,
    //       mode: "insensitive",
    //     },
    //   });
    // }

    // if (Workstatus) {
    //   searchConditions.push({
    //     work_status: {
    //       equals: Workstatus,
    //     },
    //   });
    // }
    if (Workstatus) {
      const normalizedWorkStatus = Workstatus.toUpperCase();

      const workStatusMap = {
        STARTED: "STARTED",
        PAUSED: "PAUSED",
        RESUMED: "RESUMED",
        FINISHED: "FINISHED",
      };

      searchConditions.push({
        work_status: {
          in: Object.values(workStatusMap).filter((value) =>
            value.toLowerCase().includes(normalizedWorkStatus.toLowerCase())
          ),
        },
      });
    }

    // if (TimeSpent) {
    //   searchConditions.push({
    //     time_spent: {
    //       contains: TimeSpent,
    //       mode: "insensitive",
    //     },
    //   });
    // }

    // if (ActualNumber) {
    //   searchConditions.push({
    //     actual_number: {
    //       contains: ActualNumber,
    //       mode: "insensitive",
    //     },
    //   });
    // }

    if (ActualNumber) {
      const actualNumberList = ActualNumber.split(",")
        .map((item) => parseInt(item.trim(), 10))
        .filter((num) => !isNaN(num)); // Remove any invalid numbers
    
      if (actualNumberList.length > 0) {
        searchConditions.push({
          actual_number: {
            in: actualNumberList, // Matches any of the values
          },
        });
      }
    }
    if (Notes) {
      const notesList = Notes.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: notesList.map((note) => ({
          notes: {
            contains: note,
            mode: "insensitive",
          },
        })),
      });
    }

    // If any search condition exists (Client, Carrier, or search), push them under OR
    // if (searchConditions.length > 0) {
    whereClause.AND.push({
      AND: searchConditions, // Add all the conditions under OR
    });
    // }
    // Get the current user's ID and check visibility
    const userId = req.user_id;
    const user_ids = await getVisibility(userId, "WorkReport");
    if (!Array.isArray(user_ids)) {
      return res.status(400).json({ message: "Invalid user_ids format" });
    }
    const userIdArray = [...new Set(user_ids.map((u) => u.user_id))];
    const data = await prisma.workReport.findMany({
      where: {
        ...whereClause,
        user_id: { in: userIdArray }, // Filter records where user_id is in userIdArray
      },
      take: page ? take : undefined,
      skip: page ? skip : undefined,
      select: {
        id: true,
        work_status: true,
        date: true,
        client: true,
        carrier: true,
        work_type: true,
        user: true,
        category: true,
        planning_nummbers: true,
        expected_time: true,
        actual_number: true,
        start_time: true,
        finish_time: true,
        time_spent: true,
        task_type: true,
        notes: true,
        pause: true,
        resume: true,
        carrier_id: true,
        client_id: true,
        work_type_id: true,
      },
      orderBy: {
        id: "desc", // Sort by ID descending (latest reports first)
      },
    });

    // Get total count for pagination
    const datalength = await prisma.workReport.count({
      where: {
        user_id: { in: userIdArray },
        ...whereClause,
      },
    });

    // Return the data and total count
    if (data.length > 0) {
      return res.status(200).json({ data, datalength });
    } else {
      return res.status(400).json([]); // Return empty array if no data found
    }
  } catch (error) {
    return handleError(res, error); // Handle any errors
  }
};

export const getCurrentUserWorkReportStatusCount = async (req, res) => {
  try {
    const userId = req.user_id;
    const userIds = await getVisibility(userId, "WorkReport");

    if (!Array.isArray(userIds)) {
      return res.status(400).json({ message: "Invalid user_ids format" });
    }

    const userIdArray = [...new Set(userIds.map((u) => u.user_id))];

    const completedTasksCount = await prisma.workReport.count({
      where: {
        user_id: { in: userIdArray },
        work_status: "FINISHED",
      },
    });
    const activeTasksCount = await prisma.workReport.count({
      where: {
        user_id: { in: userIdArray },
        work_status: {
          in: ["RESUMED", "PAUSED"],
        },
      },
    });
    return res.status(200).json({
      taskCompleted: completedTasksCount,
      activeTasks: activeTasksCount,
    });
  } catch (error) {
    return handleError(res, error);
  }
};
