import { cn } from "@/lib/utils";
import { Plus } from "lucide-react";
import { MdEditDocument } from "react-icons/md";

type TriggerProp = {
  text?: string;
  type: "add" | "edit" | undefined;
  className?: string;
};

function TriggerButton({ text, type }: TriggerProp) {
  return (
    <>
      {type === "add" ? (
        <p className="py-1.5 gap-2 bg-white border  hover:to-main-color-foreground  px-3 justify-center  text-black uppercase font-semibold rounded-md flex items-center text-sm">
          <Plus className="w-5 h-5 font-extrabold" />
          {text}
        </p>
      ) : (
        <MdEditDocument className="text-gray-500  w-5 h-5" />
      )}
    </>
  );
}

export default TriggerButton;
