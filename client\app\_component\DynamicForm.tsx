import { Form } from "@/components/ui/form";
import { formSubmit, formSubmitOne } from "@/lib/helpers";
import { showingToast } from "@/lib/clientHelpers";
import React, { startTransition, useState } from "react";

import useDynamicForm from "@/lib/useDynamicForm";
import SubmitBtn from "./SubmitBtn";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTrigger,
} from "@/components/ui/dialog";
import TriggerButton from "./TriggerButton";
import CloseDialog from "./CloseDialog";
import DialogHeading from "./DialogHeading";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";

interface FormProps {
  title: string;
  description: string;
  schema: any;
  fields: any;
  endpoint: string;
  initialData?: any;
}
const DynamicForm = ({
  title,
  description,
  schema,
  fields,
  endpoint,
  initialData,
}: FormProps) => {
  interface Province {
    value: string;
    label: string;
  }
  const router = useRouter();
  const [addressApi, setAddressApi] = useState<any>([]);
  const [selectedFromProvince, setSelectedFromProvince] = useState<Province[]>(
    initialData?.from_province?.map((item: string) => ({
      value: item,
      label: item,
    })) || []
  );
  const [selectedToProvince, setSelectedToProvince] = useState<Province[]>(
    initialData?.to_province?.map((item: string) => ({
      value: item,
      label: item,
    })) || []
  );
  const defaultDate = initialData?.effective_date
    ? new Date(initialData.effective_date).toISOString().slice(0, 10)
    : "";
  const { form, open, setOpen, startTransition, isPending } = useDynamicForm(
    schema,
    {
      ...fields,
      ...initialData,
      ...{ effective_date: defaultDate },
    }
  );
  const onSubmit = (values: any) => {
    // Validate values
    const validUpperCaseValues = Object.entries(values).reduce(
      (acc: any, [key, value]) => {
        // if (typeof value === "string") {
        //   acc[key] = value.toUpperCase();
        // } else {
        acc[key] = value;
        // }
        return acc;
      },
      {}
    );

    if (
      Object.values(validUpperCaseValues).some(
        (value) =>
          typeof value === "string" &&
          ["N/A", "NULL", "NOT APPLICABLE"].includes(value)
      )
    ) {
      showingToast({
        data: { message: "Please enter valid data." },
        form: form,
        setOpen: setOpen,
      });
      return;
    }

    const fromProvince = selectedFromProvince.map((item) => item.value);
    const toProvince = selectedToProvince.map((item) => item.value);

    startTransition(() => {
      formSubmitOne({
        url: endpoint,
        request: initialData ? "PUT" : "POST",
        formData: {
          ...validUpperCaseValues,
          from_province: fromProvince,
          to_province: toProvince,
          addressApi: [addressApi],
        },
        revPath: "/freight_audit/freight_setup",
      }).then((data) => {
        showingToast({ data: data, form: form, setOpen: setOpen });
        setSelectedFromProvince([]);
        setSelectedToProvince([]);
      });
    });

    form.reset();
    router.refresh();
  };

  return (
    <>
      <Dialog
        open={open}
        onOpenChange={() => {
          setOpen(true);
        }}
      >
        <DialogTrigger>
          {initialData ? (
            <TriggerButton type="edit" />
          ) : (
            <TriggerButton type="add" text={`${title}`} />
          )}
        </DialogTrigger>
        <DialogContent className="max-w-5xl dark:bg-gray-800 overflow-auto cbar max-h-[90vh] add-rate-row">
          <CloseDialog form={form} setOpen={setOpen} />
          <DialogHeader>
            <DialogHeading title={title} description={description} />

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <div
                  className={cn(
                    fields?.length === 1
                      ? "grid grid-cols"
                      : "grid grid-cols-2 gap-5"
                  )}
                >
                  {/* {fields.map((field: any) => {
                    return (
                      <FieldComponent
                        key={field.name}
                        field={field}
                        form={form}
                        selectedFromProvince={selectedFromProvince}
                        selectedToProvince={selectedToProvince}
                        setSelectedFromProvince={setSelectedFromProvince} // Pass setter
                        setSelectedToProvince={setSelectedToProvince}
                        setAddressApi={setAddressApi}
                      />
                    );
                  })} */}
                </div>
                <div className="py-3">
                  <SubmitBtn isSubmitting={isPending} />
                </div>
              </form>
            </Form>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
};
export default DynamicForm;
