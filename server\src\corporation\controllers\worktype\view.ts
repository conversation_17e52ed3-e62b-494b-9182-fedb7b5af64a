import { handleError } from "../../../utils/helpers";

export const viewWorktype = async (req, res) => {
  try {
    const data = await prisma.workType.findMany({
      select: {
        category: true,
        work_type: true,
        is_work_carrier_specific: true,
        does_it_require_planning_number: true,
        is_backlog_regular_required: true,
        id: true,
        category_id: true,
      },
      orderBy: {
        id: "desc",
      },
    });
    if (data) {
      return res.status(200).json(data);
    }
    return res.status(400).json([]);
  } catch (error) {
    return handleError(res, error);
  }
};
