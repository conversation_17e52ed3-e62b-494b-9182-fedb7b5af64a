model LegrandMapping {
    id                     String       @id @default(uuid())
    businessUnit           String?      @db.VarChar()
    legalName              String?      @db.VarChar()
    customeCode            String?      @db.VarChar()
    shippingBillingName    String?      @db.VarChar()
    shippingBillingAddress String?      @db.VarChar()
    location               String?      @db.VarChar()
    zipPostal              String?
    aliasCity              String?      @db.VarChar()
    aliasShippingNames     String?      @db.VarChar()
    corporationId          Int?
    corporation            Corporation? @relation(fields: [corporationId], references: [corporation_id], onDelete: Cascade)
    createdAt              DateTime     @default(now())
    updatedAt              DateTime     @default(now()) @updatedAt

    @@map("legrand_mappings")
}
