import { CONSTRAINTS } from "cron/dist/constants";
import { createItem } from "../../../utils/operation";
import xlsx from "xlsx";
import fs from "fs";
import { handleError } from "../../../utils/helpers";

export const createClient = async (req, res) => {
  const { corporation_id } = req;

  const fields = {
    associateId: Number(req.body.associate),
    client_name: req.body.client_name,
    // ownership: req.body.ownership,
    ownership_id: Number(req.body.ownership),
    branch_id: Number(req.body.branch),
    corporation_id: Number(corporation_id),
  };

  await createItem({
    model: "client",
    fieldName: "id",
    fields: fields,
    res: res as Response,
    req: req,
    successMessage: "client has been created",
  });
};

// export const createClient = async (req, res) => {
//   try {
//     const { corporation_id } = req;

//     const {
//       associate: associateId,
//       client_name,
//       users, // expect this to be an array of user IDs
//       branch,
//     } = req.body;

//     if (!Array.isArray(users) || users.length === 0) {
//       return res.status(400).json({ error: "At least one user must be selected." });
//     }

//     const client = await prisma.client.create({
//       data: {
//         client_name: client_name.trim().toUpperCase(),
//         associate: { connect: { id: Number(associateId) } },
//         branch: { connect: { id: Number(branch) } },
//         corporation: { connect: { corporation_id: Number(corporation_id) } },
//         users: {
//           connect: users.map((id) => ({ id: Number(id) })),
//         },
//       },
//     });

//     return res.status(200).json({
//       success: true,
//       message: "Client has been created",
//       data: client,
//     });
//   } catch (error) {
//     return handleError(res, error);
//   }
// };

export const excelClient = async (req, res) => {
  try {
    
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    const allowedExtensions = [".xlsx", ".xls"];
    const fileExtension = req.file.originalname.split(".").pop();
    if (!allowedExtensions.includes(`.${fileExtension}`)) {
      return res
        .status(400)
        .json({ error: "Invalid file type. Only Excel files are allowed." });
    }

    const workbook = xlsx.readFile(req.file.path);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const import_data: any = xlsx.utils.sheet_to_json(worksheet, { header: 1 });
    

    const header = ["Client Name", "Associate", "Ownership", "Branch"];
    const errors = [];

    if (
      !import_data[0].includes(header[0]) ||
      !import_data[0].includes(header[1]) ||
      !import_data[0].includes(header[2]) ||
      !import_data[0].includes(header[3])
    ) {
      errors.push(
        "Invalid file format. Please ensure the file has the correct headers."
      );
      res
        .status(200)
        .json({
          message:
            "Invalid file format. Please ensure the file has the correct headers.",
          errors,
        });
      return;
    }

    const userData = await Promise.all(
      import_data.slice(1).map(async (row: any, index) => {

        const branch = await prisma.branch.findFirst({
          where: { branch_name: row[3] },
          select: { id: true },
        });
        

        const associate = await prisma.associate.findFirst({
          where: { name: row[1] },
          select: { id: true },
        });
        

        const user = await prisma.user.findFirst({
          where: { username: row[2] },
          select: { id: true },
        });
        

        const corporation = await prisma.corporation.findFirst({
          where: { corporation_id: row[4] },
          select: { corporation_id: true },
        });
        

        const existingClient = await prisma.client.findFirst({
          where: {
            OR: [{ client_name: row[0] }],
          },
        });

        if (existingClient) {
          return { error: `Client Name ${row[0]} already exists.` };
        }

        return {
          corporation_id: Number(corporation.corporation_id),
          associateId: associate.id || null,
          client_name: row[0],
          ownership: user.id || null,
          branch_id: branch.id || null,
        };
      })
    );

    // const validClient = userData.filter((client: any) => !client.error);
    const errorMessages = userData
      .filter((client: any) => client.error)
      .map((client: any) => client.error);

    const validClient = userData.filter(
      (client: any) =>
        !client.error &&
        client.client_name &&
        client.associateId &&
        client.user_id &&
        client.branch_id
    );
    

    // if (validClient.length === 0) {
    //   errors.push('No valid client data found. Ensure all required fields are filled correctly.');
    //   return null;
    // }

    await prisma.client.createMany({
      data: validClient,
    });

    return res.status(200).json({
      message: "Client data imported successfully",
      errors: errorMessages,
      successCount: validClient.length,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ errors: "Internal Server Error" });
  } finally {
    fs.unlinkSync(req.file.path);
  }
};
