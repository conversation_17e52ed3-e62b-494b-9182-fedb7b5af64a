"use client";
import { useState, useTransition } from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
const useDynamicForm = <T extends z.ZodType<any, any>>(
  schema: T,
  defaultValues: z.infer<T>
) => {
  const [isPending, startTransition] = useTransition();
  const [open, setOpen] = useState(false);
  const form: UseFormReturn<z.infer<T>> = useForm<z.infer<T>>({
    defaultValues,
    resolver: zodResolver(schema),
  });
  return {
    open,
    setOpen,
    isPending,
    startTransition,
    form,
  };
};

export default useDynamicForm;
