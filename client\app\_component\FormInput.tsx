import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import React, { useState } from "react";
import { RiEyeCloseFill } from "react-icons/ri";
import { BsEyeFill } from "react-icons/bs";
import { IconType } from "react-icons"; // Import IconType for type checking
import { DateTime } from "luxon";

type FormProps = {
  form: any;
  name: string;
  label: string;
  placeholder?: string;
  className?: string;
  type: string;
  disabledStr?: string;
  disable?: boolean;
  isRequired?: boolean;
  isEntryPage?: boolean;
  allowNegative?: boolean;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  ref?: any;
  min?: string;
  max?: string;
  icon?: {
    Component: IconType; // The icon component from react-icons
    position?: "left" | "right"; // Optional position, defaults to left
    className?: string; // Optional additional styling for the icon
    onClick?: () => void; // Optional click handler
  };
};

const FormInput = ({
  form,
  name,
  label,
  placeholder,
  className,
  type,
  isRequired,
  disable,
  isEntryPage,
  allowNegative,
  onBlur,
  ref,
  icon,
  min,
  max,
}: FormProps) => {
  const [show, setShow] = useState(false);
  const now = DateTime.now(); // Local time zone
  const currentDate = now.toISODate(); // Get the current date in the format YYYY-MM-DD
  const currentTime = now.toFormat("HH:mm"); // Get the current time in HH:mm format


  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
          const value = parseFloat(e.target.value);
          if (type === "number" && value < 0) {
            e.target.value = "0"; // Reset to 0 if the value is negative
            field.onChange("0"); // Directly update the field value
          }
          if (onBlur) onBlur(e);
        };

        // Password input with show/hide functionality
        if (type === "password") {
          return (
            <FormItem
              className={cn`${
                isEntryPage ? "space-y-0.5 " : "md:mb-2 space-y-0.5"
              } ${className}`}
            >
              <FormLabel
                className={`${
                  isEntryPage ? "md:text-xs" : "md:text-base"
                } text-gray-800 dark:text-gray-300 whitespace-nowrap cursor-text`}
              >
                {label}
                {isRequired && <span className="text-red-500">*</span>}
              </FormLabel>
              <FormControl>
                <div className="relative flex items-center">
                  {/* Optional Left Icon */}
                  {icon && icon.position !== "right" && (
                    <icon.Component
                      className={cn(
                        "absolute left-2 z-10 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-500",
                        icon.className
                      )}
                      onClick={icon.onClick}
                    />
                  )}
                  <Input
                    type={show ? "text" : "password"}
                    placeholder={placeholder}
                    ref={ref}
                    {...field}
                    className={cn(
                      "bg-gray-200 dark:bg-gray-700 border-none dark:border-gray-700 placeholder:text-gray-400 dark:placeholder:text-gray-100/50 !outline-main-color",
                      icon && icon.position !== "right" ? "pl-8" : "",
                      icon && icon.position === "right" ? "pr-14" : "pr-8"
                    )}
                    min={min}
                  />
                  {/* Eye Icon for Show/Hide */}
                  {show ? (
                    <BsEyeFill
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 cursor-pointer w-5 h-5 dark:text-gray-200 text-gray-600"
                      onClick={() => setShow(!show)}
                    />
                  ) : (
                    <RiEyeCloseFill
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 cursor-pointer w-5 h-5 dark:text-gray-200 text-gray-600"
                      onClick={() => setShow(!show)}
                    />
                  )}
                  {/* Optional Right Icon */}
                  {icon && icon.position === "right" && (
                    <icon.Component
                      className={cn(
                        "absolute right-8 z-10 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-500",
                        icon.className
                      )}
                      onClick={icon.onClick}
                    />
                  )}
                </div>
              </FormControl>
              <FormMessage
                className={`${
                  isEntryPage ? "text-xs tracking-wider" : "tracking-wider"
                }`}
              />
            </FormItem>
          );
        }

        // Regular input with optional icon
        return (
          <FormItem
            className={cn`${
              isEntryPage ? "space-y-0.5 " : "md:mb-2 space-y-0.5"
            } ${className}`}
          >
            <FormLabel
              className={`${
                isEntryPage ? "md:text-xs" : "md:text-base"
              } text-gray-800 dark:text-gray-300 whitespace-nowrap cursor-text`}
            >
              {label}
              {isRequired && <span className="text-red-500">*</span>}
            </FormLabel>
            <FormControl>
              <div className="relative flex items-center">
                {icon && icon.position !== "right" && (
                  <icon.Component
                    className={cn(
                      "absolute left-2 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-500",
                      icon.className
                    )}
                    onClick={icon.onClick}
                  />
                )}
                <Input
                  {...field}
                  type={type}
                  placeholder={placeholder}
                  disabled={disable}
                  onBlur={
                    type === "number" && !allowNegative ? handleBlur : onBlur
                  }
                  min={type === "number" && !allowNegative ? "0" : type === "date" ? min : undefined}
                  max={
                    type === "date"
                      ? max || currentDate
                      : type === "time"
                      ? field.value && field.value.split("T")[1] === currentDate
                        ? currentTime
                        : undefined
                      : undefined
                  }
                  step="any" // Allows decimal values if needed
                  className={cn(
                    `bg-gray-200 dark:bg-gray-700 border-none dark:border-gray-700 placeholder:text-gray-400 dark:placeholder:text-gray-100/50 outline-none focus:!outline-main-color`,
                    icon ? (icon.position !== "right" ? "pl-8" : "pr-8") : "",
                    isEntryPage ? "h-7 text-xs" : ""
                  )}
                />
                {icon && icon.position === "right" && (
                  <icon.Component
                    className={cn(
                      "absolute right-2 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-500",
                      icon.className
                    )}
                    onClick={icon.onClick}
                  />
                )}
              </div>
            </FormControl>
            <FormMessage
              className={`${
                isEntryPage ? "text-xs tracking-wider" : "tracking-wider"
              }`}
            />
          </FormItem>
        );
      }}
    />
  );
};

export default FormInput;
