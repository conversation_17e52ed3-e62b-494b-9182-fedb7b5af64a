"use client";
import DataTable from "@/app/_component/DataTable";
import { Column } from "./Column";

const ViewAssociate = ({
  data,
  permissions,
}: {
  data: any;
  permissions: string[];
}) => {
  return (
    <div className="w-full">
      <DataTable
        data={data}
        columns={Column(permissions)}
        showColDropDowns
        showPageEntries
        className="w-full"
      />
    </div>
  );
};

export default ViewAssociate;
