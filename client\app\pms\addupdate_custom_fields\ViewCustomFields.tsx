"use client";
import React, { useState } from "react";
import ServerSideDataTable from "@/app/_component/ServerSideDataTable";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import DataGridTable from "@/app/_component/DataGridTable";
import { column } from "./column";

export const ViewCustomFields = ({
  alldata,
  permissions,
  allBranch,
  allUser,
  allAssociate,
  userData
}: any) => {
  //  (typeof Column)
  //  (permissions)
  //
  //  (allCarrier);

  const [totallength, setTotallength] = useState<number>(0);
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const pathname = usePathname();
   const router = useRouter();

  // Handle the new data structure from backend with pagination
  const customFieldsData = Array.isArray(alldata?.data) ? alldata.data : Array.isArray(alldata) ? alldata : [];
  const pageSize = Number(searchParams.get("pageSize")) || 50;
  const totalDataLength = alldata?.datalength || customFieldsData.length;
  const totalPages = alldata?.totalPages || Math.ceil(totalDataLength / pageSize);

  return (
    <div>
      <DataGridTable
        data={customFieldsData}
        columns={column(permissions, allBranch, allUser, allAssociate, userData, router)}
        showColDropDowns
        showPageEntries
        className="w-full"
        total={true}
        showSearchColumn={true}
        pageSize={pageSize}
        totalPages={totalPages}
      />
    </div>
  );
};

export default ViewCustomFields;