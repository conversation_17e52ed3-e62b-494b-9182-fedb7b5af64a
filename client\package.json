{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "@shadcn/ui": "^0.0.4", "@tanstack/react-table": "^8.20.6", "@types/recharts": "^1.8.29", "ag-grid-community": "^34.0.0", "ag-grid-enterprise": "^34.0.0", "ag-grid-react": "^34.0.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "framer-motion": "^12.0.6", "html2canvas": "^1.4.1", "http-server": "^14.1.1", "js-cookie": "^3.0.5", "lucide-react": "^0.469.0", "luxon": "^3.5.0", "motion": "^12.10.5", "multer": "^1.4.5-lts.1", "next": "14.2.2", "next-themes": "^0.4.4", "papaparse": "^5.5.1", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-csv": "^2.2.2", "react-d3-tree": "^3.6.6", "react-date-range": "^2.0.1", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-icons": "^5.4.0", "react-select": "^5.10.1", "react-table": "^7.8.0", "react-time-picker": "^7.0.0", "react-toastify": "^11.0.5", "recharts": "^2.15.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "swr": "^2.3.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/papaparse": "^5.3.15", "@types/react": "^18", "@types/react-csv": "^1.1.10", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}