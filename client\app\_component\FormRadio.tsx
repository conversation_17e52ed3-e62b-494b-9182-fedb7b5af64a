import React from "react"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { cn } from "@/lib/utils"

type Option = {
  label: string
  value: string
}

type FormRadioProps = {
  form: any
  name: string
  label: string
  options: Option[]
  className?: string
  isRequired?: boolean
  isEntryPage?: boolean
  disable?: boolean
}

const FormRadio = ({ form, name, label, options, className, isRequired, isEntryPage, disable }: FormRadioProps) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn(isEntryPage ? "mb-1 space-y-0.5 " : "md:mb-3 space-y-0.5", className)}>
          <FormLabel
            className={`${
              isEntryPage ? "md:text-xs" : "md:text-base"
            } text-gray-800 dark:text-gray-300 whitespace-nowrap cursor-text`}
          >
            {label}
            {isRequired && <span className="text-red-500">*</span>}
          </FormLabel>
          <FormControl>
            <RadioGroup
              onValueChange={field.onChange}
              defaultValue={field.value}
              className="flex"
              disabled={disable}
            >
              {options.map((option) => (
                <FormItem key={option.value} className="flex items-center space-x-1">
                  <FormControl>
                    <RadioGroupItem value={option.value} />
                  </FormControl>
                  <FormLabel className="font-normal pr-2 ">{option.label}</FormLabel>
                </FormItem>
              ))}
            </RadioGroup>
          </FormControl>
          <FormMessage className={`${isEntryPage ? "text-xs tracking-wider" : "tracking-wider"}`} />
        </FormItem>
      )}
    />
  )
}

export default FormRadio

