"use client";
import { useState, useRef } from "react";
import { getCookie, getAllData } from "@/lib/helpers"; 
import { employee_routes } from "@/lib/routePath"; 
import { toast } from "sonner"; 

export const useSession = (userData: any) => {
  const [isSessionValid, setIsSessionValid] = useState(true);
  const [isLogoutConfirmationOpen, setIsLogoutConfirmationOpen] = useState(false);
  const initialCheckDone = useRef(false);

  const checkSessionToken = async () => {
    if (!userData?.id) return false;
    // Skip if initial check already done
    if (!initialCheckDone.current && userData?.id) {
      initialCheckDone.current = true;
    }
    
    const sessionToken = await getCookie("token"); 

    try {
      const sessionData = await getAllData(employee_routes.GETALL_SESSION); 

      if (sessionData) {
        const matchedSession = sessionData.find(
          (i: any) => i.user_id === userData.id
        );

        if (matchedSession) {
          if (sessionToken !== matchedSession.session_token) {
            setIsSessionValid(false); 
            setIsLogoutConfirmationOpen(true);
            // Directly call handleLogout here instead of using useEffect
            handleLogout();
            return false;
          } else {
            setIsSessionValid(true); 
            setIsLogoutConfirmationOpen(false); 
            return true;
          }
        } else {
          setIsSessionValid(false); 
          setIsLogoutConfirmationOpen(false);
          // Directly call handleLogout here
          handleLogout();
          return false; 
        }
      } else {
        setIsSessionValid(false); 
        setIsLogoutConfirmationOpen(false);
        // Directly call handleLogout here
        handleLogout();
        return false; 
      }
    } catch (error) {
      console.error("Error checking session token:", error); 
      setIsSessionValid(false);
      setIsLogoutConfirmationOpen(true);
      // Directly call handleLogout here
      handleLogout();
      return false; 
    }
  };

  const handleLogout = async () => {
    const userToken = await getCookie("token");
    if (userToken) {
      const logoutUrl = employee_routes.LOGOUT_USERS; 
      try {
        const response = await fetch(logoutUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({ cookieTobeDeleted: userToken }), 
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            toast.success(data.message); 
            window.location.href = "/"; 
          } else {
            toast.error(data.error || "An error occurred during logout."); 
          }
        }
      } catch (error) {
        console.error("Logout error:", error); 
        toast.error("An error occurred while logging out."); 
      }
    } else {
      toast.error("No session cookie found."); 
    }
  };

  return {
    isSessionValid, 
    setIsSessionValid, 
    isLogoutConfirmationOpen, 
    setIsLogoutConfirmationOpen,
    checkSessionToken, 
    handleLogout, 
  };
};