"use client";
import React from 'react'
import Link from 'next/link'

const HomePage = () => {
  return (
    <></>
    // <div className="min-h-screen">
    //   {/* Navigation */}
    //   {/* <nav className="fixed w-full bg-white/80 backdrop-blur-md z-50 border-b">
    //     <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    //       <div className="flex items-center justify-between h-16">
    //         <div className="flex-shrink-0">
    //           <h1 className="text-2xl font-bold text-indigo-600">WorkTracker</h1>
    //         </div>
    //         <div className="hidden md:block">
    //           <div className="ml-10 flex items-center space-x-4">
    //             <Link href="#features" className="text-gray-700 hover:text-indigo-600 px-3 py-2">
    //               Features
    //             </Link>
    //             <Link href="#how-it-works" className="text-gray-700 hover:text-indigo-600 px-3 py-2">
    //               How it Works
    //             </Link>
    //             <Link href="#pricing" className="text-gray-700 hover:text-indigo-600 px-3 py-2">
    //               Pricing
    //             </Link>
    //           </div>
    //         </div>
    //         <div className="flex items-center space-x-4">
    //           <Link href="/login" className="text-gray-700 hover:text-indigo-600 px-3 py-2">
    //             Login
    //           </Link>
    //           <Link 
    //             href="/signup" 
    //             className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
    //           >
    //             Get Started
    //           </Link>
    //         </div>
    //       </div>
    //     </div>
    //   </nav> */}

    //   {/* Hero Section */}
    //   <section className="pt-32 pb-20 bg-gradient-to-b from-indigo-50 to-white">
    //     <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    //       <div className="text-center">
    //         <motion.h1 
    //           initial={{ opacity: 0, y: 20 }}
    //           animate={{ opacity: 1, y: 0 }}
    //           className="text-5xl font-bold  text-gray-900 mb-6"
    //         >
    //           Track, Plan, and Excel in Your <br />
    //           <span className="text-indigo-600 mt-6">Corporate Journey</span>
    //         </motion.h1>
    //         <motion.p 
    //           initial={{ opacity: 0, y: 20 }}
    //           animate={{ opacity: 1, y: 0 }}
    //           transition={{ delay: 0.2 }}
    //           className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto"
    //         >
    //           Streamline your daily work planning and track progress effortlessly. 
    //           Built for modern corporations that value productivity and transparency.
    //         </motion.p>
    //         <motion.div 
    //           initial={{ opacity: 0, y: 20 }}
    //           animate={{ opacity: 1, y: 0 }}
    //           transition={{ delay: 0.4 }}
    //           className="flex justify-center gap-4"
    //         >
    //           <Link 
    //             href="/signup" 
    //             className="bg-indigo-600 text-white px-8 py-3 rounded-lg hover:bg-indigo-700 transition-colors text-lg"
    //           >
    //             Start Free Trial
    //           </Link>
    //           <Link 
    //             href="#demo" 
    //             className="bg-white text-indigo-600 px-8 py-3 rounded-lg hover:bg-gray-50 transition-colors text-lg border border-indigo-600"
    //           >
    //             Watch Demo
    //           </Link>
    //         </motion.div>
    //       </div>
    //     </div>
    //   </section>

    //   {/* Features Section */}
    //   <section id="features" className="py-20">
    //     <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    //       <h2 className="text-3xl font-bold text-center mb-12">Why Choose WorkTracker?</h2>
    //       <div className="grid md:grid-cols-3 gap-8">
    //         {[
    //           {
    //             icon: "📊",
    //             title: "Real-time Progress Tracking",
    //             description: "Monitor your tasks and projects in real-time with intuitive dashboards and reports."
    //           },
    //           {
    //             icon: "🎯",
    //             title: "Smart Goal Setting",
    //             description: "Set and track both personal and team goals with our intelligent goal-setting framework."
    //           },
    //           {
    //             icon: "🤝",
    //             title: "Team Collaboration",
    //             description: "Seamlessly collaborate with team members, share updates, and stay aligned on objectives."
    //           }
    //         ].map((feature, index) => (
    //           <motion.div
    //             key={index}
    //             initial={{ opacity: 0, y: 20 }}
    //             whileInView={{ opacity: 1, y: 0 }}
    //             transition={{ delay: index * 0.2 }}
    //             className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
    //           >
    //             <div className="text-4xl mb-4">{feature.icon}</div>
    //             <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
    //             <p className="text-gray-600">{feature.description}</p>
    //           </motion.div>
    //         ))}
    //       </div>
    //     </div>
    //   </section>

    //   {/* How It Works Section */}
    //   <section id="how-it-works" className="py-20 bg-gray-50">
    //     <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    //       <h2 className="text-3xl font-bold text-center mb-12">How It Works</h2>
    //       <div className="grid md:grid-cols-4 gap-8">
    //         {[
    //           { step: "1", title: "Sign Up", description: "Create your account in minutes" },
    //           { step: "2", title: "Set Goals", description: "Define your objectives and milestones" },
    //           { step: "3", title: "Track Progress", description: "Monitor your daily achievements" },
    //           { step: "4", title: "Get Insights", description: "Analyze performance and improve" }
    //         ].map((item, index) => (
    //           <motion.div
    //             key={index}
    //             initial={{ opacity: 0, x: 20 }}
    //             whileInView={{ opacity: 1, x: 0 }}
    //             transition={{ delay: index * 0.2 }}
    //             className="text-center"
    //           >
    //             <div className="w-12 h-12 bg-indigo-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
    //               {item.step}
    //             </div>
    //             <h3 className="text-xl font-semibold mb-2">{item.title}</h3>
    //             <p className="text-gray-600">{item.description}</p>
    //           </motion.div>
    //         ))}
    //       </div>
    //     </div>
    //   </section>

    //   {/* CTA Section */}
    //   <section className="py-20 bg-indigo-600 text-white">
    //     <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    //       <h2 className="text-3xl font-bold mb-6">Ready to Transform Your Work Experience?</h2>
    //       <p className="text-xl mb-8 text-indigo-100">Join thousands of professionals who have already improved their productivity.</p>
    //       <Link 
    //         href="/signup" 
    //         className="bg-white text-indigo-600 px-8 py-3 rounded-lg hover:bg-gray-100 transition-colors text-lg inline-block"
    //       >
    //         Get Started Now
    //       </Link>
    //     </div>
    //   </section>

    //   {/* Footer */}
    //   <footer className="bg-gray-900 text-gray-300 py-12">
    //     <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    //       <div className="grid md:grid-cols-4 gap-8">
    //         <div>
    //           <h3 className="text-white text-lg font-semibold mb-4">WorkTracker</h3>
    //           <p className="text-sm">Making work tracking and planning effortless for modern corporations.</p>
    //         </div>
    //         <div>
    //           <h4 className="text-white text-lg font-semibold mb-4">Product</h4>
    //           <ul className="space-y-2 text-sm">
    //             <li><Link href="#features">Features</Link></li>
    //             <li><Link href="#pricing">Pricing</Link></li>
    //             <li><Link href="#demo">Demo</Link></li>
    //           </ul>
    //         </div>
    //         <div>
    //           <h4 className="text-white text-lg font-semibold mb-4">Company</h4>
    //           <ul className="space-y-2 text-sm">
    //             <li><Link href="/about">About Us</Link></li>
    //             <li><Link href="/contact">Contact</Link></li>
    //             <li><Link href="/careers">Careers</Link></li>
    //           </ul>
    //         </div>
    //         <div>
    //           <h4 className="text-white text-lg font-semibold mb-4">Legal</h4>
    //           <ul className="space-y-2 text-sm">
    //             <li><Link href="/privacy">Privacy Policy</Link></li>
    //             <li><Link href="/terms">Terms of Service</Link></li>
    //           </ul>
    //         </div>
    //       </div>
    //       <div className="border-t border-gray-800 mt-12 pt-8 text-sm text-center">
    //         <p>&copy; 2024 WorkTracker. All rights reserved.</p>
    //       </div>
    //     </div>
    //   </footer>
    // </div>
  )
}

export default HomePage
