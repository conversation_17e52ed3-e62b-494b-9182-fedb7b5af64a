import DeleteRow from "@/app/_component/DeleteRow";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import { branch_routes } from "@/lib/routePath";
import UpdateBranch from "./UpdateBranch";
import { ColumnDef } from "@tanstack/react-table";

export interface Branch {
    branch_name: string;
    id: any;
}


export const Column = (permissions: string[]): ColumnDef<Branch>[] => [
    {
        accessorKey: "branch_name",
        header: "Branch Name",
    },
    {
        accessorKey: "action",
        header: "Action",
        id: "action",
        cell: ({ row }) => {
            const branch = row?.original;


            return (
                <div className="flex items-center">
                    <PermissionWrapper
                        permissions={permissions}
                        requiredPermissions={["update-branch"]}
                    >
                        <UpdateBranch data={branch} />
                    </PermissionWrapper>

                    <PermissionWrapper
                        permissions={permissions}
                        requiredPermissions={["delete-branch"]}
                    >
                        <DeleteRow
                            route={`${branch_routes.DELETE_BRANCH}/${branch?.id}`}
                        />
                    </PermissionWrapper>
                </div>
            );
        },
    },
];