import { getVisibility } from "../corporation/controllers/visibilty/visibiltyHelper";
import { genericSearch } from "./genericSearch";

// Move static mappings outside function - defined once, not on every request
const MODEL_TO_TABLE_MAP: Record<string, string> = {
  workreport: "WorkReport",
  dailyplanning: "DailyPlanning",
  // add more mappings if needed
};

// Models that require user ID filtering
const MODELS_USING_USER_ID = new Set(["workreport", "dailyplanning"]);

export const genericSearchController = async (req, res) => {
  try {
    // Validate required parameters early
    const model = req.params.model as any;
    if (!model) {
      return res.status(400).json({ message: "Model parameter is required" });
    }

    const normalizedModel = model.toLowerCase();

    // Parse and extract query parameters with defaults
    const {
      page: pageParam = "1",
      pageSize: pageSizeParam = "50",
      select: selectParam,
      fDate,
      tDate,
      dateField = "date",
      includeRelations: includeRelationsQuery,
      noPagination,
      ...rest
    } = req.query;

    let includeRelations: boolean | string[] = false;

    if (includeRelationsQuery) {
      if (includeRelationsQuery === "true") {
        includeRelations = true; // Include all relations
      } else if (typeof includeRelationsQuery === "string") {
        // Split into array of relation names
        includeRelations = includeRelationsQuery
          .split(",")
          .map((rel) => rel.trim());
      }
    }
    // Parse pagination parameters
    const parsedPage = noPagination
      ? undefined
      : parseInt(pageParam as string, 10);
    const parsedPageSize = noPagination
      ? undefined
      : parseInt(pageSizeParam as string, 10);

    // Parse select parameter once
    let parsedSelect;
    if (selectParam) {
      try {
        parsedSelect = JSON.parse(selectParam as string);
      } catch (e) {
        return res
          .status(400)
          .json({ message: "Invalid select parameter format" });
      }
    }

    // Efficiently build filters and relatedFilters
    const filters: Record<string, any> = {};
    const relatedFilters: Record<string, Record<string, any>> = {};

    // Process filter parameters in one loop
    for (const key in rest) {
      const value = rest[key];
      if (!value) continue;

      if (key.includes(".")) {
        const [relation, field] = key.split(".");
        relatedFilters[relation] = relatedFilters[relation] || {};
        relatedFilters[relation][field] = value;
      } else {
        filters[key] = value;
      }
    }

    // Build date range object only if dates are provided
    const dateRange =
      fDate || tDate
        ? {
            field: dateField,
            from: fDate ? new Date(fDate as string) : undefined,
            to: tDate ? new Date(`${tDate}T23:59:59.999Z`) : undefined,
          }
        : undefined;

    // User ID filtering optimization
    let userIdFilter: number[] | undefined = undefined;

    const userId = req.user_id;
    const shouldApplyUserIdFilter =
      userId && MODELS_USING_USER_ID.has(normalizedModel);

    if (shouldApplyUserIdFilter) {
      const dbTableName =
        MODEL_TO_TABLE_MAP[normalizedModel] ?? normalizedModel;
      try {
        const userIds = await getVisibility(userId, dbTableName);
        if (!Array.isArray(userIds)) {
          return res.status(400).json({ message: "Invalid user_ids format" });
        }
        // Use Set for efficient deduplication
        userIdFilter = [...new Set(userIds.map((u) => u.user_id))];
      } catch (visibilityError) {
        console.error("Visibility check error:", visibilityError);
        return res.status(500).json({ message: "Error checking permissions" });
      }
    }

    // Execute search with all parameters prepared
    const { data, datalength } = await genericSearch({
      model,
      filters,
      relatedFilters,
      page: parsedPage,
      pageSize: parsedPageSize,
      dateRange,
      select: parsedSelect,
      includeRelations: includeRelations,
      userIdFilter,
    });

    return res.status(200).json({ data, datalength });
  } catch (error) {
    console.error("Generic Search Error:", error);
    return res.status(500).json({
      error: "Internal Server Error",
      message: error instanceof Error ? error.message : String(error),
    });
  }
};
