import { handleError } from "../../../utils/helpers";

export const getCustomFieldsForClient = async (req, res) => {
  try {
    const clientId = req.params.id;
    if (!clientId) {
      return res.status(400).json({ error: 'clientId is required' });
    }

    const clientCustomFields = await prisma.clientCustomFieldArrangement.findMany({
      where: { client_id: Number(clientId) },
      include: {
        CustomField: {
          select: {
            id: true,
            name: true,
            type: true,
            autoOption: true,
          }
        },
      },
      orderBy: {
        order: 'asc'
      }
    });

    if (clientCustomFields) {
      const allCustomFields = clientCustomFields.flatMap(ccf => ccf.CustomField);

      return res.status(200).json({ custom_fields: allCustomFields });
    }

    return res.status(400).json({ custom_fields: [] });
  } catch (error) {
    return handleError(res, error);
  }
};
