"use client";

import { useState, useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Search, ChevronUp, ChevronDown } from "lucide-react";
import { Badge } from "@/components/ui/badge";

const ViewTwoTenTable = ({ dailyPlanningDetailsTwoTenError }: any) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortColumn, setSortColumn] = useState("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  const { data, carrierAgeCounts } = dailyPlanningDetailsTwoTenError;

  const aggregatedData = useMemo(() => {
    const acc: any = {};

    data?.forEach((item: any) => {
      item?.DailyPlanningDetails?.forEach((detail: any) => {
        const carrierKey = `${detail.carrier.name}_${detail.carrier_id}`;

        if (!acc[carrierKey]) {
          acc[carrierKey] = {
            name: detail.carrier.name,
            two_ten_error: 0,
            two_ten_m_f: 0,
            two_ten_success: 0,
            two_ten_hold: 0,
            two_ten_import_additional: 0,
            two_ten_manual_match: 0,
            two_ten_total: 0,
          };
        }

        acc[carrierKey].two_ten_error += detail.two_ten_error || 0;
        acc[carrierKey].two_ten_m_f += detail.two_ten_m_f || 0;
        acc[carrierKey].two_ten_success += detail.two_ten_success || 0;
        acc[carrierKey].two_ten_hold += detail.two_ten_hold || 0;
        acc[carrierKey].two_ten_import_additional +=
          detail.import_additional || 0;
        acc[carrierKey].two_ten_manual_match +=
          detail.two_ten_manual_match || 0;
        acc[carrierKey].two_ten_total +=
          (detail.two_ten_m_f || 0) +
          (detail.two_ten_success || 0) +
          (detail.two_ten_hold || 0) +
          (detail.import_additional || 0) +
          (detail.two_ten_error || 0);
      });
    });

    return acc;
  }, [data]);

  const filteredCarriers = useMemo(() => {
    return Object.values(aggregatedData)
      .filter((carrier: any) =>
        carrier.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .sort((a: any, b: any) => {
        const getValue = (obj: any) => {
          switch (sortColumn) {
            case "name":
              return obj.name;
            case "error":
              return obj.two_ten_error;
            case "matchFail":
              return obj.two_ten_m_f;
            case "success":
              return obj.two_ten_success;
            case "hold":
              return obj.two_ten_hold;
            case "additional":
              return obj.two_ten_import_additional;
            case "manualMatch":
              return obj.two_ten_manual_match;
            case "total":
              return obj.two_ten_total;
            default:
              return obj.name;
          }
        };

        const aValue = getValue(a);
        const bValue = getValue(b);

        if (typeof aValue === "string") {
          return sortDirection === "asc"
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }

        return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
      });
  }, [aggregatedData, searchTerm, sortColumn, sortDirection]);

  const totals: any = useMemo(() => {
    return filteredCarriers.reduce(
      (acc: any, carrier: any) => {
        acc.two_ten_error += carrier.two_ten_error;
        acc.two_ten_m_f += carrier.two_ten_m_f;
        acc.two_ten_success += carrier.two_ten_success;
        acc.two_ten_hold += carrier.two_ten_hold;
        acc.two_ten_import_additional += carrier.two_ten_import_additional;
        acc.two_ten_manual_match += carrier.two_ten_manual_match;
        acc.two_ten_total += carrier.two_ten_total;
        return acc;
      },
      {
        two_ten_error: 0,
        two_ten_m_f: 0,
        two_ten_success: 0,
        two_ten_hold: 0,
        two_ten_import_additional: 0,
        two_ten_manual_match: 0,
        two_ten_total: 0,
      }
    );
  }, [filteredCarriers]);

  const handleSort = (column: string) => {
    if (column === sortColumn) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  const formatBucket = (key: string) => {
    const bucketMap: any = {
      zerotoseven: "[0-7]",
      eighttofifteen: "[8-15]",
      sixteentothirty: "[16-30]",
      thirtyonetosixty: "[31-60]",
      sixtyonetoninety: "[61-90]",
      ninetyonetohundredandtwenty: "[91-120]",
      hundredandtwentyplus: "[120+]",
    };
    return bucketMap[key] || key;
  };

  const getBucketKey = (ageRange: string) => {
    const bucketKeyMap: { [key: string]: string } = {
      "120+": "hundredandtwentyplus",
      "91-120": "ninetyonetohundredandtwenty",
      "61-90": "sixtyonetoninety",
      "31-60": "thirtyonetosixty",
      "16-30": "sixteentothirty",
      "8-15": "eighttofifteen",
      "0-7": "zerotoseven",
    };
    return bucketKeyMap[ageRange];
  };

  // Helper function to get priority for an age range
  const getPriority = (ageRange: string, carrierData: any) => {
    if (!carrierData) return "";
    const bucketKey = getBucketKey(ageRange);
    return carrierData[bucketKey]?.priority || "";
  };

  const getBucketCount = (ageRange: string, carrierData: any) => {
    if (!carrierData) return "-";

    const bucketKeyMap: { [key: string]: string } = {
      "120+": "hundredandtwentyplus",
      "91-120": "ninetyonetohundredandtwenty",
      "61-90": "sixtyonetoninety",
      "31-60": "thirtyonetosixty",
      "16-30": "sixteentothirty",
      "8-15": "eighttofifteen",
      "0-7": "zerotoseven",
    };

    const bucketKey = bucketKeyMap[ageRange];
    return carrierData[bucketKey]?.count || "-";
  };

  return (
    <div className="">
      {/* <div className="flex justify-between items-center">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search carriers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div> */}
      <div>
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50">
              <TableHead
                className="w-[200px] cursor-pointer"
                onClick={() => handleSort("name")}
              >
                Carrier{" "}
                {sortColumn === "name" &&
                  (sortDirection === "asc" ? (
                    <ChevronUp className="inline ml-2 h-4 w-4" />
                  ) : (
                    <ChevronDown className="inline ml-2 h-4 w-4" />
                  ))}
              </TableHead>
              <TableHead className="text-right">Error</TableHead>
              <TableHead className="text-right">Match Fail</TableHead>
              <TableHead className="text-right">Success</TableHead>
              <TableHead className="text-right">Hold</TableHead>
              <TableHead className="text-right">Import Additional</TableHead>
              <TableHead className="text-right">Manual Match</TableHead>
              <TableHead className="text-right">Total</TableHead>
              <TableHead className="text-center  ">120+</TableHead>
              <TableHead className="text-center ">91-120</TableHead>
              <TableHead className="text-center"> 61-90</TableHead>
              <TableHead className="text-center"> 31-60</TableHead>
              <TableHead className="text-center"> 16-30</TableHead>
              <TableHead className="text-center"> 8-15</TableHead>
              <TableHead className="text-center"> 0-7</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCarriers.map((carrier: any, index: number) => {
              const carrierData =
                carrierAgeCounts &&
                Object.values(carrierAgeCounts).find(
                  (item: any) =>
                    item.name.trim().toLowerCase() ===
                    carrier.name.trim().toLowerCase()
                );

              return (
                <TableRow
                  key={index}
                  className="hover:bg-muted/50 transition-colors"
                >
                  <TableCell className="font-medium">{carrier.name}</TableCell>
                  <TableCell className="text-right">
                    {carrier.two_ten_error}
                  </TableCell>
                  <TableCell className="text-right">
                    {carrier.two_ten_m_f}
                  </TableCell>
                  <TableCell className="text-right">
                    {carrier.two_ten_success}
                  </TableCell>
                  <TableCell className="text-right">
                    {carrier.two_ten_hold}
                  </TableCell>
                  <TableCell className="text-right">
                    {carrier.two_ten_import_additional}
                  </TableCell>
                  <TableCell className="text-right">
                    {carrier.two_ten_manual_match}
                  </TableCell>
                  <TableCell className="text-right">
                    <span className="font-semibold text-primary bg-primary/10 px-2 py-1 rounded">
                      {carrier.two_ten_total}
                    </span>
                  </TableCell>
                  <TableCell className="text-center">
                    {getPriority("120+", carrierData) ? (
                      <Badge
                        className={`text-center ${
                          getPriority("120+", carrierData) === "High"
                            ? "bg-red-500"
                            : getPriority("120+", carrierData) === "Medium"
                            ? "bg-orange-400"
                            : getPriority("120+", carrierData) === "Low"
                            ? "bg-green-500"
                            : ""
                        }`}
                      >
                        {getBucketCount("120+", carrierData)}
                      </Badge>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell className="text-center">
                    {getPriority("91-120", carrierData) ? (
                      <Badge
                        className={`text-center ${
                          getPriority("91-120", carrierData) === "High"
                            ? "bg-red-500"
                            : getPriority("91-120", carrierData) === "Medium"
                            ? "bg-orange-400"
                            : getPriority("91-120", carrierData) === "Low"
                            ? "bg-green-500"
                            : ""
                        }`}
                      >
                        {getBucketCount("91-120", carrierData)}
                      </Badge>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell className="text-center">
                    {getPriority("61-90", carrierData) ? (
                      <Badge
                        className={`text-center ${
                          getPriority("61-90", carrierData) === "High"
                            ? "bg-red-500"
                            : getPriority("61-90", carrierData) === "Medium"
                            ? "bg-orange-400"
                            : getPriority("61-90", carrierData) === "Low"
                            ? "bg-green-500"
                            : ""
                        }`}
                      >
                        {getBucketCount("61-90", carrierData)}
                      </Badge>
                    ) : (
                      "-"
                    )}
                  </TableCell>

                  <TableCell className="text-center">
                    {getPriority("31-60", carrierData) ? (
                      <Badge
                        className={`text-center ${
                          getPriority("31-60", carrierData) === "High"
                            ? "bg-red-500"
                            : getPriority("31-60", carrierData) === "Medium"
                            ? "bg-orange-400"
                            : getPriority("31-60", carrierData) === "Low"
                            ? "bg-green-500"
                            : ""
                        }`}
                      >
                        {getBucketCount("31-60", carrierData)}
                      </Badge>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell className="text-center">
                    {getPriority("16-30", carrierData) ? (
                      <Badge
                        className={`text-center ${
                          getPriority("16-30", carrierData) === "High"
                            ? "bg-red-500"
                            : getPriority("16-30", carrierData) === "Medium"
                            ? "bg-orange-400"
                            : getPriority("16-30", carrierData) === "Low"
                            ? "bg-green-500"
                            : ""
                        }`}
                      >
                        {getBucketCount("16-30", carrierData)}
                      </Badge>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell className="text-center">
                    {getPriority("8-15", carrierData) ? (
                      <Badge
                        className={`text-center ${
                          getPriority("8-15", carrierData) === "High"
                            ? "bg-red-500"
                            : getPriority("8-15", carrierData) === "Medium"
                            ? "bg-orange-400"
                            : getPriority("8-15", carrierData) === "Low"
                            ? "bg-green-500"
                            : ""
                        }`}
                      >
                        {getBucketCount("8-15", carrierData)}
                      </Badge>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell className="text-center">
                    {getPriority("0-7", carrierData) ? (
                      <Badge
                        className={`text-center ${
                          getPriority("0-7", carrierData) === "High"
                            ? "bg-red-500"
                            : getPriority("0-7", carrierData) === "Medium"
                            ? "bg-orange-400"
                            : getPriority("0-7", carrierData) === "Low"
                            ? "bg-green-500"
                            : ""
                        }`}
                      >
                        {getBucketCount("0-7", carrierData)}
                      </Badge>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
          <TableFooter>
            <TableRow className="bg-muted/50">
              <TableCell className="font-bold">Total</TableCell>
              <TableCell className="text-right font-bold">
                {totals.two_ten_error}
              </TableCell>
              <TableCell className="text-right font-bold">
                {totals.two_ten_m_f}
              </TableCell>
              <TableCell className="text-right font-bold">
                {totals.two_ten_success}
              </TableCell>
              <TableCell className="text-right font-bold">
                {totals.two_ten_hold}
              </TableCell>
              <TableCell className="text-right font-bold">
                {totals.two_ten_import_additional}
              </TableCell>
              <TableCell className="text-right font-bold">
                {totals.two_ten_manual_match}
              </TableCell>
              <TableCell className="text-right font-bold">
                {totals.two_ten_total}
              </TableCell>
              <TableCell />
              <TableCell />
              <TableCell />
            </TableRow>
          </TableFooter>
        </Table>
      </div>
    </div>
  );
};

export default ViewTwoTenTable;
