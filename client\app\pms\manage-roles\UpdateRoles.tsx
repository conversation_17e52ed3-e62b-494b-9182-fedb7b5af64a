"use client";
import React, { useEffect, useState } from "react";
import { z } from "zod";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { FaPlus } from "react-icons/fa";
import CloseDialog from "@/app/_component/CloseDialog";
import DialogHeading from "@/app/_component/DialogHeading";
import FormInput from "@/app/_component/FormInput";
import SubmitBtn from "@/app/_component/SubmitBtn";
import SelectComp from "@/app/_component/SelectComp";
import CheckBoxesComp from "@/app/_component/CheckBoxComp";
import { RolesSelected } from "./RoleSelected";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTrigger,
} from "@/components/ui/dialog";
import { AddRolesFormSchema } from "@/lib/zodSchema";
import { rolespermission_routes } from "@/lib/routePath";
import { formSubmit } from "@/lib/helpers";
import { showingToast } from "@/lib/clientHelpers";
import useDynamicForm from "@/lib/useDynamicForm";
import { SelectItem } from "@/components/ui/select";
import { Form } from "@/components/ui/form";
import TriggerButton from "@/app/_component/TriggerButton";

const UpdateRoles = ({ client, getAllPermission }: any) => {
  const ids = client.role_permission?.map(
    (rolePerm: any) => rolePerm.permission.id
  );

  const { open, setOpen, isPending, startTransition, form } = useDynamicForm(
    AddRolesFormSchema,
    {
      name: client.name || "",
      permission: ids || [],
      action: "",
    }
  );

  const [modules, setModules] = useState<string[]>([]);
  const [checkBoxData, setCheckBoxData] = useState<any[]>([]);
  const router = useRouter();

  const selectedModule = form.watch("action");
  const selectedPermissions = form.watch("permission");

  // Parse getAllPermission to extract unique modules
  useEffect(() => {
    const moduleKeys = Object.keys(getAllPermission);
    const uniqueModules = Array.from(
      new Set(
        moduleKeys.map((key) =>
          getAllPermission[key][0]?.module
            ? getAllPermission[key][0].module
            : key
        )
      )
    );
    setModules(uniqueModules);
  }, [getAllPermission]);

  // Update actions (checkBoxData) based on the selected module
  useEffect(() => {
    if (selectedModule) {
      const actionsForModule = Object.values(getAllPermission)
        .flat()
        .filter((action: any) => action.module === selectedModule);
      setCheckBoxData(
        actionsForModule.map((action: any) => ({
          permission_id: action.id,
          action: action.action,
        }))
      );
    } else {
      setCheckBoxData([]);
    }
  }, [selectedModule, getAllPermission]);

  // Reset checkBoxData when dialog is closed
  useEffect(() => {
    if (!open) {
      setCheckBoxData([]);
    }
  }, [open]);

  const onSubmit = async (value: z.infer<typeof AddRolesFormSchema>) => {
    startTransition(() => {
      formSubmit(
        `${rolespermission_routes.UPDATE_ROLE}/${client.id}`,
        "PUT",
        value
      ).then((data) => {
        if (data.success) {
          toast.success(data.message || "Role created successfully");
          router.refresh();
          form.reset()
          showingToast({ data, form, setOpen });
        } else {
          toast.error(data.message || "Something went wrong");
        }
      });
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger title="Update">
        <TriggerButton type="edit" />
      </DialogTrigger>
      <DialogContent className="max-w-4xl dark:bg-gray-800">
        {/* <CloseDialog form={form} setOpen={setOpen} /> */}
        <DialogHeader>
          <DialogHeading
            title="Update Role"
            description="Update Role and Permissions.."
          />
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(
                (values) => onSubmit(values),
                (errors) => console.error("Validation Errors:", errors)
              )}
            >
              <div className="grid grid-cols-1 space-y-3">
                <FormInput
                  form={form}
                  label="Role Name"
                  name="name"
                  type="text"
                  isRequired={true}
                />

                <div className="grid grid-cols-2 gap-5">
                  <SelectComp
                    form={form}
                    label="Select Module"
                    name="action"
                    placeholder="Select a module"
                    isRequired={true}
                  >
                    {modules.map((module: string) => (
                      <SelectItem key={module} value={module}>
                        {module}
                      </SelectItem>
                    ))}
                  </SelectComp>

                  {checkBoxData.length > 0 && (
                    <CheckBoxesComp
                      label="Select Actions"
                      data={checkBoxData}
                      form={form}
                      name="permission"
                      valueName="permission_id"
                      labelName="action"
                      className="grid grid-cols-2 gap-y-5 py-5"
                    />
                  )}
                </div>

                {/* {selectedPermissions?.length ? (
                  <RolesSelected
                    role={selectedPermissions}
                    // getAllPermission={getAllPermission}
                  />
                ) : null} */}
              </div>

              <div className="py-5">
                <SubmitBtn text="Submit" className=" bg-primary text-secondary hover:bg-primary/90" isSubmitting={isPending} />
              </div>
            </form>
          </Form>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
};

export default UpdateRoles;
