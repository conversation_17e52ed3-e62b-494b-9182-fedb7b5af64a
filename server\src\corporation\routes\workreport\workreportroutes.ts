import { Router } from "express";
import { createWorkreport } from "../../controllers/workreport/create";
import {
  viewWorkreport,
  viewclientWorkreport,
  viewuserWorkreport,
  viewworktypeWorkreport,
  viewcategoryWorkreport,
  getCurrentUserWorkReport,
  getCurrentUserWorkReportStatusCount,
} from "../../controllers/workreport/view";
import { updateWorkReport } from "../../controllers/workreport/update";
import { deleteWorkreport } from "../../controllers/workreport/delete";
import { authenticate } from "../../../middleware/authentication";
import { checkPermissionMiddleware } from "../../../middleware/checkPermission";
import { createWorkreportManually } from "../../controllers/workreport/manualcreate";
import { updateWorkReports } from "../../controllers/workreport/UpdateWorkReport";
import { exportWorkReportToExcel } from "../../controllers/workreport/exportservice";
// import { updateWorkReports } from "../../controllers/workreport/UpdateworkReport";

const router = Router();

router.post(
  "/create-workreport",
  authenticate,
  checkPermissionMiddleware("WORK REPORT", "create-workReport"),
  createWorkreport
);

router.post(
  "/create-workreport-manually",
  authenticate,
  checkPermissionMiddleware("WORK REPORT", "create-workReport"),
  createWorkreportManually
);
router.get(
  "/get-all-workreport",
  authenticate,
  checkPermissionMiddleware("WORK REPORT", "view-workReport"),
  viewWorkreport
);
router.get("/get-user-workreport/:id", authenticate, updateWorkReport);
router.get("/get-all-clientworkreport", viewclientWorkreport);
router.get("/get-all-userworkreport", authenticate, viewuserWorkreport);
router.get("/get-all-worktypeworkreport", viewworktypeWorkreport);
router.get("/get-all-categoryworkreport", viewcategoryWorkreport);
router.get(
  "/get-current-user-workreport",
  authenticate,
  getCurrentUserWorkReport
);
router.put(
  "/update-workreport/:id",
  authenticate,
  checkPermissionMiddleware("WORK REPORT", "update-workReport"),
  updateWorkReport
);
router.delete(
  "/delete-workreport/:id",
  authenticate,
  checkPermissionMiddleware("WORK REPORT", "delete-workReport"),
  deleteWorkreport
);

router.put(
  "/update-workreports/:id",
  authenticate,
  checkPermissionMiddleware("WORK REPORT", "update-workReport"),
  updateWorkReports
);

router.get(
  "/",
  authenticate,
  getCurrentUserWorkReportStatusCount
);

router.get(
  "/get-workreport",
  // authenticate,
  exportWorkReportToExcel
);
export default router;
