"use client";
import React, { useState } from "react";
import { Carrier, column } from "./column";
import ServerSideDataTable from "@/app/_component/ServerSideDataTable";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import DataGridTable from "@/app/_component/DataGridTable";

const ViewCarrier = ({ alldata, permissions }: any) => {
  const [totallength, setTotallength] = useState<number>(0);
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const pathname = usePathname();
  const { replace } = useRouter();

  const pageSizedata = parseInt(searchParams.get("pageSize"));
  const totaldatalength = Math.ceil(
    alldata?.datalength / (pageSizedata ? pageSizedata : 50)
  );

  const pageSize = Number(searchParams.get("pageSize"));
  return (
    <div className="w-full">
      <DataGridTable
        data={alldata?.data}
        columns={column(permissions)}
        // filter
        // filter_column="name"
        showColDropDowns
        showPageEntries
        // filter2
        // filter_column2="register1"
        className="w-full"
        pageSize={pageSize}
        totalPages={totallength ? totallength : totaldatalength}
      />
    </div>
  );
};

export default ViewCarrier;
