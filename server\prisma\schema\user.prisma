model User {
  id                   Int                    @id @default(autoincrement())
  corporation_id       Int?
  role_id              Int?
  role                 Roles?                 @relation(fields: [role_id], references: [id], onDelete: Cascade)
  firstName            String?                @db.VarChar()
  lastName             String?                @db.VarChar()
  email                String                 @unique @db.VarChar()
  username             String                 @unique @db.VarChar()
  password             String                 @db.VarChar()
  level                Int?
  parent_id            Int?
  date_of_joining      DateTime?              @db.Timestamptz() 
  branch_id            Int?                 
  branch               Branch?                @relation(fields: [branch_id], references: [id], onDelete: Cascade)
  created_at           DateTime               @default(now()) @db.Timestamptz()
  updated_at           DateTime               @updatedAt @db.Timestamptz()
  corporation          Corporation?           @relation(fields: [corporation_id], references: [corporation_id])
  WorkReport           WorkReport[]
  DailyPlanningDetails DailyPlanningDetails[]
  DailyPlanning        DailyPlanning[]
  image                ImageFile[]
  session              Session[]
  userClients          UserClients[]
  ownershipClients     Client[]               @relation("OwnershipClients")
}


model UserClients {
  userId   Int
  clientId Int
  user     User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  client   Client  @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@id([userId, clientId])
}