import React from "react";
import { getAllData, getCookie } from "@/lib/helpers";
import { category_routes, client_routes, employee_routes } from "@/lib/routePath";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import CustomizeReport from "./CustomizeReport";

const ReportsPage = async () => {
  const categories = await getAllData(category_routes.GETALL_CATEGORY);
  const AllClients = await getAllData(client_routes.GETALL_CLIENT);
  const clients = AllClients?.data || [];
  
  let users = await getAllData(employee_routes.GETCURRENT_USER);

  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userDataPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];

  const corporationCookie = await getCookie("corporationtoken");

  const permissions = corporationCookie ? ["allow_all"] : userDataPermissions;
  if (corporationCookie) {
    const response = await getAllData(employee_routes.GETALL_USERS);
    users = response?.data || [];
  }

  return (
    <div className="w-full p-2 pl-4">
      <div className="h-9 flex items-center">
        <AdminNavBar link={"/pms/customize_report"} name={"Reports"} />
      </div>
      <div className="space-y-2 mb-6">
        <h1 className="text-2xl">Reports</h1>
        {/* <p className="text-sm text-gray-700">
          
        </p> */}
      </div>
      <CustomizeReport
        permissions={permissions}
        categories={categories}
        users={users}
        clients={clients}
      />
    </div>
  );
};

export default ReportsPage;
