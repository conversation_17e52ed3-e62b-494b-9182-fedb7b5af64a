"use client";

import * as React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend,
} from "recharts";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

// Dummy data generator with CSA information
const generateDummyData = (timeFrame: string) => {
  const csaData = [
    { name: "CSA_001", value: 45, status: { created: 25, completed: 20 } },
    { name: "CSA_002", value: 38, status: { created: 22, completed: 16 } },
    { name: "CSA_003", value: 52, status: { created: 30, completed: 22 } },
    { name: "CSA_004", value: 31, status: { created: 18, completed: 13 } },
    { name: "CSA_005", value: 42, status: { created: 24, completed: 18 } },
   
  ];

  const statusData = csaData.reduce((acc, csa) => {
    acc.created += csa.status.created;
    acc.completed += csa.status.completed;
    return acc;
  }, { created: 0, completed: 0 });

  return {
    csaWorkload: csaData,
    statusData: [
      { name: "Created", value: statusData.created },
      { name: "Completed", value: statusData.completed }
    ]
  };
};

const COLORS = [
    "#E6E5E5",
    "#414144",
    "#6A6A6C",
    "#939395",
    "#BCBCBD",
    
  ];

export function PieChartWorkLoad() {
  const [dataView, setDataView] = React.useState("csa");
  const [timeFrame, setTimeFrame] = React.useState("month");
  const [chartData, setChartData] = React.useState(
    generateDummyData(timeFrame)
  );

  React.useEffect(() => {
    setChartData(generateDummyData(timeFrame));
  }, [timeFrame]);

  const data = dataView === "status" ? chartData.statusData : chartData.csaWorkload;
  const total = data.reduce((sum, entry) => sum + entry.value, 0);

  return (
    <Card className="w-full max-w-3xl">
      <CardHeader>
        <CardTitle>CSA Workload Distribution</CardTitle>
        <CardDescription>
          View workload distribution across CSAs or overall status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex space-x-4 mb-4">
          <Select value={dataView} onValueChange={setDataView}>
            <SelectTrigger className="w-[180px]">
              <SelectValue>
                {dataView === "status" ? "Overall Status" : "CSA Workload"}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="csa">CSA Workload</SelectItem>
              <SelectItem value="status">Overall Status</SelectItem>
            </SelectContent>
          </Select>
          <Select value={timeFrame} onValueChange={setTimeFrame}>
            <SelectTrigger className="w-[180px]">
              <SelectValue>
                {timeFrame.charAt(0).toUpperCase() + timeFrame.slice(1)}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="year">Year</SelectItem>
              <SelectItem value="month">Month</SelectItem>
              <SelectItem value="week">Week</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <ResponsiveContainer width="100%" height={400}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={150}
              fill="#8884d8"
              dataKey="value"
              label={({ name, value, percent }) =>
                `${name}: ${value} (${(percent * 100).toFixed(0)}%)`
              }
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>
            <Tooltip formatter={(value) => [`${value} tasks`, 'Workload']} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
        <div className="text-center mt-4">
          <p className="text-2xl font-bold">{total}</p>
          <p className="text-sm text-muted-foreground">
            Total Tasks {dataView === "status" ? "by Status" : "by CSA"}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}