import React from "react";
import { getAllData, getCookie } from "@/lib/helpers";
import {
  client_routes,
  daily_planning,
  employee_routes,
} from "@/lib/routePath";
import Manage from "./manage";

const page = async () => {
  const allDailyPlanning = await getAllData(
    `${daily_planning.GETSPECIFIC_DAILY_PLANNING}`
  );

  console.time("Time taken for: " + employee_routes.GETCURRENT_USER);
  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  console.timeEnd("Time taken for: " + employee_routes.GETCURRENT_USER);
  const userPermissions = userData?.role?.role_permission || [];

  const corporationCookie = await getCookie("corporationtoken");

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  //  (permissions, "permissions");
  const pageSizes = 100;
  const pages = 1;
  const actions = permissions?.map((item) => item?.permission?.action);
  const allClient = await getAllData(
    `${client_routes.GETALL_CLIENT}?pageSize=${pageSizes}&page=${pages}`
  );
  return (
    <>
      <Manage
        permissions={permissions}
        userData={userData}
        allDailyPlanning={allDailyPlanning}
        actions={actions}
        allClient={allClient?.data}
      />
    </>
  );
};

export default page;
