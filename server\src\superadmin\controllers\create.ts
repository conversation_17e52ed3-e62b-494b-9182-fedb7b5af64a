import { createItem } from "../../utils/operation"

export const createSuperAdmin = async(req, res)=> {
    const fields ={
        username: req.body.username,
        email: req.body.email,
        password: req.body.password
    }
    await createItem({
        model: "superAdmin",
        fieldName:"id",
        fields: fields,
        res: res as Response,
        req: req,
        successMessage: "Super Admin has been created"
    })
}