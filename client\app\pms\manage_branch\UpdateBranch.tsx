"use client";
import DialogHeading from '@/app/_component/DialogHeading';
import FormInput from '@/app/_component/FormInput';
import SubmitBtn from '@/app/_component/SubmitBtn';
import TriggerButton from '@/app/_component/TriggerButton';
import { DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import { formSubmit } from '@/lib/helpers';
import { branch_routes } from '@/lib/routePath';
import useDynamicForm from '@/lib/useDynamicForm';
import { createBranchSchema } from '@/lib/zodSchema';
import { Dialog } from '@/components/ui/dialog';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react'
import { toast } from 'sonner';

function UpdateBranch({ data }:  any ) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const router = useRouter();

    const { form } = useDynamicForm(createBranchSchema, {
        name: data?.branch_name || "",
    });


  async function onSubmit(values: any) {
    try {
      const formData = {
        name: values.name.toUpperCase().trim(),
      };
   
      const res = await formSubmit(
        `${branch_routes.UPDATE_BRANCH}/${data.id}`,
        "PUT",
        formData
      );
      
      
      if (res.success) {
        toast.success(res.message);
        router.refresh();
        setIsDialogOpen(false);
      } else {
        toast.error(res.error || "An error occurred while adding the carrier.");
      }
    } catch (error) {
      toast.error("An error occurred while adding the carrier.");
      console.error(error);
    }
  }
  return (
    <>
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
    <DialogTrigger onClick={() => setIsDialogOpen(true)}>
        <TriggerButton type="edit" />
    </DialogTrigger>
    <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
        <DialogHeading
            title="Update Branch"
            description="Update branch details"
        />
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
                <div className="grid grid-cols-1 gap-5">
                    <FormInput
                        form={form}
                        label="Branch Name"
                        name="name"
                        type="text"
                        isRequired
                    />
                </div>
                <SubmitBtn
                    className="w-full bg-primary text-secondary hover:bg-primary/90"
                    text="Update"
                />
            </form>
        </Form>
    </DialogContent>    
</Dialog>   
    
    </>
  )
}

export default UpdateBranch