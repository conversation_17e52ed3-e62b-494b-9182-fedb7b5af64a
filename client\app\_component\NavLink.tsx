"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";
import { cn } from "@/lib/utils";

type LinkProps = {
  label: string;
  path: string;
  icon: React.ReactNode;
  open?: boolean;
};

const NavLink = ({ label, path, icon, open }: LinkProps) => {
  const pathname = usePathname();
  const isActive = pathname === path;

  return (
    <Link
      prefetch={false}
      href={path}
      className={cn(
        "flex items-center gap-3 p-2 rounded-md transition-all duration-200",
        "hover:bg-slate-700 active:bg-slate-700 text-slate-200 hover:text-white",
        isActive && "bg-slate-800"
      )}
    >
      {/* Icon wrapper */}
      <div className={cn("relative flex items-center justify-center", !open && "group w-full")}>
        {/* Icon */}
        <div
          className={cn(
            "w-5 h-5 flex items-center justify-center transition-transform duration-200",
            !open && "group-hover:scale-110"
          )}
        >
          {icon}
        </div>

        {/* Tooltip only when collapsed */}
        {!open && (
          <span
            className="absolute left-full top-1/2 -translate-y-1/2 ml-2 px-3 py-1 whitespace-nowrap
              bg-slate-800 text-white text-sm rounded-md shadow-lg
              opacity-0 group-hover:opacity-100 group-hover:translate-x-1
              transition-all duration-300 z-[100] pointer-events-none"
          >
            {label}
          </span>
        )}
      </div>

      {/* Label only in expanded mode */}
      {open && (
        <p className="text-sm capitalize text-white">{label}</p>
      )}
    </Link>
  );
};

export default NavLink;