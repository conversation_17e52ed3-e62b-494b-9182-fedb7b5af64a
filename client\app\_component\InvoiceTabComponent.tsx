import React from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";

interface TabItem {
  value: string;
  label: string;
  content: React.ReactNode;
  className?: string;
}

interface TabsProps {
  tab: TabItem[];
  defaultValue?: string;
}

const InvoiceTabComponent = ({ tab, defaultValue }: TabsProps) => {
  return (
    <>
      <Tabs defaultValue={defaultValue} className="bg-white py-0 rounded-md ">
        <TabsList className="my-0 bg-transparent space-x-2 flex py-0">
          {tab?.map((item, index) => {
            return (
              <div key={index}>
                <TabsTrigger
                  value={item.value}
                  className={cn(
                    "data-[state=active]:bg-transparent data-[state=active]:border-b border-main-color data-[state=active]:rounded-none  data-[state=active]:text-black data-[state=active]:shadow-none  text-black tracking-wider rounded-b-none data-[state=active]:rounded-t-md",
                    item.className
                  )}
                >
                  {item.label}
                </TabsTrigger>
              </div>
            );
          })}
        </TabsList>
        {tab?.map((item, index) => {
          return (
            <TabsContent value={item.value} className="mt-0" key={index}>
              {item.content}
            </TabsContent>
          );
        })}
      </Tabs>
    </>
  );
};

export default InvoiceTabComponent;
