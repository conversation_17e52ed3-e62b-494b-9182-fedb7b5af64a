import { handleError } from "../../../utils/helpers";

export const viewAssociate = async (req, res) => {
  try {
    const data = await prisma.associate.findMany({
      orderBy: {
        id: 'desc',
      }, });
    if (data) {
      return res.status(200).json({ data, datalength: data.length });
    }
    return res.status(400).json([]);
  } catch (error) {
    return handleError(res, error);
  }
};