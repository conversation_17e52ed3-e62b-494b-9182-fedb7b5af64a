"use client"
export const PermissionWrapper = ({ permissions, requiredPermissions, children }:any) => {
    // Check if the user has any of the required permissions
    //  (permissions)
    const hasPermission =
    permissions?.includes("allow_all") || // Unconditional access if "allow_all" is present
    requiredPermissions?.some((perm:any) => permissions?.includes(perm));
    //  (hasPermission)
  return hasPermission ? children : null;
  };