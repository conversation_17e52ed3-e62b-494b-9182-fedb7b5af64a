import React from "react";
import type { ColumnDef } from "@tanstack/react-table";
import DeleteRow from "@/app/_component/DeleteRow";
import { daily_planning } from "@/lib/routePath";
import { formatDate } from "@/lib/swrFetching";
import { LuUpload } from "react-icons/lu";
import Link from "next/link";
import UpdateDailyPlanning from "./UpdateDailyPlanning";
import { FaPlus } from "react-icons/fa";
import AllTables from "./AllTables";
import ViewDailyPlanning from "./ViewDailyPlanning";
import ViewDailyPlanningDetails from "./ViewDailyPlanningDetails";

export interface DailyPlanning {
  client_id: string;
  daily_planning_date: Date;
  id: number;
  client: any;
}

export const columns: ColumnDef<DailyPlanning>[] = [
  {
    accessorKey: "daily_planning_date",
    header: "Date",
    accessorFn: (row: any) => formatDate(row?.daily_planning_date),
    cell: ({ row }) => formatDate(row.original?.daily_planning_date),
  },
  {
    accessorKey: "client_name",
    header: "Client",
    accessorFn: (row: any) => row?.client?.client_name,
    cell: ({ row }) => row.original?.client?.client_name,
  },
  {
    accessorKey: "action",
    header: "Action",
    id: "action",
    cell: ({ row }) => {
      const data = row?.original;
      const id = data?.id;
      return (
        <div className="flex items-center gap-1">
          <UpdateDailyPlanning data={data} />
          <DeleteRow
            route={`${daily_planning.DELETE_DAILY_PLANNING}/${data?.id}`}
          />
          <Link href={`/user/daily_planning/import/${data?.id}`}>
            <LuUpload className="font-extrabold text-gray-500" />
          </Link>
          <Link href={`/user/daily_planning/${data?.id}`}>
            <FaPlus className="font-extrabold text-gray-500" />
          </Link>
          <AllTables id={row.original?.id}  />

        </div>
        
      );
    },
  },
  
];
