// ... existing code ...
"use client";

import React, { useState, useMemo } from "react";
import { CSVLink } from "react-csv";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Download, ChevronUp, ChevronDown } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  align?: "left" | "right" | "center";
  render?: (value: any, row: any) => React.ReactNode;
}

interface ViewTableProps {
  data: any[];
  carrierAgeCounts: any;
  columns: TableColumn[];
  defaultSort?: {
    column: string;
    direction: "asc" | "desc";
  };
  showFooter?: boolean;
  footerData?: {
    label: string;
    values: Record<string, any>;
  };
}

const ViewTable = ({
  data,
  carrierAgeCounts,
  columns,
  defaultSort = { column: "name", direction: "asc" },
  showFooter = true,
  footerData,
}: ViewTableProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortColumn, setSortColumn] = useState(defaultSort.column);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">(
    defaultSort.direction
  );
  const [priorityFilter, setPriorityFilter] = useState("All");

  // ... existing aggregatedData useMemo ...

  const filteredData = useMemo(() => {
    return (
      data &&
      Object.values(data)
        .filter((item: any) =>
          item.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
        .sort((a: any, b: any) => {
          const column = columns.find((col: any) => col.key === sortColumn);
          if (!column) return 0;

          const aValue = a[sortColumn];
          const bValue = b[sortColumn];

          if (typeof aValue === "string") {
            return sortDirection === "asc"
              ? aValue.localeCompare(bValue)
              : bValue.localeCompare(aValue);
          }
          return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
        })
    );
  }, [data, searchTerm, sortColumn, sortDirection, columns]);

  const handleSort = (column: string) => {
    if (column === sortColumn) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  // ... existing helper functions ...

  return (
    <div className="">
      <Table>
        <TableHeader>
          <TableRow className="bg-muted/50">
            {columns.map((column: any) => (
              <TableHead
                key={column.key}
                className={`${column.sortable ? "cursor-pointer" : ""} ${
                  column.align ? `text-${column.align}` : ""
                }`}
                onClick={() => column.sortable && handleSort(column.key)}
              >
                {column.label}
                {column.sortable &&
                  sortColumn === column.key &&
                  (sortDirection === "asc" ? (
                    <ChevronUp className="inline ml-2 h-4 w-4" />
                  ) : (
                    <ChevronDown className="inline ml-2 h-4 w-4" />
                  ))}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>

        <TableBody>
          {filteredData.map((row: any, index: number) => (
            <TableRow
              key={index}
              className="hover:bg-muted/50 transition-colors"
            >
              {columns.map((column: any) => (
                <TableCell
                  key={column.key}
                  className={column.align ? `text-${column.align}` : ""}
                >
                  {column.render
                    ? column.render(row[column.key], row)
                    : row[column.key]}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>

        {showFooter && footerData && (
          <TableFooter>
            <TableRow className="bg-muted/50">
              <TableCell className="font-bold">{footerData.label}</TableCell>
              {columns.slice(1).map((column : any) => (
                <TableCell
                  key={column.key}
                  className={`${
                    column.align ? `text-${column.align}` : ""
                  } font-bold`}
                >
                  {footerData.values[column.key] || ""}
                </TableCell>
              ))}
            </TableRow>
          </TableFooter>
        )}
      </Table>
    </div>
  );
};

export default ViewTable;
