"use client";
import React, { useEffect, useState } from "react";
import { AddTask } from "./AddTask";
import { SelectClient } from "./SelectClient";
import { Car } from "lucide-react";
import CarrierComponent from "@/components/carrierComponent/CarrierComponent";
import ViewWorkRecord from "./ViewWorkRecord";
import {
  carrier_routes,
  workreport_routes,
  worktype_routes,
} from "@/lib/routePath";
import { getAllData } from "@/lib/helpers";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import Sidebar from "@/components/sidebar/Sidebar";
import { NavBar } from "@/app/_component/NavBar";
import NoTask from "./NoTask";
import { TrackerContext } from "./TrackerContext";
import BreadCrumbs from "@/app/_component/BreadCrumbs";
import ManualAddTask from "./ManualAddTask";

import { But<PERSON> } from "@/components/ui/button";
import { WorkReport } from "./column";
import * as XLSX from "xlsx";
import saveAs from "file-saver";
import { formatDate, formatDuration } from "@/lib/swrFetching";
import ExcelJS from "exceljs";

import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { useSession } from "@/lib/useSession";
import { Dialog, DialogContent } from "@/components/ui/dialog";
const Parent = ({
  permissions,
  WorkData,
  workTypes,
  allCarrier,
  allClient,
  allUser,
  // checkSessionToken,
  params,
  userData,
}: any) => {
  const [filterdata, setFilterData] = useState([]);
  //  (permissions);

  const [selectedCarrier, setSelectedCarrier] = useState("");
  const [selectedClient, setSelectedClient] = useState("");
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [isAddTaskOpen, setIsAddTaskOpen] = React.useState(false);
  const [seletedWorkType, setSeletedWorkType] = React.useState<any>();
  const [previousSelectedClient, setPreviousSelectedClient] = useState<any>();
  const [previousSelectedCarrier, setPreviousSelectedCarrier] = useState<any>();
  const [fDate, setFDate] = useState("");
  const [tDate, setTDate] = useState("");
  const [taskCreatd, setTaskCreated] = useState(false);
  const searchParams = useSearchParams();
  const pageSizedata = parseInt(searchParams.get("pageSize"));
  const totaldatalength = Math.ceil(
    WorkData?.datalength / (pageSizedata ? pageSizedata : 50)
  );
  const [isLogoutConfirmationOpen, setIsLogoutConfirmationOpen] =
    useState(false);
  const { checkSessionToken, isSessionValid } = useSession(userData);
  const exportParams = new URLSearchParams(params);

  exportParams.delete("pageSize");
  exportParams.delete("page");

  const [isLoading, setIsLoading] = useState(false);

  const handleExport = async () => {
    const sessionValid = await checkSessionToken();

    if (!sessionValid || !isSessionValid) {
      setIsLogoutConfirmationOpen(true);
      return;
    }

    setIsLoading(true);
    try {
      const apiUrl = `${
        workreport_routes.GET_CURRENT_USER_WORKREPORT
      }?${exportParams.toString()}`;
      const WorkData = await getAllData(apiUrl);

      if (
        WorkData &&
        WorkData.data &&
        Array.isArray(WorkData.data) &&
        WorkData.data.length > 0
      ) {
        const workReportData = WorkData.data;
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet("Work Report");

        // Add headers
        const headers = [
          "Date",
          "Username",
          "Client Name",
          "Carrier Name",
          "Work Type",
          "Category Name",
          "Task Type",
          "Start Time",
          "Finish Time",
          "Time Spent",
          "Actual Number",
          "Notes",
        ];
        worksheet.addRow(headers);

        workReportData.forEach((item) => {
          const formattedRow = [
            formatDate(item.date),
            item.user.username,
            item.client?.client_name || "N/A",
            item.carrier?.name || "N/A",
            item.work_type?.work_type || "N/A",
            item.category?.category_name || "N/A",
            item.task_type || "N/A",
            item.start_time ? formatTime(item.start_time) : "N/A",
            item.finish_time ? formatTime(item.finish_time) : "N/A",
            formatDuration(item.time_spent),
            item.actual_number || "N/A",
            item.notes || "N/A",
          ];
          worksheet.addRow(formattedRow);
        });

        const fileBuffer = await workbook.xlsx.writeBuffer();

        const blob = new Blob([fileBuffer], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = "Work_Report.xlsx";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        throw new Error("Error: No valid data found for export.");
      }
    } catch (error) {
      console.error("Error exporting work reports:", error);
      toast.error(`Error exporting work reports: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (timeString) => {
    if (!timeString) return "N/A";
    const date = new Date(timeString);
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const formatDuration = (timeSpent) => {
    if (timeSpent == null || isNaN(timeSpent)) {
      return "00:00";
    }

    const totalSeconds = Math.round(timeSpent * 60);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);

    const formattedHours = String(hours).padStart(2, "0");
    const formattedMinutes = String(minutes).padStart(2, "0");

    return `${formattedHours}:${formattedMinutes}`;
  };

  const router = useRouter();
  //  (carrierByClient);
  return (
    <>
      {isLogoutConfirmationOpen && (
        <Dialog open={isLogoutConfirmationOpen}>
          <DialogContent>
            <div className="text-center">
              <p className="mb-4">
                You are already logged in on another device.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      )}
      <TrackerContext.Provider
        value={{ fDate, setFDate, tDate, setTDate, filterdata, setFilterData }}
      >
        <div className=" flex w-full p-5 py-10 gap-2 overflow-hidden">
          <div className="  w-full ">
            {/* {isAddTaskOpen && ( */}
            <div className="absolute top-0 ">
              <BreadCrumbs
                breadcrumblist={[{ link: "/user/tracker", name: "TaskSphere" }]}
              />
            </div>
            <>
              {/* <SelectClient
              setSelectedClient={setSelectedClient}
              isTimerRunning={isTimerRunning}
              elapsedTime={elapsedTime}
              setElapsedTime={setElapsedTime}
              setIsTimerRunning={setIsTimerRunning}
              setSelectedCarrier={setSelectedCarrier}
              setSeletedWorkType={setSeletedWorkType}
              workTypes={workTypes}
              seletedWorkType={seletedWorkType}
            /> */}

              {!isTimerRunning ? (
                <div className="w-full flex justify-end pb-2">
                  <AddTask
                    selectedCarrier={selectedCarrier}
                    selectedClient={selectedClient}
                    setIsTimerRunning={setIsTimerRunning}
                    setElapsedTime={setElapsedTime}
                    isTimerRunning={isTimerRunning}
                    setSeletedWorkType={setSeletedWorkType}
                    workTypes={workTypes}
                    allClient={allClient}
                    setSelectedCarrier={setSelectedCarrier}
                    setSelectedClient={setSelectedClient}
                    previousSelectedClient={previousSelectedClient}
                    previousSelectedCarrier={previousSelectedCarrier}
                    seletedWorkType={seletedWorkType}
                    setTaskCreated={setTaskCreated}
                    taskCreatd={taskCreatd}
                    userData={userData}
                    allUser={allUser}
                    // setIsAddTaskOpen={setIsAddTaskOpen}
                    // isAddTaskOpen={isAddTaskOpen}
                  />
                </div>
              ) : (
                <div className="flex items-center gap-2 p-4 bg-gray-100 rounded-lg shadow-md mb-2">
                  <span className=" text-gray-700">
                    <span className="font-semibold">Client:</span>{" "}
                    {previousSelectedClient?.client_name}
                  </span>
                  <span className="text-gray-500">|</span>
                  <span className="text-gray-600">
                    <span className="font-semibold">Carrier:</span>{" "}
                    {previousSelectedCarrier?.name ||
                      previousSelectedCarrier?.carrier?.name}
                  </span>
                </div>
              )}

              <div className="flex gap-6 md:gap-0  md:flex items-center ">
                <Button
                  className="gap-2 justify-center   uppercase font-semibold rounded-md flex items-center text-sm"
                  onClick={handleExport}
                >
                  {isLoading ? (
                    <span className="animate-spin">
                      <AiOutlineLoading3Quarters />
                    </span>
                  ) : (
                    "Download report"
                  )}
                  {isLoading ? "Exporting..." : ""}{" "}
                </Button>

                <ManualAddTask
                  workTypes={workTypes}
                  allClient={allClient}
                  setIsAddTaskOpen={setIsAddTaskOpen}
                  isAddTaskOpen={isAddTaskOpen}
                  userData={userData}
                />
              </div>

              <ViewWorkRecord
                permissions={permissions}
                isTimerRunning={isTimerRunning}
                setIsTimerRunning={setIsTimerRunning}
                setElapsedTime={setElapsedTime}
                WorkData={WorkData}
                setPreviousSelectedClient={setPreviousSelectedClient}
                setPreviousSelectedCarrier={setPreviousSelectedCarrier}
                isAddTaskOpen={isAddTaskOpen}
                taskCreatd={taskCreatd}
                workTypes={workTypes}
                totaldatalength={totaldatalength}
              />
            </>
            {/* )} */}

            {/* {!isAddTaskOpen  && <NoTask setIsAddTaskOpen={setIsAddTaskOpen} />} */}
          </div>
          {/* {!isTimerRunning && temp?.is_work_carrier_specific && (
          <div
            className={`max-w-[20%] ${
              temp?.is_work_carrier_specific
                ? "animate-slide-in-right"
                : "animate-slide-out-right"
            } `}
          >
            <CarrierComponent
              setSelectedCarrier={setSelectedCarrier}
              seletedWorkType={seletedWorkType}
              selectedCarrier={selectedCarrier}
              carrierByClient={carrierByClient}
            />
          </div>
        )} */}
        </div>
      </TrackerContext.Provider>
    </>
  );
};

export default Parent;
