import { Router } from "express";
import { authenticate } from "../../../middleware/authentication";
import { checkPermissionMiddleware } from "../../../middleware/checkPermission";
import { createAssociate } from "../../controllers/associate/create";
import { viewAssociate } from "../../controllers/associate/view";
import { updateAssociate } from "../../controllers/associate/update";
import { deleteAssociate } from "../../controllers/associate/delete";

const router = Router();

router.post(
  "/create-associate",
   authenticate,
   checkPermissionMiddleware("ASSOCIATE MANAGEMENT", "create-associate"),
  createAssociate
);

router.get(
  "/get-all-associate",
  authenticate,
  checkPermissionMiddleware("ASSOCIATE MANAGEMENT", "view-associate"),
  viewAssociate
);

  router.put(
    "/update-associate/:id",
    authenticate,
    checkPermissionMiddleware("ASSOCIATE MANAGEMENT", "update-associate"),
    updateAssociate
  );

  router.delete(
    "/delete-associate/:id",
    authenticate,
    checkPermissionMiddleware("ASSOCIATE MANAGEMENT", "delete-associate"),
    deleteAssociate
  );

export default router;
