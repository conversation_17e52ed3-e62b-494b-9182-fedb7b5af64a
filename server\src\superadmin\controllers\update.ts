import { Request, Response } from "express";
import { updateItem } from "../../utils/operation";

export const updateSuperAdmin = async (req, res) => {
    const id= req.params.id;
  const fields = {
    username: req.body.username,
    email: req.body.email,
  };
  await updateItem({
    model: "superAdmin",
    fieldName: "id",
    fields: fields,
    id: Number(id),
    res,
    req,
    successMessage: "Super Admin has been updated",
  });
};
