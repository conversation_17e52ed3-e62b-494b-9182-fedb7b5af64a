import React from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import SubmitBtn from "../_component/SubmitBtn";

const page = () => {
  return (
    <>
      <div className="grid grid-cols-1 gap-0 h-screen place-items-center">
        <div className=" ">
          <Image src="/notAuthorized.svg" width={500} height={500} alt="logo" />
          <div className="place-items-center flex justify-center">
            <Link
              className="w-full  tracking-widest font-semibold uppercase 
         hover:bg-primary/50 hover:border-2 hover:border-primary hover:text-white
          text-white  p-2 text-center rounded-lg mt-2 bg-primary duration-300"
              href="/user/tracker"
            >
              Go Home
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};

export default page;
