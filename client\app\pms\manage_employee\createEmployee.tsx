"use client";
import React, { useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { employee_routes, location_api } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { createEmployeeSchema } from "@/lib/zodSchema";
import FormTextarea from "@/app/_component/TextArea";
import { formSubmit } from "@/lib/helpers";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import DialogHeading from "@/app/_component/DialogHeading";
import { FaPlus } from "react-icons/fa";
import { But<PERSON> } from "@/components/ui/button";
import TriggerButton from "@/app/_component/TriggerButton";
import { SheetDemo } from "./Import";
import * as XLSX from "xlsx";

function CreateEmployee({
  allRoles,
  data,
  usertitle,
  allUsers,
  allBranch,
  allClient,
}: any) {
  const router = useRouter();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState<number | string>("");
  const [filteredManagers, setFilteredManagers] = useState(data);
  const [selectedClients, setSelectedClients] = useState<number[]>([]);
  const [showClientList, setShowClientList] = useState(false);
  const [clientSearch, setClientSearch] = useState("");
  const [expandedAssociate, setExpandedAssociate] = useState<string>("");
  const [tempSelectedClients, setTempSelectedClients] = useState<number[]>([]);

  const { form } = useDynamicForm(createEmployeeSchema, {
    username: "",
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    // user_type: "MEMBER",
    role_id: "",
    level: "",
    parent_id: "",
    branch: "",
    date_of_joining: "",
    clients: [],
  });
  const parentClients = useMemo(() => {
    const parent = allUsers?.find(
      (user: any) => user?.id === Number(form.watch("parent_id"))
    );
    return parent?.userClients?.map((uc: any) => uc.client?.id) || [];
  }, [allUsers, form.watch("parent_id")]);
  async function onSubmit(values: any) {
    const level = parseInt(values.level?.toString() || "0");

   if (level > 2 && level !== 5 && selectedClients.length === 0) {
  form.setError("clients", {
    type: "manual",
    message: "Please select at least one client.",
  });
  return;
}
    try {
      const formData = {
        username: values.username,
        firstName: values.firstName,
        lastName: values.lastName,
        email: values.email,
        password: values.password,
        confirmPassword: values.confirmPassword,
        user_type: values.user_type,
        role_id: values.role_id,
        level: values.level,
        parent_id: values.parent_id,
        branch: values.branch,
        date_of_joining: values.date_of_joining,
        clients: selectedClients.map((client: any) => client),
      };

      const data = await formSubmit(
        employee_routes.CREATE_USER,
        "POST",
        formData
      );

      if (data.success) {
        toast.success(data.message);
        setIsDialogOpen(false);
        router.refresh();
        form.reset();
        setSelectedClients([]);
      } else {
        toast.error(
          data.message ||
            data.error ||
            "An error occurred while adding the user."
        );
      }
    } catch (error) {
      toast.error("An error occurred while adding the employee.");
      console.error(error);
    }
  }

  const groupedClients: Record<string, any[]> = allClient?.reduce(
    (acc: any, client: any) => {
      const associateName = client?.associate?.name || "Unassigned";

      if (!acc[associateName]) {
        acc[associateName] = [];
      }

      acc[associateName].push(client);
      return acc;
    },
    {}
  );

  const handleLevelChange = (level: string) => {
    setSelectedLevel(level);
    const filteredData = allUsers.filter(
      (user: any) => parseInt(user.level) > parseInt(level)
    );
    setFilteredManagers(filteredData);
  };

  // const handleAddClient = (clientId: number) => {
  //   setSelectedClients((prevClients) =>
  //     prevClients.includes(clientId)
  //       ? prevClients.filter((id) => id !== clientId)
  //       : [...prevClients, clientId]
  //   );
  // };

  const handleAddClient = (clientId: number) => {
    setSelectedClients((prevClients) => {
      const updatedClients = prevClients.includes(clientId)
        ? prevClients.filter((id) => id !== clientId)
        : [...prevClients, clientId];

      form.setValue("clients", updatedClients, {
        shouldValidate: true,
      });

      return updatedClients;
    });
  };

  const userType = [
    { value: "HR", label: "HR" },
    { value: "TL", label: "TL" },
    { value: "CSA", label: "CSA" },
    { value: "MEMBER", label: "Member" },
  ];

  const handleExportUser = async () => {
    const datas = data.map((user: any) => ({
      Role: user.role.name || "",
      FirstName: user.firstName || "",
      LastName: user.lastName || "",
      Email: user.email || "",
      Username: user.username || user.username?.id || "",
      Password: "N/A",
    }));

    const ws = XLSX.utils.json_to_sheet(datas);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "allEmployee");

    const excelFile = XLSX.write(wb, { bookType: "xlsx", type: "array" });

    const blob = new Blob([excelFile], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheet.sheet",
    });
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = "users.xlsx";
    link.click();
  };

  const handleTempClientToggle = (clientId: number) => {
    setTempSelectedClients((prev) =>
      prev.includes(clientId)
        ? prev.filter((id) => id !== clientId)
        : [...prev, clientId]
    );
  };

  useEffect(() => {
    if (showClientList) {
      setTempSelectedClients([...selectedClients]);
    }
  }, [showClientList]);

  return (
    <>
      <Dialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          setIsDialogOpen(open);
          if (open) {
            form.reset();
            setSelectedClients([]);
            form.setValue("clients", []);
          }
        }}
      >
        {/* <DialogTrigger asChild>
          <Button
            className="cursor-pointer "
            onClick={() => setIsDialogOpen(true)}
          >
            <FaPlus />
            Create User
          </Button>
        </DialogTrigger> */}
        <Button onClick={handleExportUser} className="mr-2">
          Export
        </Button>
        <SheetDemo />
        <DialogTrigger>
          <TriggerButton type="add" text="user" />
        </DialogTrigger>

        <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
          <DialogHeading
            title="Add User"
            description="Please Enter User Details"
          />
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="grid grid-cols-2 gap-5 items-center">
                <FormInput
                  form={form}
                  label="Username"
                  name="username"
                  type="text"
                  isRequired
                />
                <div className="grid grid-cols-2 gap-5 items-center">
                  <FormInput
                    form={form}
                    label="First Name"
                    name="firstName"
                    type="text"
                    isRequired
                  />
                  <FormInput
                    form={form}
                    label="Last Name"
                    name="lastName"
                    type="text"
                    isRequired
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-5 items-center">
                {/* <div className="grid grid-cols-2 gap-5 items-center">
                  <FormInput
                    form={form}
                    label="First Name"
                    name="firstName"
                    type="text"
                    isRequired
                  />
                  <FormInput
                    form={form}
                    label="Last Name"
                    name="lastName"
                    type="text"
                    isRequired
                  />
                </div> */}
                <div className="grid grid-cols-2 gap-5 items-center">
                  <FormInput
                    form={form}
                    label="Password"
                    name="password"
                    type="password"
                    isRequired
                  />

                  <FormInput
                    form={form}
                    label="Confirm Password"
                    name="confirmPassword"
                    type="password"
                    isRequired
                  />
                </div>
                <FormInput
                  form={form}
                  label="Email"
                  name="email"
                  type="email"
                  isRequired
                />
              </div>
              <div className="grid grid-cols-2 gap-5 items-center">
                {/* <div className="grid grid-cols-2 gap-5 items-center">
                  <FormInput
                    form={form}
                    label="Password"
                    name="password"
                    type="password"
                    isRequired
                  />

                  <FormInput
                    form={form}
                    label="Confirm Password"
                    name="confirmPassword"
                    type="password"
                    isRequired
                  />
                </div> */}
                <div className="grid grid-cols-2 gap-5 items-center">
                  <SelectComp
                    form={form}
                    label="Branch"
                    name="branch"
                    placeholder="Select Branch"
                    isRequired
                  >
                    {allBranch.map((branch: any) => (
                      <SelectItem value={branch.id.toString()} key={branch.id}>
                        {branch.branch_name}
                      </SelectItem>
                    ))}
                  </SelectComp>
                  <FormInput
                    form={form}
                    label="Date of Joining"
                    name="date_of_joining"
                    type="date"
                    isRequired
                  />
                </div>
                <div className="grid grid-cols-2 gap-5 items-center">
                  <SelectComp
                    form={form}
                    label="Role"
                    name="role_id"
                    placeholder="Select Role"
                    key={"role"}
                    isRequired
                  >
                    {allRoles.map((role: any) => (
                      <SelectItem value={role.id.toString()} key={role.id}>
                        {role.name}
                      </SelectItem>
                    ))}
                  </SelectComp>
                  <SelectComp
                    form={form}
                    label="Title"
                    name="level"
                    placeholder="Select Title"
                    key={"userTitle"}
                    isRequired
                    onValueChange={handleLevelChange}
                  >
                    {usertitle.map((user: any) => {
                      //  (user)
                      return (
                        <SelectItem value={user.id.toString()} key={user.id}>
                          {user.title}
                        </SelectItem>
                      );
                    })}
                  </SelectComp>
                </div>
                {/* <SelectComp
                  form={form}
                  label="User Type"
                  name="user_type"
                  placeholder="Select User Type"
                  isRequired
                >
                  {userType.map((item) => (
                    <SelectItem value={item.value} key={item.value}>
                      {item.label}
                    </SelectItem>
                  ))}
                </SelectComp> */}
              </div>

              <div className="grid grid-cols-2 gap-5 items-center">
                <SelectComp
                  form={form}
                  label="Reporting To"
                  name="parent_id"
                  placeholder="Select Manager"
                  key={"user"}
                  isRequired={selectedLevel !== "5"}
                >
                  {filteredManagers?.map((user: any) => {
                    //  (user)
                    return (
                      <SelectItem value={user.id.toString()} key={user.id}>
                        {user.firstName} {user.lastName}
                        <br />
                        <span className="text-gray-500  text-[10px] ">
                          {user.email}
                        </span>
                      </SelectItem>
                    );
                  })}
                </SelectComp>

                {selectedLevel !== "5" &&
                  (Number(selectedLevel) > 2 ? (
                    <div className="mt-5">
                      <Button
                        onClick={() => setShowClientList(true)}
                        className="bg-primary text-white"
                        type="button"
                      >
                        Add Dedicated Client
                      </Button>
                      {form.formState.errors.clients && (
                        <p className="text-sm text-red-500 mt-1">
                          {form.formState.errors.clients.message}
                        </p>
                      )}
                      {/* Dialog for client selection */}
                      <Dialog
                        open={showClientList}
                        onOpenChange={(open) => {
                          setShowClientList(open);
                          setExpandedAssociate("");
                          setClientSearch("");
                          if (open) {
                            setTempSelectedClients(selectedClients);
                          }
                        }}
                      >
                        <DialogContent
                          className="max-w-md"
                          key={showClientList ? "open" : "closed"}
                        >
                          <DialogHeading
                            title="Select Clients"
                            description="Choose one or more clients to assign."
                          />
                          <input
                            type="text"
                            placeholder="Search clients..."
                            value={clientSearch}
                            onChange={(e) => setClientSearch(e.target.value)}
                            className="w-full mb-2 px-3 py-2 border rounded-md text-sm"
                          />

                          <div className="max-h-[300px] overflow-y-auto space-y-4">
                            {Object.entries(groupedClients).map(
                              ([associate, clients]) => {
                                const filteredClients = clients.filter(
                                  (client) =>
                                    client.client_name
                                      .toLowerCase()
                                      .includes(clientSearch.toLowerCase())
                                );

                                if (filteredClients.length === 0) return null;

                                const isOpen = expandedAssociate === associate;
                                const isAnyClientSelected =
                                  filteredClients.some((client) =>
                                    tempSelectedClients.includes(client.id)
                                  );
                                const areAllClientsSelected =
                                  filteredClients.every((client) =>
                                    tempSelectedClients.includes(client.id)
                                  );
                                if (expandedAssociate && !isOpen) return null;

                                return (
                                  <div key={associate}>
                                    <div className="flex items-center gap-2">
                                      <input
                                        type="checkbox"
                                        id={`associate-${associate}`}
                                        checked={isAnyClientSelected}
                                        onChange={() => {
                                          setExpandedAssociate((prev) =>
                                            prev === associate ? "" : associate
                                          );
                                        }}
                                      />
                                      <label
                                        htmlFor={`associate-${associate}`}
                                        className="text-sm font-semibold text-primary cursor-pointer"
                                        onClick={(e) => {
                                          e.preventDefault();
                                          setExpandedAssociate((prev) =>
                                            prev === associate ? "" : associate
                                          );
                                        }}
                                      >
                                        {associate}
                                      </label>
                                    </div>

                                    {isOpen && (
                                      <div className="space-y-2 pl-6 mt-2">
                                        {filteredClients.map((client) => (
                                          <div
                                            key={client.id}
                                            className="flex items-center gap-2"
                                          >
                                            <input
                                              type="checkbox"
                                              id={`client-${client.id}`}
                                              checked={tempSelectedClients.includes(
                                                client.id
                                              )}
                                              onChange={() => {
                                                const updated =
                                                  tempSelectedClients.includes(
                                                    client.id
                                                  )
                                                    ? tempSelectedClients.filter(
                                                        (id) => id !== client.id
                                                      )
                                                    : [
                                                        ...tempSelectedClients,
                                                        client.id,
                                                      ];
                                                setTempSelectedClients(updated);
                                              }}
                                            />

                                            <label
                                              htmlFor={`client-${client.id}`}
                                              className="text-sm cursor-pointer"
                                              onClick={() => {
                                                const updated =
                                                  tempSelectedClients.includes(
                                                    client.id
                                                  )
                                                    ? tempSelectedClients.filter(
                                                        (id) => id !== client.id
                                                      )
                                                    : [
                                                        ...tempSelectedClients,
                                                        client.id,
                                                      ];
                                                setTempSelectedClients(updated);
                                              }}
                                            >
                                              {client.client_name}
                                            </label>
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                );
                              }
                            )}
                          </div>

                          <div className="mt-4 text-right">
                            <Button
                              type="button"
                              className="bg-primary text-white"
                              onClick={() => {
                                setSelectedClients(tempSelectedClients);
                                form.setValue("clients", tempSelectedClients, {
                                  shouldValidate: true,
                                });
                                setShowClientList(false);
                              }}
                            >
                              Submit Selected Clients
                            </Button>
                          </div>
                        </DialogContent>
                      </Dialog>

                      {/* Display selected clients */}
                      {selectedClients.length > 0 && (
                        <div className="mt-2 space-y-2 text-sm text-muted-foreground">
                          <p className="font-semibold">Selected Clients: </p>
                          <div className="max-h-[180px] overflow-y-auto pr-2 border rounded-md p-3 space-y-3">
                            {Object.entries(groupedClients).map(
                              ([associate, clients]) => {
                                const selected = clients.filter((client) =>
                                  selectedClients.includes(client.id)
                                );

                                if (selected.length === 0) return null;

                                return (
                                  <div key={associate}>
                                    <h4 className="text-primary font-medium mb-1">
                                      {associate}
                                    </h4>
                                    <ul className="list-disc ml-5">
                                      {selected.map((client) => (
                                        <li key={client.id}>
                                          {client.client_name}
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                );
                              }
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : parentClients.length > 0 ? (
                    <div className="mt-1 space-y-2 text-sm text-muted-foreground">
                      <p className="font-semibold">Select Clients</p>
                      <div className="max-h-[300px] overflow-y-auto pr-2 border rounded-md p-3 space-y-4">
                        {Object.entries(groupedClients).map(
                          ([associate, clients]) => {
                            const managerClients = clients.filter((client) =>
                              parentClients.includes(client.id)
                            );

                            if (managerClients.length === 0) return null;

                            const isAssociateChecked = managerClients.some(
                              (client) => selectedClients.includes(client.id)
                            );

                            const isExpanded = expandedAssociate === associate;

                            return (
                              <div key={associate}>
                                <div className="flex items-center gap-2">
                                  <input
                                    type="checkbox"
                                    id={`assoc-${associate}`}
                                    checked={isAssociateChecked}
                                    onChange={() => {
                                      const allSelected = managerClients.every(
                                        (client) =>
                                          selectedClients.includes(client.id)
                                      );

                                      const updated = allSelected
                                        ? selectedClients.filter(
                                            (id) =>
                                              !managerClients.some(
                                                (c) => c.id === id
                                              )
                                          )
                                        : [
                                            ...selectedClients,
                                            ...managerClients.map((c) => c.id),
                                          ];

                                      setSelectedClients(updated);
                                      form.setValue("clients", updated, {
                                        shouldValidate: true,
                                      });

                                      setExpandedAssociate((prev) =>
                                        prev === associate ? "" : associate
                                      );
                                    }}
                                  />
                                  <label
                                    htmlFor={`assoc-${associate}`}
                                    className="text-sm font-semibold text-primary cursor-pointer"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      setExpandedAssociate((prev) =>
                                        prev === associate ? "" : associate
                                      );
                                    }}
                                  >
                                    {associate}
                                  </label>
                                </div>

                                {isExpanded && (
                                  <div className="flex flex-wrap gap-4 pl-6 mt-2">
                                    {managerClients.map((client) => (
                                      <div
                                        key={client.id}
                                        className="flex items-center gap-2"
                                      >
                                        <input
                                          type="checkbox"
                                          id={`client-${client.id}`}
                                          checked={selectedClients.includes(
                                            client.id
                                          )}
                                          onChange={() => {
                                            const updated =
                                              selectedClients.includes(
                                                client.id
                                              )
                                                ? selectedClients.filter(
                                                    (id) => id !== client.id
                                                  )
                                                : [
                                                    ...selectedClients,
                                                    client.id,
                                                  ];
                                            setSelectedClients(updated);
                                            form.setValue("clients", updated, {
                                              shouldValidate: true,
                                            });
                                          }}
                                        />
                                        <label
                                          htmlFor={`client-${client.id}`}
                                          className="text-sm cursor-pointer"
                                          onClick={() => {
                                            const updated =
                                              selectedClients.includes(
                                                client.id
                                              )
                                                ? selectedClients.filter(
                                                    (id) => id !== client.id
                                                  )
                                                : [
                                                    ...selectedClients,
                                                    client.id,
                                                  ];
                                            setSelectedClients(updated);
                                            form.setValue("clients", updated, {
                                              shouldValidate: true,
                                            });
                                          }}
                                        >
                                          {client.client_name}
                                        </label>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            );
                          }
                        )}
                      </div>
                      {form.formState.errors.clients && (
                        <p className="text-sm text-red-500 mt-1">
                          {form.formState.errors.clients.message}
                        </p>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-red-500 mt-1">
                      No clients assign to user's CSA . Please assign client to
                      CSA before assigning to the user.
                    </p>
                  ))}
              </div>
              <SubmitBtn
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                text="Submit"
              />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default CreateEmployee;
