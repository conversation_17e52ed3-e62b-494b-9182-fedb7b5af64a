import { But<PERSON> } from "@/components/ui/button";
import { formSubmit, getCookie } from "@/lib/helpers";
import {
  corporation_routes,
  employee_routes,
  superadmin_routes,
} from "@/lib/routePath";
import React from "react";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { RiShutDownLine } from "react-icons/ri";

const Logout = ({ isOpen }: any) => {
  const router = useRouter();
  const handleLogout = async () => {
    const superadminToken = await getCookie("superadmintoken");
    const corporationToken = await getCookie("corporationtoken");
    const userToken = await getCookie("token");

    let cookieTobeDeleted = superadminToken || corporationToken || userToken;
    let logoutUrl = superadminToken
      ? superadmin_routes.LOGOUT_SUPERADMIN
      : corporationToken
      ? corporation_routes.LOGOUT_CORPORATION
      : employee_routes.LOGOUT_SESSION_USERS;

   

    if (cookieTobeDeleted) {
      try {
        const response = await fetch(logoutUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({ cookieTobeDeleted }),
        });
        //  (response);
        if (response.ok) {
          const data = await response.json();
          //  ;
          if (data.success) {
            toast.success(data.message);
            // router.push("/login");
            if (corporationToken) {
              router.push("/admin"); 
            } else if (userToken) {
              router.push("/"); 
            }
          } else {
            toast.error(data.error || "An error occurred during logout.");
          }
        } else {
          toast.error("Failed to log out. Please try again.");
        }
      } catch (error) {
        console.error("Logout error:", error);
        toast.error("An error occurred while logging out.");
      }
    } else {
      toast.error("No cookie to clear");
    }
  };

  return (
    <>
      {isOpen ? (
        <Button
          variant={"customButton"}
          className="w-full mt-auto text-secondary p-4"
          onClick={handleLogout}
        >
          Logout
        </Button>
      ) : (
        <RiShutDownLine
          className="w-2/4 mt-auto  cursor-pointer mx-0.5 text-md hover:text-white/50 text-white"
          onClick={handleLogout}
          title="Logout"
        />
      )}
    </>
  );
};

export default Logout;
