import React, { createContext, Dispatch, SetStateAction } from "react";

interface WorkReportType {
  filterdata: any;
  fDate: any;
  tDate: any;
  setFilterData: Dispatch<SetStateAction<any>>;
  setFDate: Dispatch<SetStateAction<any>>;
  setTDate: Dispatch<SetStateAction<any>>;
}

export const TrackerContext = createContext<WorkReportType>({
  fDate: "",
  tDate: "",
  filterdata: [],
  setFilterData: () => {},
  setFDate: () => {},
  setTDate: () => {},
});
