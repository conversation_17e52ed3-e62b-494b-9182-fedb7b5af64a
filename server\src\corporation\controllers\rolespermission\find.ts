export const getAllPermissionParams = async () => {
  return {
    include: {
      AuditLog: {
        orderBy: { timestamp: "desc" },
        take: 1,
        select: {
          tableName: true,
          recordId: true,
          recordType: true,
          action: true,
          userId: true,
          timestamp: true,
        },
      },
    },
  };
};

export const getAllRoleParams = async (corporation_id) => {
  return {
    where: { corporation_id },

    orderBy: { created_at: "asc" },

  };
};

export const getRoleParams = async (corporation_id) => {
  return {
    select: { role_id: true, name: true, client_id: true },
    where: { corporation_id },
    orderBy: { name: "asc" },
  };
};

export const getRolePermissionParams = async (role_id) => {
  return (prisma as any).rolePermission.findMany({
    where: { role_id },
    orderBy: { role: { created_at: "desc" } },
    select: {
      permission: { select: { module: true, permission_id: true } },
  
    },
  });
};

export const getFreightSetupParams = async (corporation_id) => {
  return {
    where: { corporation_id },
    orderBy: { created_at: "asc" },
    include: {
      role: {
        select: {
          role_permission: {
            select: { permission: { select: { module: true } } },
          },
        },
      },
    },
  };
};
