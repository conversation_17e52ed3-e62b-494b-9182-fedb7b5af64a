"use server";
import { getAllData, getCookie } from "@/lib/helpers";
import {
  carrier_routes,
  client_routes,
  employee_routes,
  setup_routes,
} from "@/lib/routePath";
import ManageClientCarrier from "../ManageClientCarrier";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";

const page = async ({
  params: { client_id },
}: {
  params: { client_id: string };
}) => {
  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];

  const corporationCookie = await getCookie("corporationtoken");

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  const allCarriersetup = await getAllData(
    `${setup_routes.GETALL_SETUP_BYID}/${client_id}`
  );
  const pageSize = 50;
  const page = 1;
  const API_URL = `${carrier_routes.GETALL_CARRIER}?pageSize=${pageSize}&page=${page}`;
  const allCarrier = await getAllData(API_URL);

  const allClient = await getAllData(
    `${client_routes.GETALL_CLIENT}?pageSize=${pageSize}&page=${page}`
  );

  return (
    <>
      <ManageClientCarrier
        permissions={permissions}
        allCarriersetup={allCarriersetup}
        allClient={allClient?.data}
        allCarrier={allCarrier?.data}
        client_id={client_id}
      />
    </>
  );
};

export default page;