// components/Stepper.js
import React from "react";
import { MdArrowForwardIos, MdOutlineDoubleArrow } from "react-icons/md";

const Stepper = ({ steps, currentStep, onStepChange, client_name, carrier_name }:any) => {
  const [isAtTop, setIsAtTop] = React.useState(true);

  const handleScroll = () => {
    // Check if the window's scroll position is at the top
    if (window.scrollY === 0) {
      setIsAtTop(true);
    } else {
      setIsAtTop(false);
    }
  };

  React.useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    // Cleanup on unmount
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div className={`fixed justify-between bg-white shadow-md flex top-[4.4%] border shadow-sky-200 
     bo z-10 rounded-md p-1 right-[20%] ${isAtTop ? '' : 'hidden'}`}>
      <div className="flex justify-center gap-x-2 items-center mr-4">
        <p className="text-sm font-semibold text-main-color">{client_name}:</p>
        <p className="text-sm">{carrier_name}</p>
      </div>
      <div className="flex justify-center">
        {steps.map((step: any, index:any) => (
          <React.Fragment key={index}>
            <button
              className={`px-2 uppercase text-sm font-medium rounded-full ${
                currentStep === index || currentStep > index
                  ? "text-main-color"
                  : "text-gray-500"
              } hover:text-sky-300`}
              onClick={() => onStepChange(index)}
            >
              {step}
            </button>
            {index < steps.length - 1 && (
              <MdOutlineDoubleArrow
                className={`w-14 text-lg ${
                  currentStep > index ? "text-main-color" : "text-gray-300"
                } transition-colors duration-300`}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default Stepper;
