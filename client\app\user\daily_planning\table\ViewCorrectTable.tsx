"use client";

import React, { useState, useMemo } from "react";
import { CSVLink } from "react-csv";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Download, ChevronUp, ChevronDown } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

const ViewCorrectTable = ({ dailyPlanningDetailsCorrect }: any) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortColumn, setSortColumn] = useState("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [priorityFilter, setPriorityFilter] = useState("All");
  const carrierAgeCounts = dailyPlanningDetailsCorrect.carrierAgeCounts;

  const aggregatedData = useMemo(() => {
    const acc: any = {};
    dailyPlanningDetailsCorrect.data &&
      dailyPlanningDetailsCorrect.data.forEach((item: any) => {
        item.DailyPlanningDetails.forEach((detail: any) => {
          const carrierKey = `${detail.carrier.name}_${detail.carrier_id}`;
          if (!acc[carrierKey]) {
            acc[carrierKey] = {
              name: detail.carrier.name,
              correct: 0,
            };
          }
          acc[carrierKey].correct += detail.correct || 0;
        });
      });
    return acc;
  }, [dailyPlanningDetailsCorrect.data]);

  //  (aggregatedData && aggregatedData)

  const filteredCarriers = useMemo(() => {
    return (
      aggregatedData &&
      Object.values(aggregatedData)
        .filter((carrier: any) =>
          carrier.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
        .filter((carrier: any) => {
          if (priorityFilter === "All") return true;
          const carrierData = Object.values(carrierAgeCounts).find(
            (item: any) =>
              item.name.trim().toLowerCase() ===
              carrier.name.trim().toLowerCase()
          );
          return (
            carrierData &&
            Object.values(carrierData).some(
              (value: any) => value.priority === priorityFilter
            )
          );
        })
        .sort((a: any, b: any) => {
          if (sortColumn === "name") {
            return sortDirection === "asc"
              ? a.name.localeCompare(b.name)
              : b.name.localeCompare(a.name);
          } else {
            return sortDirection === "asc"
              ? a.correct - b.correct
              : b.correct - a.correct;
          }
        })
    );
  }, [
    aggregatedData,
    searchTerm,
    sortColumn,
    sortDirection,
    priorityFilter,
    carrierAgeCounts,
  ]);

//  (filteredCarriers)

  const totals: any = useMemo(() => {
    return (
      filteredCarriers &&
      filteredCarriers.reduce(
        (totals: any, carrier: any) => {
          totals.correct += carrier.correct;
          return totals;
        },
        { correct: 0 }
      )
    );
  }, [filteredCarriers]);

  const csvData = useMemo(() => {
    const headers = ["Carrier", "Review Status"];
    const rows = filteredCarriers.map((carrier: any) => [
      carrier.name,
      carrier.correct,
    ]);
    const totalRow = ["Total", totals.correct];
    return [headers, ...rows, totalRow];
  }, [filteredCarriers, totals]);

  const handleSort = (column: string) => {
    if (column === sortColumn) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  

  const getBucketKey = (ageRange: string) => {
    const bucketKeyMap: { [key: string]: string } = {
      "120+": "hundredandtwentyplus",
      "91-120": "ninetyonetohundredandtwenty",
      "61-90": "sixtyonetoninety",
      "31-60": "thirtyonetosixty",
      "16-30": "sixteentothirty",
      "8-15": "eighttofifteen",
      "0-7": "zerotoseven",
    };
    return bucketKeyMap[ageRange];
  };

  // Helper function to get priority for an age range
  const getPriority = (ageRange: string, carrierData: any) => {
    if (!carrierData) return "";
    const bucketKey = getBucketKey(ageRange);
    return carrierData[bucketKey]?.priority || "";
  };

  const getBucketCount = (ageRange: string, carrierData: any) => {
    if (!carrierData) return "-";

    const bucketKeyMap: { [key: string]: string } = {
      "120+": "hundredandtwentyplus",
      "91-120": "ninetyonetohundredandtwenty",
      "61-90": "sixtyonetoninety",
      "31-60": "thirtyonetosixty",
      "16-30": "sixteentothirty",
      "8-15": "eighttofifteen",
      "0-7": "zerotoseven",
    };

    const bucketKey = bucketKeyMap[ageRange];
    return carrierData[bucketKey]?.count || "-";
  };

  return (
    <div className="">
      {/* <div className="flex justify-between items-center">
        <div className="flex items-center gap-4"> */}
      {/* <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search carriers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div> */}
      {/* </div>
      </div> */}

      <Table>
        <TableHeader>
          <TableRow className="bg-muted/50">
            <TableHead
              className="w-[200px] cursor-pointer"
              onClick={() => handleSort("name")}
            >
              Carrier
              {sortColumn === "name" &&
                (sortDirection === "asc" ? (
                  <ChevronUp className="inline ml-2 h-4 w-4" />
                ) : (
                  <ChevronDown className="inline ml-2 h-4 w-4" />
                ))}
            </TableHead>
            <TableHead
              className="text-right cursor-pointer"
              onClick={() => handleSort("correct")}
            >
              Correct Status
              {sortColumn === "correct" &&
                (sortDirection === "asc" ? (
                  <ChevronUp className="inline ml-2 h-4 w-4" />
                ) : (
                  <ChevronDown className="inline ml-2 h-4 w-4" />
                ))}
            </TableHead>
            <TableHead className="text-center  ">120+</TableHead>
            <TableHead className="text-center ">91-120</TableHead>
            <TableHead className="text-center"> 61-90</TableHead>
            <TableHead className="text-center"> 31-60</TableHead>
            <TableHead className="text-center"> 16-30</TableHead>
            <TableHead className="text-center"> 8-15</TableHead>
            <TableHead className="text-center"> 0-7</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {filteredCarriers.map((carrier: any, index: number) => {
            const carrierData = Object.values(carrierAgeCounts).find(
              (item: any) =>
                item.name.trim().toLowerCase() ===
                carrier.name.trim().toLowerCase()
            );

            return (
              <TableRow
                key={index}
                className="hover:bg-muted/50 transition-colors"
              >
                <TableCell className="font-medium">{carrier.name}</TableCell>
                <TableCell className="text-right">
                  <span className="font-semibold text-primary bg-primary/10 px-2 py-1 rounded">
                    {carrier.correct}
                  </span>
                </TableCell>
                <TableCell className="text-center">
                  {getPriority("120+", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("120+", carrierData) === "High"
                          ? "bg-red-500"
                          : getPriority("120+", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("120+", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("120+", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell className="text-center">
                  {getPriority("91-120", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("91-120", carrierData) === "High"
                          ? "bg-red-500"
                          : getPriority("91-120", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("91-120", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("91-120", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell className="text-center">
                  {getPriority("61-90", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("61-90", carrierData) === "High"
                          ? "bg-red-500"
                          : getPriority("61-90", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("61-90", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("61-90", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>

                <TableCell className="text-center">
                  {getPriority("31-60", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("31-60", carrierData) === "High"
                          ? "bg-red-500"
                          : getPriority("31-60", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("31-60", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("31-60", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell className="text-center">
                  {getPriority("16-30", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("16-30", carrierData) === "High"
                          ? "bg-red-500"
                          : getPriority("16-30", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("16-30", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("16-30", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell className="text-center">
                  {getPriority("8-15", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("8-15", carrierData) === "High"
                          ? "bg-red-500"
                          : getPriority("8-15", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("8-15", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("8-15", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell className="text-center">
                  {getPriority("0-7", carrierData) ? (
                    <Badge
                      className={`text-center ${
                        getPriority("0-7", carrierData) === "High"
                          ? "bg-red-500"
                          : getPriority("0-7", carrierData) === "Medium"
                          ? "bg-orange-400"
                          : getPriority("0-7", carrierData) === "Low"
                          ? "bg-green-500"
                          : ""
                      }`}
                    >
                      {getBucketCount("0-7", carrierData)}
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>

        <TableFooter>
          <TableRow className="bg-muted/50">
            <TableCell className="font-bold">Total</TableCell>
            <TableCell className="text-right font-bold text-primary">
              {totals.correct}
            </TableCell>
            <TableCell />
            <TableCell />
            <TableCell />
          </TableRow>
        </TableFooter>
      </Table>
    </div>
  );
};

export default ViewCorrectTable;
