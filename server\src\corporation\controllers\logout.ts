import { Request, Response } from "express";
import { handleError } from "../../utils/helpers";
import jwt from "jsonwebtoken";

export const sessionlogout = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { cookieTobeDeleted } = req.body;
    //  ('cookiedelete',cookieTobeDeleted);
    if (!cookieTobeDeleted) {
      return res.status(400).json({
        success: false,
        error: "No token provided for logout.",
      });
    }

    if (req.cookies["corporationtoken"]) {
      return res
        .clearCookie("corporationtoken", {
          httpOnly: true,
          // secure: process.env.NODE_ENV === "production",
          // sameSite: "strict",
        })
        .status(200)
        .json({
          success: true,
          message: "Logout successful",
        });
    } else if (req.cookies["token"]) {
      const decodedToken = jwt.decode(req.cookies["token"]) as any;
      const userId = decodedToken?.id;
      await prisma.session.deleteMany({
        where: { user_id: userId },
      });

      return res
        .clearCookie("token", {
          httpOnly: true,
          // secure: process.env.NODE_ENV === "production",
          // sameSite: "strict",
          maxAge: 0,
        })
        .status(200)
        .json({
          success: true,
          message: "Logout successful",
        });
    } else {
      return res.status(400).json({
        success: false,
        error: "No token found for logout.",
      });
    }
  } catch (error) {
    handleError(res, error);
  }
};

export const logout = async (req: Request, res: Response): Promise<any> => {
  try {
    const { cookieTobeDeleted } = req.body;
    if (!cookieTobeDeleted) {
      return res.status(400).json({
        success: false,
        error: "No token provided for logout.",
      });
    }

    if (req.cookies["corporationtoken"]) {
      return res
        .clearCookie("corporationtoken", {
          httpOnly: true,
          // secure: process.env.NODE_ENV === "production",
          // sameSite: "strict",
        })
        .status(200)
        .json({
          success: true,
          message: "Logout successful",
        });
    } else if (req.cookies["token"]) {
      return res
        .clearCookie("token", {
          httpOnly: true,
          // secure: process.env.NODE_ENV === "production",
          // sameSite: "strict",
          maxAge: 0,
        })
        .status(200)
        .json({
          success: true,
          message: "Logout successful",
        });
    } else {
      return res.status(400).json({
        success: false,
        error: "No token found for logout.",
      });
    }
  } catch (error) {
    handleError(res, error);
  }
};
