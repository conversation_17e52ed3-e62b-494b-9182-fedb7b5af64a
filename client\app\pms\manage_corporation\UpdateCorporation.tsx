"use client";
import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import FormTextarea from "@/app/_component/TextArea";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { useLocation } from "@/hooks/useLocation";
import { formSubmit } from "@/lib/helpers";
import { corporation_routes, location_api } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { corporationSchema, updateCorporationSchema } from "@/lib/zodSchema";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FaEdit } from "react-icons/fa";
import DialogHeading from "@/app/_component/DialogHeading";
import TriggerButton from "@/app/_component/TriggerButton";

function UpdateCorporation({ initialData }: { initialData: any }) {
  const router = useRouter();
  const { countries, states, cities, fetchStates, fetchCities } =
    useLocation(location_api);

  const { form } = useDynamicForm(updateCorporationSchema, {
    username: initialData.username || "",
    email: initialData.email || "",
    country: initialData.country || "",
    state: initialData.state || "",
    city: initialData.city || "",
    address: initialData.address || "",
  });

  const countryName = form.watch("country");
  const stateName = form.watch("state");

  useEffect(() => {
    if (countryName) {
      fetchStates(countryName);
    }
  }, [countryName]);

  useEffect(() => {
    if (stateName) {
      fetchCities(stateName);
    }
  }, [stateName]);

  async function onSubmit(values: any) {
    try {
      const formData = {
        username: values.username,
        email: values.email,
        country: values.country || null,
        state: values.state || null,
        city: values.city || null,
        address: values.address || null,
      };
      const res = await formSubmit(
        `${corporation_routes.UPDATE_CORPORATION}/${initialData.corporation_id}`,
        "PUT",
        formData
      );
      if (res.success === true) {
        toast.success(res.message);
        form.reset()
      } else {
        toast.error(
          res.error || "An error occurred while updating the corporation."
        );
      }
    } catch (error) {
      toast.error("An error occurred while updating the corporation.");
      console.error(error);
    }
  }

  return (
    <Dialog>
      <DialogTrigger>
        <TriggerButton type="edit" />
      </DialogTrigger>
      <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
        <DialogHeading
          title="Update Corporation"
          description="Please update the corporation details"
        />
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-5">
              <FormInput
                form={form}
                label="Username"
                name="username"
                type="text"
                isRequired
              />
              <FormInput
                form={form}
                label="Email"
                name="email"
                type="email"
                isRequired
              />
            </div>

            <div className="grid grid-cols-3 gap-5">
              <SelectComp
                form={form}
                label="Country"
                name="country"
                placeholder="Select Country"
              >
                {countries.map((item) => (
                  <SelectItem value={item.name} key={item.id}>
                    {item.name}
                  </SelectItem>
                ))}
              </SelectComp>

              <SelectComp
                form={form}
                label="State"
                name="state"
                placeholder="Select State"
              >
                {states.map((item) => (
                  <SelectItem value={item.name} key={item.id}>
                    {item.name}
                  </SelectItem>
                ))}
              </SelectComp>

              <SelectComp
                form={form}
                label="City"
                name="city"
                placeholder="Select City"
              >
                {cities.map((item) => (
                  <SelectItem value={item.name} key={item.id}>
                    {item.name}
                  </SelectItem>
                ))}
              </SelectComp>
            </div>

            <FormTextarea
              form={form}
              label="Address"
              name="address"
            />

            <SubmitBtn className="w-full" text="Update" />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export default UpdateCorporation;
