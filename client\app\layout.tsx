import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";
import { GlobalKeyboardShortcutsProvider } from "@/app/_component/GlobalKeyboardShortcuts";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Oi360",
  description: "Generated by TechlogixIt",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <GlobalKeyboardShortcutsProvider>
          {children}
          <Toaster position="top-right" richColors/>
        </GlobalKeyboardShortcutsProvider>
      </body>
    </html>
  );
}
