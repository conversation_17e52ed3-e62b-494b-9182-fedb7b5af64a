import DeleteRow from "@/app/_component/DeleteRow";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import { ColumnDef } from "@tanstack/react-table";
import UpdateAssociate from "./UpdateAssociate";
import { associate_routes } from "@/lib/routePath";

export interface Associate {
  name: string;
  id: any;
}

export const Column = (permissions: string[]): ColumnDef<Associate>[] => [
  {
    accessorKey: "Sr. No.",
    header: "Sr. No.",
    cell: ({ row }) => row.index + 1,
  }, {
    accessorKey: "name",
    header: "Associate Name",
  },
  {
    accessorKey: "action",
    header: "Action",
    id: "action",
    cell: ({ row }: any) => {
      const associate = row?.original;

      return (
        <div className="flex items-center ">
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["update-associate"]}
          >
            <UpdateAssociate data={associate} />
          </PermissionWrapper>

          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["delete-associate"]}
          >
            <DeleteRow
              route={`${associate_routes.DELETE_ASSOCIATE}/${associate?.id}`}
            />
          </PermissionWrapper>
        </div>
      );
    },
  },
];
