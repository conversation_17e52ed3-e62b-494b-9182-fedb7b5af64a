import BreadCrumbs from "@/app/_component/BreadCrumbs";
import React from "react";

interface AdminNavBarProps {
  link: string;
  name: string;
  link1?: string;
  name1?: string;
}

export const AdminNavBar = ({ link, name,link1, name1 }: AdminNavBarProps) => {
  const list = [
    {
      link: "/pms/dashboard",
      name: "Dashboard",
    },
    {
      link: link,
      name: name,
    },
    ...(link1 && name1 ? [{ link: link1, name: name1 }] : []),
  ];

  return <BreadCrumbs breadcrumblist={list} />;
};
