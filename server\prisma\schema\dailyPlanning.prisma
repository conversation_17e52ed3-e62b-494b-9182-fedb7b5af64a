model DailyPlanning {
  id                   Int                    @id @default(autoincrement()) @map("daily_planning_id")
  corporation_id       Int?
  daily_planning_date  DateTime               @default(now())
  client_id            Int?
  corporation          Corporation?           @relation(fields: [corporation_id], references: [corporation_id])
  client               Client?                @relation(fields: [client_id], references: [id], onDelete: Cascade)
  user_id              Int?
  user                 User?                  @relation(fields: [user_id], references: [id], onDelete: Cascade)
  created_at           DateTime               @default(now())
  updated_at           DateTime               @default(now()) @updatedAt
  DailyPlanningDetails DailyPlanningDetails[]
  DailyPlanningByType  DailyPlanningByType[]

  @@unique([client_id, daily_planning_date])
}

model DailyPlanningByType {
  id                   Int                    @id @default(autoincrement())
  daily_planning_id    Int
  daily_planning       DailyPlanning          @relation(fields: [daily_planning_id], references: [id], onDelete: Cascade)
  type                 DailyPlanningType
  created_at           DateTime               @default(now())
  updated_at           DateTime               @default(now()) @updatedAt
  DailyPlanningDetails DailyPlanningDetails[]
}

model DailyPlanningDetails {
  id                        Int                  @id @default(autoincrement())
  corporation_id            Int?
  daily_planning_type_id    Int?
  daily_planning_type       DailyPlanningByType? @relation(fields: [daily_planning_type_id], references: [id], onDelete: Cascade)
  bucket                    Bucket?
  priority_status           PriorityStatus?
  corporation               Corporation?         @relation(fields: [corporation_id], references: [corporation_id], onDelete: Cascade)
  user_id                   Int?
  user                      User?                @relation(fields: [user_id], references: [id], onDelete: Cascade)
  daily_planning_id         Int?
  daily_planning            DailyPlanning?       @relation(fields: [daily_planning_id], references: [id], onDelete: Cascade)
  carrier_id                Int?
  carrier                   Carrier?             @relation(fields: [carrier_id], references: [id], onDelete: Cascade)
  invoice_entry_total       Int?
  two_ten_error_total       Int?
  alloted                   Int?
  pending                   Int?
  review_status             Int?
  pf_status                 Int?
  batch_error               Int?
  reason                    String?              @db.VarChar()
  hold                      Int?
  two_ten_error             Int?
  two_ten_m_f               Int?
  two_ten_success           Int?
  two_ten_hold              Int?
  two_ten_import_additional Int?
  two_ten_manual_match      Int?
  old                       Int?
  new                       Int?
  ute                       Int?
  shipping_type             String?
  division                  String?              @db.VarChar()
  receive_by                String?
  receive_date              DateTime?
  review_by                 String?
  review_date               DateTime?
  reconcile_by              String?
  reconcile_date            DateTime?
  send_by                   String?
  send_date                 DateTime?
  no_invoices               Int?
  amount_of_invoice         Decimal?
  source                    String?
  age                       Int[]
  correct                   Int?
  entry                     Int?
  currency                  String?
  notes                     String?              @db.VarChar()
  type                      DailyPlanningType?
  created_at                DateTime             @default(now())
  updated_at                DateTime             @default(now()) @updatedAt
}

