"use client";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { formSubmit, getAllData } from "@/lib/helpers";
import { carrier_routes, daily_planning_details_routes } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { AddDailyPlanningDetailsSchema } from "@/lib/zodSchema";
import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import DialogHeading from "@/app/_component/DialogHeading";
import TriggerButton from "@/app/_component/TriggerButton";
import { useRouter } from "next/navigation";

const UpdateDailyPlanningDetails = ({ data, type }: any) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [carrierByClient, setCarrierByClient] = useState<any>([]);
  const router = useRouter();
  // The type is fixed based on the data prop
  const planningType = type || "INVOICE_ENTRY_STATUS";

  useEffect(() => {
    if (data && data.daily_planning_id && isDialogOpen) {
      getCarrierByClient();
    }
  }, [data, isDialogOpen]);

  async function getCarrierByClient() {
    try {
      const carrierByClient = await getAllData(
        `${carrier_routes.GET_CARRIER_BY_CLIENT}/${data.clientId}`
      );
      setCarrierByClient(carrierByClient);
    } catch (error) {
      console.error("Error fetching carrier data:", error);
    }
  }
  // Set default values based on data and include the type from props
  const defaultValues = {
    type: planningType,
    carrier: data?.carrierId?.toString() || "",
    old: data?.old?.toString() || "",
    new: data?.new?.toString() || "",
    total: data?.total?.toString() || "0",
    ute: data?.ute?.toString() || "",
    shipping_type: data?.shipping_type || "",
    division: data?.division?.toString() || "",
    receive_date: data?.receive_date
      ? new Date(data?.receive_date).toISOString().split("T")[0]
      : "",
    reconcile_date: data?.reconcile_date
      ? new Date(data?.reconcile_date).toISOString().split("T")[0]
      : "",
    send_date: data?.send_date
      ? new Date(data?.send_date).toISOString().split("T")[0]
      : "",
    no_invoices: data?.no_invoices?.toString() || "",
    amount_of_invoice: data?.amount_of_invoice?.toString() || "",
    currency: data?.currency || "USD",
    notes: data?.notes || "",
  };

  const { form } = useDynamicForm(AddDailyPlanningDetailsSchema, defaultValues);

  const onSubmit = async (values: any) => {
    try {
      const invoiceFields = ["carrier", "old", "new", "total", "ute"];
      const statementFields = [
        "receive_date",
        "no_invoices",
        "amount_of_invoice",
        "currency",
        "notes",
        "reconcile_date",
        "send_date",
        "shipping_type",
        "division",
        "carrier",
      ];

      // Determine which fields to include
      const allowedFields =
        planningType === "INVOICE_ENTRY_STATUS"
          ? invoiceFields
          : statementFields;

      // Filter the values based on the allowed fields
      const filteredValues = Object.keys(values)
        .filter((key) => allowedFields.includes(key))
        .reduce((obj: any, key) => {
          obj[key] = values[key];
          return obj;
        }, {});

      // Always include the type
      filteredValues.type = planningType;


      const formData = await formSubmit(
        `${daily_planning_details_routes.UPDATE_DAILY_PLANNING_DETAILS}/${data.daily_planning_details_id}`,
        "PUT",
        filteredValues
      );

      if (formData.success) {
        router.refresh();
        setIsDialogOpen(false);
        toast.success(formData.message);
      } else {
        toast.error(
          formData.message || "An error occurred while updating the details."
        );
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("An error occurred while submitting the form.");
    }
  };

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger onClick={() => setIsDialogOpen(true)}>
          <TriggerButton type="edit" />
        </DialogTrigger>
        <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
          <DialogHeading
            title={`Update ${
              planningType === "INVOICE_ENTRY_STATUS"
                ? "Invoice Entry Status"
                : "Statement Table"
            }`}
            description="Please enter daily planning details"
          />
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit, (errors) =>
                console.error("Validation Errors:", errors)
              )}
              className="space-y-4"
            >
              <div className="grid grid-cols-1 gap-5">
                {/* Hidden input to maintain the type value */}
                <input
                  type="hidden"
                  {...form.register("type")}
                  value={planningType}
                />

                {/* Fields for Invoice Entry Status */}
                {planningType === "INVOICE_ENTRY_STATUS" && (
                  <>
                    <SelectComp
                      form={form}
                      label="Carrier"
                      name="carrier"
                      placeholder="Select Carrier"
                      isRequired
                      className="w-full rounded-md"
                    >
                      {carrierByClient &&
                        carrierByClient.map((carrier: any) => (
                          <SelectItem
                            value={carrier.carrier?.id?.toString()}
                            key={carrier.carrier.id}
                          >
                            {carrier.carrier.name}
                          </SelectItem>
                        ))}
                    </SelectComp>
                    <div className="grid md:grid-cols-3 grid-cols-1 gap-5">
                      <FormInput
                        label="Old"
                        form={form}
                        name="old"
                        type="text"
                        isRequired
                      />
                      <FormInput
                        label="New"
                        form={form}
                        name="new"
                        type="text"
                        isRequired
                      />
                      <FormInput
                        label="UTE"
                        form={form}
                        name="ute"
                        type="text"
                        isRequired
                      />
                    </div>
                  </>
                )}

                {/* Fields for Statement Table */}
                {planningType === "STATEMENT_TABLE" && (
                  <>
                    <div className="grid md:grid-cols-2 grid-cols-1 gap-5">
                      <SelectComp
                        form={form}
                        label="Carrier"
                        name="carrier"
                        placeholder="Select Carrier"
                        isRequired
                        className="w-full rounded-md"
                      >
                        {carrierByClient &&
                          carrierByClient.map((carrier: any) => {
                            return (
                              <SelectItem
                                value={carrier.carrier?.id?.toString()}
                                key={carrier.carrier.id}
                              >
                                {carrier.carrier.name}
                              </SelectItem>
                            );
                          })}
                      </SelectComp>
                      <FormInput
                        label="Transport Type"
                        form={form}
                        name="shipping_type"
                        type="text"
                        isRequired
                      />
                      <FormInput
                        label="Division"
                        form={form}
                        name={`division`}
                        placeholder="Enter Division"
                        type="text"
                        // isRequired
                      />

                    {/* </div>

                    <div className="grid md:grid-cols-3 grid-cols-1 gap-5"> */}
                      <FormInput
                        label="Receive Date"
                        form={form}
                        name="receive_date"
                        type="date"
                        isRequired
                      />
                      {/* <FormInput
                        label="Reconcile Date"
                        form={form}
                        name={`reconcile_date`}
                        type="date"
                        // isRequired
                      />
                      <FormInput
                        label="Send Date"
                        form={form}
                        name={`send_date`}
                        type="date"
                        
                      /> */}
                    </div>
                    <div className="grid md:grid-cols-2 grid-cols-1 gap-5">
                      <FormInput
                        label="No. of Invoices"
                        placeholder="Enter No. Of Invoices"
                        form={form}
                        name={`no_invoices`}
                        type="number"
                        isRequired
                      />

                      <FormInput
                        label="Amount"
                        placeholder="Enter  Amount"
                        form={form}
                        name={`amount_of_invoice`}
                        type="number"
                        isRequired
                      />
                    </div>

                    <div className="grid md:grid-cols-2 grid-cols-1 gap-5">
                      <SelectComp
                        label="Currency"
                        form={form}
                        name={`currency`}
                        placeholder="Currency"
                        isRequired
                      >
                        <SelectItem value="USD">USD</SelectItem>
                        <SelectItem value="CAD">CAD</SelectItem>
                        <SelectItem value="KRW">KRW</SelectItem>
                      </SelectComp>
                      <FormInput
                        label="Notes"
                        form={form}
                        name={`notes`}
                        placeholder="Enter notes"
                        type="text"
                      />
                    </div>
                  </>
                )}
              </div>

              <SubmitBtn
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                text="Update"
              />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdateDailyPlanningDetails;
