import { Request, Response } from "express";
import { PrismaClient, FieldType } from "@prisma/client";

const prisma = new PrismaClient();

export interface AuthenticatedRequest extends Request {
  user?: { username: string };
}

// --- Helper function to map field type ---
function mapFieldType(type: string): FieldType {
  switch (type.toUpperCase()) {
    case "DATE":
      return "DATE";
    case "NUMBER":
      return "NUMBER";
    case "TEXT":
      return "TEXT";
    case "AUTO":
      return "AUTO";
    default:
      return "TEXT";
  }
}

// --- POST: Add Custom Fields ---
export const createCustomField = async (req: Request, res: Response) => {
  try {
    const { fields } = req.body;

    if (!Array.isArray(fields) || fields.length === 0) {
      return res.status(400).json({ error: "No custom fields provided." });
    }

    const validFields = fields.filter((f: any) => f.name && f.type);

    if (validFields.length === 0) {
      return res.status(400).json({ error: "No valid custom fields found." });
    }

    const createdFields = [];
    const connectedFieldIds: string[] = [];
    let skipped = 0;

    for (const field of validFields) {
      const exists = await prisma.customField.findFirst({
        where: {
          name: {
            equals: field.name,
            mode: "insensitive",
          },
        },
      });

      if (!exists) {
        const mappedType = mapFieldType(field.type);

        const createData: any = {
          name: field.name,
          type: mappedType,
          createdBy: field.created_by || "system",
          updatedBy: field.updated_by || "system",
        };

        if (mappedType === "AUTO") {
          createData.autoOption = field.autoOption || null;
        }

        const created = await prisma.customField.create({
          data: createData,
        });

        createdFields.push(created);
        connectedFieldIds.push(created.id);
      } else {
        skipped++;
      }
    }

    // Note: Custom fields are now managed through client_custom_field_arrangements table
    // No automatic linking to clients when creating new fields

    return res.status(201).json({ created: createdFields, skipped });
  } catch (error) {
    console.error("Error creating custom fields:", error);
    return res.status(500).json({ error: "Server error" });
  }
};