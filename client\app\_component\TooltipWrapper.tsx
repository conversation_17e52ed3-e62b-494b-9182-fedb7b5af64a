import {
    Toolt<PERSON>,
    Toolt<PERSON>Content,
    <PERSON><PERSON>ipProvider,
    TooltipTrigger,
  } from "@/components/ui/tooltip";
  
  interface TooltipWrapperProps {
    content: string;
    children: React.ReactNode;
  }
  
  export default function TooltipWrapper({ content, children }: TooltipWrapperProps) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {children}
          </TooltipTrigger>
          <TooltipContent>
            <p>{content}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }