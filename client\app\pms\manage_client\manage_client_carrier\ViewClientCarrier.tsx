"use client";
import DataTable from "@/app/_component/DataTable";
import React from "react";
import Column, { Carrier, Client, ClientCarrier } from "./columnClientCarrier";

export const ViewClientCarrier = ({
  data,
  permissions,
  allclient,
  allcarrier,
}: {
  data: ClientCarrier[];
  permissions: string[];
  allclient: Client[];
  allcarrier: Carrier[];
}) => {


  return (
    <div>
      <DataTable
        data={data}
        columns={Column(permissions, allclient, allcarrier)}
        showColDropDowns
        showPageEntries
      />
    </div>
  );
};
