import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();
import { Response, Request } from "express";
import { Interface } from "readline";
export interface CustomRequest extends Request {
  user_id: number;
}

export const hasPermission = async (
  user_id: number,
  module: string,
  action: string,
  client_id?: string,
) => {
  const rolePermission = await prisma.user.findFirst({
    where: {
      id: user_id,
    },
    select: {
      role: {
        select: {
          client_id: true,
          role_permission: {
            select: {
              permission: {
                select: {
                  module: true,
                  action: true,
                },
              },
            },
          },
        },
      },
    },
  });

  if (rolePermission.role.role_permission.length > 0) {
    const permission = rolePermission.role.role_permission.find(
      (role) => role.permission.module === module
    );

    return !!permission;
  } else {
    return false;
  }
};

export const checkUserPermission = async ({
  req,
  res,
  action,
  permissiontype,
  client_id,
}: {
  req: CustomRequest;
  res: Response;
  action: string;
  permissiontype: string;
  client_id?: string;
}) => {
  const id = req.user_id;
  if (!id) {
    return true;
  }

  const hasPermissionFlag = await hasPermission(
    Number(id),
    permissiontype,
    action,
    client_id
  );

  if (!hasPermissionFlag) {
    res.status(403).json({ success: false, message: "Permission denied" });
    return false;
  }

  return true;
};

export const checkClientModule = async ({
  client_id,
  user_id,
}: {
  client_id?: string;
  user_id: number;
}) => {
  const user = await prisma.user.findFirst({
    where: {
      id: user_id,
      role: {
        client_id: Number(client_id),
      },
    },
    select: {
      role: {
        select: {
          client_id: true,
        },
      },
    },
  });

  return true;
};
