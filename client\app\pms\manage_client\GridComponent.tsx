// "use client";

// import { AgGridReact } from "ag-grid-react";
// import { useState, useEffect } from "react";
// import { ColDef } from "ag-grid-community";
// import { AllCommunityModule, ModuleRegistry } from "ag-grid-community";
// import { PermissionWrapper } from "@/lib/permissionWrapper";
// import { Button } from "@/components/ui/button";
// import { useRouter } from "next/navigation";
// import UpdateClient from "./UpdateClient";
// import DeleteRow from "@/app/_component/DeleteRow";
// import { client_routes } from "@/lib/routePath";

// ModuleRegistry.registerModules([AllCommunityModule]);

// const GridComponent = ({ allData, permissions, allBranch, allUser }) => {
//   const router = useRouter();
  
//   const [pageSize, setPageSize] = useState(20); // Default page size
//   const [currentPage, setCurrentPage] = useState(1); // Default to page 1
//   const [rowData, setRowData] = useState(allData?.data || []);
//   console.log(rowData, "rowData");
  
//   // Fetch new data when page or page size changes
//   useEffect(() => {
//     const fetchData = async () => {
//       const params = new URLSearchParams({
//         page: currentPage.toString(),
//         pageSize: pageSize.toString(),
//         // Add any other filters here if needed
//       });
      
//       const response = await fetch(`${client_routes.GETALL_CLIENT}?${params.toString()}`);
//       const data = await response.json();
//       setRowData(data);
//     };
    
//     fetchData();
//   }, [currentPage, pageSize]); // Trigger fetch on page or page size change

//   const columnDefs: ColDef[] = [
//     { field: "client_name", headerName: "Client Name", filter: true },
//     {
//       field: "user",
//       headerName: "User",
//       valueGetter: (params) => params.data?.user?.username || "No Username",
//       filter: true,
//     },
//     {
//       field: "branch",
//       headerName: "Branch",
//       valueGetter: (params) => params.data?.branch?.branch_name || "No Branch",
//       filter: true,
//     },
//     {
//       field: "payment terms",
//       headerName: "Payment Terms",
//       cellRenderer: (params) => {
//         return (
//           <div className="flex items-center">
//             <PermissionWrapper
//               permissions={permissions}
//               requiredPermissions={["view-setup"]}
//             >
//               <Button
//                 variant="customButton"
//                 className="cursor-pointer capitalize bg-gray-500 text-white hover:bg-gray-600"
//                 onClick={() => {
//                   router.push(
//                     `/pms/manage_client/manage_client_carrier/${params.data?.client_id}`
//                   );
//                 }}
//               >
//                 Add
//               </Button>
//             </PermissionWrapper>
//           </div>
//         );
//       },
//     },
//     {
//       field: "action",
//       headerName: "Action",
//       cellRenderer: (params) => {
//         return (
//           <div className="flex items-center">
//             <PermissionWrapper
//               permissions={permissions}
//               requiredPermissions={["update-client"]}
//             >
//               <UpdateClient
//                 data={params.data}
//                 allBranch={allBranch}
//                 allUser={allUser}
//               />
//             </PermissionWrapper>

//             <PermissionWrapper
//               permissions={permissions}
//               requiredPermissions={["delete-client"]}
//             >
//               <DeleteRow
//                 route={`${client_routes.DELETE_CLIENT}/${params?.data?.client_id}`}
//               />
//             </PermissionWrapper>
//           </div>
//         );
//       },
//     },
//   ];

//   const onPageSizeChanged = (newPageSize) => {
//     setPageSize(newPageSize);
//     setCurrentPage(1); // Reset to page 1 on page size change
//   };

//   const onPageChanged = (newPage) => {
//     setCurrentPage(newPage);
//   };

//   return (
//     <div style={{ width: "100%", height: "100vh" }}>
//       <AgGridReact
//         rowData={rowData}
//         columnDefs={columnDefs}
//         pagination={true}
//         paginationPageSize={pageSize}
//         // domLayout="autoHeight"
//         onGridSizeChanged={(event) => event.api.sizeColumnsToFit()}
//         onPaginationChanged={(event) => {
//           onPageChanged(event.api.paginationGetCurrentPage() + 1); // Page starts at 1
//         }}
//         paginationNumberFormatter={(params) => {
//           return params.value.toLocaleString();
//         }}
//       />
    
//     </div>
//   );
// };

// export default GridComponent;
