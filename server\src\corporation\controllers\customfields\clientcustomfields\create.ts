import { Request, Response } from "express";
import { PrismaClient, FieldType } from "@prisma/client";

const prisma = new PrismaClient();

export interface AuthenticatedRequest extends Request {
  user?: { username: string };
}

// --- POST: Save Selected Fields for a Client ---
export const createClientCustomField = async (req: Request, res: Response) => {
  try {
    const { client_id, custom_fields } = req.body;

    // Input validation:
    // - client_id must be present
    // - custom_fields must be an array
    if (!client_id || !Array.isArray(custom_fields)) {
      return res.status(400).json({ error: "Invalid input format." });
    }

    // Validate that all custom fields exist
    const foundFields = await prisma.customField.findMany({
      where: { id: { in: custom_fields } },
    });

    if (foundFields.length !== custom_fields.length) {
      return res.status(400).json({ error: "Some custom fields do not exist." });
    }

    // Use transaction to ensure all operations succeed or fail together
    const result = await prisma.$transaction(async (tx) => {

      // First, delete all existing arrangements for this client
      await tx.clientCustomFieldArrangement.deleteMany({
        where: { client_id: Number(client_id) }
      });

      // Then, create new arrangement entries for the current custom fields
      const createdArrangements = [];
      if (custom_fields.length > 0) {
        for (let i = 0; i < custom_fields.length; i++) {
          const arrangement = await tx.clientCustomFieldArrangement.create({
            data: {
              client_id: Number(client_id),
              custom_field_id: custom_fields[i],
              order: i + 1, // 1-based ordering
            },
            include: {
              CustomField: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                }
              }
            }
          });
          createdArrangements.push(arrangement);
        }
      } else {
      }

      return createdArrangements;
    });

    res.status(201).json({ success: true, arrangements: result });
  } catch (err: any) {
    console.error("Error saving client custom fields:", err.message || err);
    res.status(500).json({
      error: "Server error",
      details: err.message || err,
    });
  }
};