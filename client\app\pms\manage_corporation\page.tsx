"use server";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { getAllData } from "@/lib/helpers";
import { corporation_routes } from "@/lib/routePath";
import React from "react";
import ViewCorporation from "./ViewCorporation";
import CreateCorporation from "./CreateCorporation";

const page = async () => {
  const data = await getAllData(corporation_routes.GETALL_CORPORATION);
  //  ;
  return (
    <div className="w-full">
      <div className="h-9 flex items-center">
        <AdminNavBar
          link={"/pms/manage-corporation"}
          name={"Manage Corporation"}
        />
      </div>
      <div className="space-y-2 mt-4">
        <h1 className="text-3xl">Manage Carrier</h1>
        <p className="text-sm text-gray-700">Here you can manage carrier</p>
      </div>
      <div className="w-full px-4">
        <div className="flex justify-end mb-4">
          <CreateCorporation />
        </div>
        <div className="w-full py-7 animate-in fade-in duration-1000">
          <ViewCorporation data={data} />
        </div>
      </div>
    </div>
  );
};

export default page;
