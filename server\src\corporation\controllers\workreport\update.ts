import { Request, Response } from "express";
import moment from "moment-timezone";
import { PrismaClient } from "@prisma/client";
import { updateItem } from "../../../utils/operation";
const prisma = new PrismaClient();

const calculateTimeSpent = (
  pause: Date[],
  resume: Date[],
  finishTime?: Date
): number => {
  let totalTimeSpent = 0;
  //  ('ok')
  for (let i = 0; i < pause.length; i++) {
    const pauseTime = moment(pause[i]).tz("Asia/Kolkata");
    const resumeTime = resume[i] ? moment(resume[i]).tz("Asia/Kolkata") : moment(finishTime).tz("Asia/Kolkata");
    if (resumeTime.isValid() && pauseTime.isValid()) {
      totalTimeSpent += resumeTime.diff(pauseTime, "seconds");
    }
    //  ({totalTimeSpent, pauseTime, resumeTime});
  }

  return totalTimeSpent;
};

export const updateWorkReport = async (
  req: Request,
  res: Response
): Promise<void> => {
  const id = req.params.id;
  //  (id, "id");

  try {
    if (!id || isNaN(Number(id))) {
      res.status(400).json({ message: "Invalid work report ID." });
      return;
    }

    const existingWorkReport = await prisma.workReport.findUnique({
      where: { id: Number(id) },
      select: {
        id: true,
        start_time: true,
        finish_time: true,
        pause: true,
        resume: true,
      },
    });

    if (!existingWorkReport) {
      res.status(404).json({ message: "Work report not found." });
      return;
    }

    const {
      start_time: startTime,
      finish_time: finishTime,
      pause,
      resume,
    } = existingWorkReport;

    const { finish_time: finishTimeInput, action } = req.body;

    const currentTime = moment().tz("Asia/Kolkata").toDate();

    if (action === "pause") {
      if (pause.length > resume.length) {
        res.status(400).json({ message: "Work report is already paused." });
        return;
      }

      await prisma.workReport.update({
        where: { id: Number(id) },
        data: {
          pause: { push: currentTime },
          updated_at: currentTime,
          work_status: "PAUSED",
        },
      });

      const totalPausedDuration = calculateTimeSpent(pause, resume, finishTime);
      const startMoment = moment(startTime).tz("Asia/Kolkata");
      const totalDurationInSeconds = moment(currentTime).diff(startMoment, "seconds");
      const actualTimeSpentInSeconds = totalDurationInSeconds - totalPausedDuration;
      const timeSpentInMinutes = actualTimeSpentInSeconds / 60;

      await prisma.workReport.update({
        where: { id: Number(id) },
        data: {
          time_spent: parseFloat(timeSpentInMinutes.toFixed(2)),
          work_status: "PAUSED",
        },
      });

      res.status(200).json({ success: true, message: "Work report has been paused." });
      return;
    }

    if (action === "resume") {
      if (pause.length <= resume.length) {
        res.status(400).json({ message: "Work report is not paused." });
        return;
      }

      await prisma.workReport.update({
        where: { id: Number(id) },
        data: {
          resume: { push: currentTime },
          updated_at: currentTime,
          work_status: "RESUMED",
        },
      });

      res.status(200).json({ success: true, message: "Work report has been resumed." });
      return;
    }

    if (finishTimeInput) {
      const startMoment = moment(startTime).tz("Asia/Kolkata");
      const finishMoment = moment(finishTimeInput).tz("Asia/Kolkata");

      if (!startMoment.isValid() || !finishMoment.isValid() || !finishMoment.isAfter(startMoment)) {
        res.status(400).json({ message: "Invalid finish time. It must be a valid datetime and after the start time." });
        return;
      }

      const pausedDuration = calculateTimeSpent(pause, resume, finishMoment.toDate());
      const totalDurationInSeconds = finishMoment.diff(startMoment, "seconds");
      const actualTimeSpentInSeconds = totalDurationInSeconds - pausedDuration;
      const timeSpentInMinutes = actualTimeSpentInSeconds / 60;

      const fields = {
        finish_time: finishTimeInput,
        time_spent: parseFloat(timeSpentInMinutes.toFixed(2)),
        work_status: "FINISHED",
        actual_number: Number(req.body.actual_number),
        notes: req.body.notes,
      };

   

      await updateItem({
        model: "workReport",
        fieldName: "id",
        fields,
        id: Number(id),
        res,
        req,
        successMessage: "Work report has been updated successfully.",
      });
    } else {
      res.status(400).json({ message: "No valid action or finish time provided." });
    }
  } catch (error) {
    console.error("Error updating work report:", error);
    res.status(500).json({ message: "Internal server error." });
  }
};

