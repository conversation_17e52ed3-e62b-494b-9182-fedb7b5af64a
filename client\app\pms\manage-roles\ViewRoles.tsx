"use client";
import DataTable from "@/app/_component/DataTable";
import React from "react";
import { column } from "./column";


const ViewRoles = ({ data, permissions, getAllPermission }: any) => {
  //  (getAllPermission);
  //  ;
  //  (typeof column); // Should log "function"

  //  (column); // Check the actual contents of column

  return (
    <>
      <DataTable
        data={data}
        columns={column(getAllPermission)}
        // filter
        // filter_column="name"
        showColDropDowns
        showPageEntries
        // filter2
        // filter_column2=""
      />
    </>
  );
};

export default ViewRoles;
