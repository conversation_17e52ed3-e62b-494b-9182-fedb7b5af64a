"use client";

import React, { useEffect, useState, useRef } from "react";
import dynamic from "next/dynamic";
import toast from "react-hot-toast";

const Tree = dynamic(() => import("react-d3-tree").then((mod) => mod.Tree), {
  ssr: false,
});

// const transformToTree = (user) => ({
//   name: `${user.firstName} ${user.lastName}`,
//   attributes: {
//     title: user.title,
//   },
//   nodeType: "child",
//   children: (user.subChildren || []).map(transformToTree),
// });

const transformToTree = (user) => {
  const allChildren = user.subChildren || [];

  const guChildren = allChildren.filter(
    (child) => child.title?.toLowerCase() === "gu"
  );
  const otherChildren = allChildren.filter(
    (child) => child.title?.toLowerCase() !== "gu"
  );

  const shouldGroupGU = guChildren.length > 0 && otherChildren.length > 0;

  const groupedGUNode = shouldGroupGU
    ? {
        name: "Grouped GUs",
        attributes: { title: "GUs" },
        nodeType: "groupedGU",
        children: guChildren.map(transformToTree),
      }
    : null;

  const childrenNodes = [
    ...otherChildren.map(transformToTree),
    ...(groupedGUNode ? [groupedGUNode] : guChildren.map(transformToTree)),
  ];

  return {
    name: `${user.firstName} ${user.lastName}`,
    attributes: {
      title: user.title,
    },
    nodeType: "child",
    children: childrenNodes,
  };
};

const buildFullHierarchy = ({
  grandGrandParentUser,
  grandParentUser,
  parentUser,
  loggedInUser,
  childrenUsersTitles,
}) => {
  const loggedInNode = {
    name: `${loggedInUser.firstName} ${loggedInUser.lastName}`,
    attributes: { title: loggedInUser.title },
    nodeType: "loggedIn",
    children: childrenUsersTitles.map(transformToTree),
  };

  const buildNode = (user, children, nodeType) => ({
    name: `${user.firstName} ${user.lastName}`,
    attributes: { title: user.title },
    children,
    nodeType,
  });

  let node = loggedInNode;
  if (parentUser) node = buildNode(parentUser, [node], "parent");
  if (grandParentUser) node = buildNode(grandParentUser, [node], "parent");
  if (grandGrandParentUser)
    node = buildNode(grandGrandParentUser, [node], "parent");

  return [node];
};

const TreeHierarchyView = ({
  grandGrandParentUser,
  grandParentUser,
  parentUser,
  loggedInUser,
  childrenUsersTitles,
  // searchedUser
}) => {
  const [treeData, setTreeData] = useState([]);
  const [expandedNodeIds, setExpandedNodeIds] = useState({});
  const containerRef = useRef(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [localSearchedUser, setLocalSearchedUser] = useState(null);
  const [zoom, setZoom] = useState(1);
  const [showMinimap, setShowMinimap] = useState(true);
  const [openGroupedGUId, setOpenGroupedGUId] = useState(null);

  useEffect(() => {
    if (loggedInUser) {
      const tree = buildFullHierarchy({
        grandGrandParentUser,
        grandParentUser,
        parentUser,
        loggedInUser,
        childrenUsersTitles,
      });
      setTreeData(tree);
    }
  }, [
    grandGrandParentUser,
    grandParentUser,
    parentUser,
    loggedInUser,
    childrenUsersTitles,
  ]);

  const findPathToUser = (node, targetUser, depth = 0, path = []) => {
    const nodeId = `${node.name}-${depth}`;
    const newPath = [...path, nodeId];
  
    const normalizedTargetName = `${targetUser.firstName} ${targetUser.lastName}`.toLowerCase();
  
    if (node.name.toLowerCase() === normalizedTargetName) {
      return newPath;
    }
  
    if (node.children) {
      for (const child of node.children) {
        const result = findPathToUser(child, targetUser, depth + 1, newPath);
        if (result) return result;
      }
    }
  
    return null;
  };
  
  useEffect(() => {
    if (localSearchedUser && treeData.length > 0) {
      const pathIds = findPathToUser(treeData[0], localSearchedUser);
      if (pathIds) {
        const expanded = {};
        pathIds.forEach((id, depth) => {
          expanded[depth] = id;
        });
  
        const groupedGUId = pathIds.find((id) => id.includes("Grouped GUs"));
        setOpenGroupedGUId(groupedGUId || null);
  
        setExpandedNodeIds(expanded);
      }
    } else {
      setExpandedNodeIds({});
      setOpenGroupedGUId(null);
    }
  }, [localSearchedUser, treeData]);
  

  const pruneTree = (nodes, depth = 0) => {
    return nodes.map((node) => {
      const prunedNode = { ...node };
      const nodeId = `${node.name}-${depth}`;

      const isGroupedGU = node.nodeType === "groupedGU";

      const shouldExpand = depth < 1 || expandedNodeIds[depth] === nodeId;


      if (shouldExpand && node.children) {
        prunedNode.children = pruneTree(node.children, depth + 1);
      } else {
        prunedNode.children = undefined;
      }

      return prunedNode;
    });
  };

  const handleSearch = () => {
    if (!searchTerm) return;

    const foundUser = findUserInTree(treeData[0], searchTerm.toLowerCase());
    if (foundUser) {
      setLocalSearchedUser(foundUser);
    } else {
      toast.error("User not found");
      setLocalSearchedUser(null);
    }
  };

  const findUserInTree = (node, term) => {
    if (!node) return null;

    const fullName = node.name.toLowerCase();
    if (fullName.includes(term)) {
      const [firstName, ...rest] = node.name.split(" ");
      return { firstName, lastName: rest.join(" ") };
    }

    if (node.children) {
      for (const child of node.children) {
        const result = findUserInTree(child, term);
        if (result) return result;
      }
    }

    return null;
  };

  const handleZoomIn = () => setZoom((prev) => Math.min(prev * 1.2, 2));
  const handleZoomOut = () => setZoom((prev) => Math.max(prev / 1.2, 0.5));
  const handleResetZoom = () => setZoom(1);

  const renderCustomNode = ({ nodeDatum, hierarchyPointNode }) => {
    const handleClick = () => {
      const depth = hierarchyPointNode.depth;
      const nodeId = `${nodeDatum.name}-${depth}`;
    
      setExpandedNodeIds((prev) => {
        const newExpandedNodeIds = { ...prev };
    
        if (newExpandedNodeIds[depth] === nodeId) {
          delete newExpandedNodeIds[depth]; 
        } else {
          newExpandedNodeIds[depth] = nodeId;
        }
    
        return newExpandedNodeIds;
      });
    };
    

    const role = nodeDatum.attributes?.title?.toLowerCase() || "";
    const mainfont = {
      hr: "#1A202C",
      cm: "#2C3E50",
      csa: "#3E3E3E",
      tl: "#102A43",
      gu: "#102A43",
    };
    const titlefont = {
      hr: "#FFFFFF",
      cm: "#FFFFFF",
      csa: "#2C2C2C",
      tl: "#F0F4F8",
      gu: "#FFFFFF",
    };
    const iconcolor = {
      hr: "#4A5568",
      cm: "#5DADE2",
      csa: "#BFA89E",
      tl: "#D81B60",
      gu: "#2B7A78",
    };
    const iconfontcolor = {
      hr: "#E2E8F0",
      cm: "#FFFFFF",
      csa: "#FFFFFF",
      tl: "#FFFFFF",
      gu: "#FFFFFF",
    };
    const colorMaplight = {
      hr: "#2D3748",
      cm: "#3498DB",
      csa: "#D5BDAF",
      tl: "#AD1457",
      gu: "#00796B",
    };
    const colorMap = {
      hr: "#F4F6F8",
      cm: "#EAF1F8",
      csa: "#FAF9F6",
      tl: "#FCE4EC",
      gu: "#F0F4F4",
    };
    const topColor =
      Object.entries(colorMap).find(([k]) => role.includes(k))?.[1] ||
      "#f3f4f6";
    const mainfontColor =
      Object.entries(mainfont).find(([k]) => role.includes(k))?.[1] ||
      "#f3f4f6";
    const titlefontColor =
      Object.entries(titlefont).find(([k]) => role.includes(k))?.[1] ||
      "#f3f4f6";
    const iconbgcolor =
      Object.entries(iconcolor).find(([k]) => role.includes(k))?.[1] ||
      "#f3f4f6";
    const icontxtcolor =
      Object.entries(iconfontcolor).find(([k]) => role.includes(k))?.[1] ||
      "#f3f4f6";
    const innerColor =
      Object.entries(colorMaplight).find(([k]) => role.includes(k))?.[1] ||
      "#f3f4f6";
    const isSearched =
      localSearchedUser &&
      nodeDatum.name.toLowerCase() ===
        `${localSearchedUser.firstName} ${localSearchedUser.lastName}`.toLowerCase();

    const initials = nodeDatum.name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);

    return (
      <g onClick={handleClick} style={{ cursor: "pointer" }}>
        {/* Connection line */}
        <path
          d={`M0,-30 L0,0`}
          className="stroke-sky-200"
          strokeWidth="2"
          strokeDasharray="4 2"
        />

        {/* Card */}
        <foreignObject
          x="-100"
          y="-50"
          width="200"
          height="70"
          className="overflow-visible"
        >
          <div
            className="relative w-full h-full clip-modern transition-all duration-300 hover:shadow-xl hover:-translate-y-1 flex items-center px-2 py-2 gap-1 group"
            style={{
              backgroundColor: isSearched ? "#fee5d4" : "#ffffff",
              boxShadow: "0 4px 10px rgba(0, 0, 0, 0.1)",
            }}
          >
            <div
              className="absolute inset-0 clip-modern z-[-1]"
              style={{
                // border: `2px solid ${topColor}`,
                background: isSearched ? "#fff8f6" : topColor,
                borderRadius: "8px",
              }}
            ></div>
            <div
              className={`
              relative w-12 h-12 clip-diamond flex items-center justify-center
             
              transition-all duration-300
              group-hover:scale-110
              group-hover:rotate-12
              text-base font-semibold
            `}
              style={{
                background: isSearched ? "#ff8787" : iconbgcolor,
                borderRadius: "8px",
                color: isSearched ? "#ffffff" : icontxtcolor,
                boxShadow: `0 0 0 2px ${topColor}`,
              }}
            >
              {initials}
            </div>

            <div className="flex flex-col min-w-0 flex-1 clip-modern">
              <div
                style={{
                  fontSize: "14px",
                  fontWeight: 600,
                  color: isSearched ? "#ffffff" : titlefontColor,
                  marginBottom: "2px",
                  background: isSearched ? "#ff6b6b" : innerColor,
                  padding: "2px 10px",
                  display: "inline-block",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  maxWidth: "150px",
                }}
              >
                {nodeDatum.name}
              </div>
              <span
                className={`text-xs truncate mt-0.5  transition-colors duration-300`}
                style={{ color: isSearched ? "#3c2c2c" : mainfontColor }}
              >
                {nodeDatum.attributes?.title}
              </span>
            </div>
          </div>
        </foreignObject>
      </g>
    );
  };

  const [translate, setTranslate] = useState({ x: 0, y: 0 });

  useEffect(() => {
    if (containerRef.current) {
      const dimensions = containerRef.current.getBoundingClientRect();
      setTranslate({
        x: dimensions.width / 2,
        y: 100,
      });
    }
  }, [treeData]);

  return (
    <div
      className="relative bg-gradient-to-br from-sky-50 via-white to-indigo-50 rounded-3xl border border-sky-100 p-2 
                    shadow-xl
                "
      style={{ width: "100%", height: "600px" }}
    >
      {/* Search Bar */}

      <div className="absolute top-4 right-4 z-10 w-full md:w-1/4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search user..."
            value={searchTerm}
            onChange={(e) => {
              const value = e.target.value;
              setSearchTerm(value);
              if (value === "") setLocalSearchedUser(null);
            }}
            onKeyDown={(e) => e.key === "Enter" && handleSearch()}
            className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-full shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg
              className="w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-4.35-4.35M17 11a6 6 0 11-12 0 6 6 0 0112 0z"
              />
            </svg>
          </div>

          {searchTerm && (
            <button
              onClick={() => {
                setSearchTerm("");
                setLocalSearchedUser(null);
              }}
              className="absolute inset-y-0 right-0 flex items-center pr-4 text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          )}
        </div>
      </div>

      <style jsx global>{`
        .clip-modern {
          clip-path: polygon(92% 0, 100% 25%, 100% 100%, 0 100%, 0 0);
        }
        .clip-search {
          clip-path: polygon(0 0, 92% 0, 100% 50%, 92% 100%, 0 100%);
        }
        .clip-diamond {
          clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
        }
      `}</style>

      <div ref={containerRef} className="w-full h-full">
        {treeData.length > 0 && (
          <Tree
            data={pruneTree(treeData)}
            translate={translate}
            orientation="vertical"
            pathFunc="step"
            collapsible={false}
            nodeSize={{ x: 180, y: 150 }}
            separation={{ siblings: 1.3, nonSiblings: 2 }}
            zoomable={true}
            renderCustomNodeElement={renderCustomNode}
            pathClassFunc={() => "stroke-sky-200 stroke-[2px]"}
          />
        )}
      </div>
    </div>
  );
};

export default TreeHierarchyView;
