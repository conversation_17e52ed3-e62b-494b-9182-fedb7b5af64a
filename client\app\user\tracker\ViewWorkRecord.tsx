import React, { useContext, useEffect, useMemo, useState } from "react";
import column, { WorkReport } from "./column";
import { TrackerContext } from "./TrackerContext";
import { getAllData } from "@/lib/helpers";
import { workreport_routes } from "@/lib/routePath";
import { formatDuration } from "@/lib/swrFetching";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { permission } from "process";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import ServerSideDataTable from "@/app/_component/ServerSideDataTable";
import DataGridTable from "@/app/_component/DataGridTable";

const ViewWorkRecord = ({
  isTimerRunning,
  setIsTimerRunning,
  setElapsedTime,
  WorkData,
  setPreviousSelectedClient,
  setPreviousSelectedCarrier,
  isAddTaskOpen,
  taskCreatd,
  allUser,
  allClient,
  allCarrier,
  workTypes,
  permissions,
  totaldatalength,
}: any) => {
  const { setFDate, fDate, tDate, setTDate } = useContext(TrackerContext);
  const [data, setData] = useState<WorkReport[]>([]);
  const [totallength, setTotallength] = useState<number>(0);
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const pathname = usePathname();
  const { replace } = useRouter();

  // const searchParams = useSearchParams();
  const pageSize = Number(searchParams.get("pageSize"));
  const page = searchParams.get("page");

  const [totals, setTotals] = useState<{
    actualNumber: number;
    timeSpent: number;
  }>({
    actualNumber: 0,
    timeSpent: 0,
  });

  const [previousData, setPreviousData] = useState<WorkReport[]>([]);
  const fetchData = async (updatedFDate: string, updatedTDate: string) => {
    try {
      const response = await getAllData(
        `${
          workreport_routes.GET_CURRENT_USER_WORKREPORT
        }/?fDate=${updatedFDate}&&tDate=${updatedTDate}&&pageSize=${
          pageSize ? pageSize : "50"
        }&&page=${page ? page : 1}`
      );
      const res = response?.data || [];

      const pageSizedata = parseInt(searchParams.get("pageSize"));
      // setTotallength(Math.ceil(response?.datalength / (pageSizedata ? pageSizedata : 50)));
      // const pageSizenumber=  parseInt(searchParams.get("pageSize"))
      // setTotallength(Math.ceil(response?.datalength / pageSizenumber? pageSizenumber : 10));
      // if (Array.isArray(res)) {
      setData(res);
      calculateTotals(res);
      //  } else {
      //   console.error("Received response is not an array:", res);
      // }
      // setData(response);
      // calculateTotals(response);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };
  const handleDateChange = async (field: "fDate" | "tDate", value: string) => {
    if (field === "fDate") {
      setFDate(value);
      params.set("fDate", value?.toString()); // set pageSize query
      replace(`${pathname}?${params.toString()}`);
      // await fetchData(value, tDate); // Fetch using updated fDate
    } else {
      setTDate(value);
      params.set("tDate", value?.toString()); // set pageSize query
      replace(`${pathname}?${params.toString()}`);
      // await fetchData(fDate, value); // Fetch using updated tDate
    }
  };

  const calculateTotals = (filteredData: WorkReport[]) => {
    const actualNumberTotal = filteredData.reduce((acc: any, item: any) => {
      const actualNumber = parseFloat(item.actual_number || "0");
      return acc + actualNumber;
    }, 0);

    const timeSpentTotal = filteredData.reduce((acc: any, item: any) => {
      const timeSpent = item.time_spent ? parseFloat(item.time_spent) : 0;
      return acc + timeSpent;
    }, 0);

    setTotals({ actualNumber: actualNumberTotal, timeSpent: timeSpentTotal });
  };

  useMemo(() => {
    if (WorkData?.data) {
      calculateTotals(WorkData.data);
    }
  }, [WorkData]);

  // const totalview = (
  //   <div className="flex justify-end">
  //     <div className="mb-3 bg-gray-100 border border-gray-300 rounded-lg text-center p-2 flex justify-end items-center space-x-6">
  //       <p className="text-s">Actual Number: {totals.actualNumber}</p>
  //       <p className="text-s">Time Spent: {formatDuration(totals.timeSpent)}</p>
  //     </div>
  //   </div>
  // );
  const totalview = (
    // <div className="fixed w-1/2 ">
    <div className="flex top-14 justify-start text-center gap-1 md:w-full ">
      <div className="mb-3 w-[300px]  border-dashed border  tracking-widest border-gray-500 rounded-lg text-center p-2  items-center space-x-6">
        <p className="text-s">
          Time Spent:{" "}
          <span className="font-normal ">
            {formatDuration(totals.timeSpent)}
          </span>
        </p>
      </div>
      <div className="mb-3 w-[300px]  border-dashed border  tracking-widest border-gray-500 rounded-lg text-center p-2  items-center space-x-6">
        <p className="text-s">Actual Number: {totals.actualNumber}</p>
      </div>
    </div>
    // </div>
  );

  const filterview = (
    <div className="flex gap-2 mb-4">
      <Input
        type="date"
        value={fDate}
        onChange={(e) => handleDateChange("fDate", e.target.value)}
        placeholder="From Date"
        className="w-full dark:bg-gray-700 !outline-main-color"
      />
      <Input
        type="date"
        value={tDate}
        onChange={(e) => handleDateChange("tDate", e.target.value)}
        placeholder="To Date"
        className="w-full dark:bg-gray-700 !outline-main-color"
      />
    </div>
  );

  const handleFilteredDataChange = (filteredData: WorkReport[]) => {
    if (JSON.stringify(filteredData) !== JSON.stringify(previousData)) {
      calculateTotals(filteredData);
      setPreviousData(filteredData);
    }
  };
  return (
    <>
      <DataGridTable
        data={WorkData.data}
        columns={column(
          isTimerRunning,
          setIsTimerRunning,
          setElapsedTime,
          setPreviousSelectedClient,
          setPreviousSelectedCarrier,
          permissions
        )}
        // filter
        // filter_column="carriername"
        // filter2
        // filter_column2="clientname"
        filter3={true}
        filter3view={filterview}
        showColDropDowns
        showPageEntries
        total={true}
        totalview={totalview}
        onFilteredDataChange={handleFilteredDataChange}
        pageSize={pageSize}
        totalPages={totallength ? totallength : totaldatalength}
      />
    </>
  );
};

export default ViewWorkRecord;
