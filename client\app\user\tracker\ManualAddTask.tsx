import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import DialogHeading from "@/app/_component/DialogHeading";
import TriggerButton from "@/app/_component/TriggerButton";
import { Form } from "@/components/ui/form";
import FormInput from "@/app/_component/FormInput";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { formSubmit, getAllData, getCookie } from "@/lib/helpers";
import { employee_routes, workreport_routes } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { manualAddTaskSchema } from "@/lib/zodSchema";
import { convertTimeToDate } from "@/lib/swrFetching";
import toast from "react-hot-toast";
import { useSession } from "@/lib/useSession";
import FormTimePicker from "@/app/_component/FormTimePicker";

import { But<PERSON> } from "@/components/ui/button";

const ManualAddTask = ({
  workTypes,
  allClient,
  allCarrier,
  setIsAddTaskOpen,
  isAddTaskOpen,
  userData,
}: any) => {
  const router = useRouter();
  const [isLogoutConfirmationOpen, setIsLogoutConfirmationOpen] =
    useState(false);
  const { checkSessionToken, isSessionValid } = useSession(userData);
  const [time, setTime] = useState("");
  const [date, setDate] = useState<Date | undefined>(new Date());

  const { form } = useDynamicForm(manualAddTaskSchema, {
    notes: undefined,
    date: undefined,
    startTime: undefined,
    endTime: undefined,
  });

  const onSubmit = async (values: any) => {
    try {
      const submittedValues = {
        ...values,
        start_time: convertTimeToDate(values.startTime),
        end_time: convertTimeToDate(values.endTime),
        notes: values.notes,
        task_type: values.task_type,
        work_type_id: parseInt(values.workType),
      };

      const data = await formSubmit(
        workreport_routes.CREATE_WORKREPORT_MANUALLY,
        "POST",
        submittedValues,
        "/user/tracker"
      );
      //  ;

      if (data.success) {
        setIsAddTaskOpen(false);
        form.reset();
        router.refresh();
      }
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };
  const handleExtraCurricularClick = async () => {
    const sessionValid = await checkSessionToken();

    if (!sessionValid || !isSessionValid) {
      setIsLogoutConfirmationOpen(true);
      return;
    }

    setIsAddTaskOpen(true);
  };

  return (
    <>
      <div className="w-full lg:flex lg:justify-end pb-2 pt-2 lg:pt-0 mt-2  ">
        <Button
          className=" py-1.5 gap-2 bg-white border hover:bg-white  px-3 justify-center  text-black uppercase font-semibold rounded-md flex items-center text-sm"
          onClick={handleExtraCurricularClick}
        >
          Extra Curricular
        </Button>
      </div>
      {isLogoutConfirmationOpen && (
        <Dialog open={isLogoutConfirmationOpen}>
          <DialogContent>
            <div className="text-center">
              <p className="mb-4">
                You are already logged in on another device.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      )}
      <Dialog open={isAddTaskOpen} onOpenChange={setIsAddTaskOpen}>
        <DialogContent className="md:min-w-[50rem] min-w-[10rem] w-screen lg:w-full">
          <DialogHeading
            title="Add Task"
            description="Please enter task details"
          />
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="grid grid-cols-2 md:grid md:grid-cols-2 gap-5 ">
                <FormInput
                  form={form}
                  label="Date"
                  name="date"
                  type="date"
                  className=""
                  isRequired
                />
                <FormTimePicker
                  form={form}
                  label="Start Time"
                  name="startTime"
                  placeholder="hh:mm AM/PM"
                  isRequired
                  className="mt-1"
                />
                <FormTimePicker
                  form={form}
                  label="End Time"
                  name="endTime"
                  placeholder="hh:mm AM/PM"
                  isRequired
                  className="mt-1"
                />
                {/* <TimePickerInput
                  form={form}
                  label="End Time"
                  name="endTime"
                
                  isRequired
                /> */}
                <FormInput
                  form={form}
                  label="Notes"
                  name="notes"
                  type="text"
                  isRequired
                />
              </div>

              <SubmitBtn
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                text="Submit"
              />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ManualAddTask;
