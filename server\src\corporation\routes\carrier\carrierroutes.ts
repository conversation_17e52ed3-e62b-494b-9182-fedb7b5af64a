import { Router } from "express";
import { createCarrier, excelCarrier } from "../../controllers/carrier/create";
import {
  viewCarrier,
  viewCarrierByClient,
} from "../../controllers/carrier/view";
import { updateCarrier } from "../../controllers/carrier/update";
import { deleteCarrier } from "../../controllers/carrier/delete";
import { authenticate } from "../../../middleware/authentication";
import { checkPermissionMiddleware } from "../../../middleware/checkPermission";
import multer from "multer";
import { exportCarrierService } from "../../controllers/carrier/exportCarrierService";
import { Carrier} from "../../controllers/carrier/clientCarrier";
const router = Router();
//checkPermissionMiddleware("createClientProfile", "CLIENT MANAGEMENT")


const DIR = "./src/public";

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, DIR);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + "-" + file.originalname);
  },
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 },
}).single("file");


router.post('/excelCarrier', upload, excelCarrier);//client



router.post(
  "/create-carrier",
  authenticate,
  checkPermissionMiddleware("CARRIER MANAGEMENT", "create-carrier"),
  createCarrier
);

router.get(
  "/get-all-carrier",
  authenticate,
  checkPermissionMiddleware("CARRIER MANAGEMENT", "view-carrier"),
  viewCarrier
);

router.put(
  "/update-carrier/:id",
  authenticate,
  checkPermissionMiddleware("CARRIER MANAGEMENT", "update-carrier"),
  updateCarrier
);

router.delete(
  "/delete-carrier/:id",
  authenticate,
  checkPermissionMiddleware("CARRIER MANAGEMENT", "delete-carrier"),
  deleteCarrier
);

router.get(
  "/get-carrier-by-client/:id",
  authenticate,
  checkPermissionMiddleware("CARRIER MANAGEMENT", "view-carrier-by-client"),
  viewCarrierByClient
);

router.get(
  "/get-carrier",
  authenticate,
  checkPermissionMiddleware("CARRIER MANAGEMENT", "view-carrier"),
  Carrier
);

router.get(
  "/export-carrier",
  exportCarrierService
);                
export default router;
