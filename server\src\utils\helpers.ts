import { Response } from "express";
export const handleError = (res, error: any, customMessage?: any) => {
  return res.status(500).json({
    success: false,
    message: customMessage || error.message,
  });
};

export const checkExistingRow = async ({
  model,
  criteria,
  res,
  errorMessage,
}: {
  model: string;
  criteria: any;
  res: any;
  errorMessage: string;
}) => {
  try {
    const existingData = await prisma[model].findFirst({
      where: criteria,
    });
    if (existingData) {
      return res.status(409).json({
        successs: false,
        message: errorMessage,
      });
    }
    return null;
  } catch (error) {
    return handleError(res, error);
  }
};
export const findMany = async ({
  model,
  params,
  res,
  postProcess,
}: {
  model: string;
  params: any;
  res: Response;
  postProcess?: (data: any) => Promise<any>;
}) => {
  try {
    const data = await prisma[model].findMany(params);
    const result = postProcess ? await postProcess(data) : data;
    return res.status(200).json(result);
  } catch (error) {
    return handleError(res, error);
  }
};

export const DeleteMany = ({
  model,
  where_condition,
  res,
}: {
  model: string;
  where_condition;
  res: Response;
}) => {
  return prisma[model].deleteMany({
    where: where_condition,
  });
};

export const buildCarrierAgeCounts = (data) => {
  let carrierAgeCounts = {};

  const determineBucket = (num) => {
    if (num <= 7) return "zerotoseven";
    else if (num <= 15) return "eighttofifteen";
    else if (num <= 30) return "sixteentothirty";
    else if (num <= 60) return "thirtyonetosixty";
    else if (num <= 90) return "sixtyonetoninety";
    else if (num <= 120) return "ninetyonetohundredandtwenty";
    else return "hundredandtwentyplus";
  };

  data.forEach((planningType) => {
    planningType.DailyPlanningDetails.forEach((detail) => {
      const carrier = detail.carrier;
      if (
        carrier &&
        carrier.ClientCarrier &&
        carrier.ClientCarrier.length > 0
      ) {
        carrier.ClientCarrier.forEach((clientCarrier) => {
          const clientCarrierId = clientCarrier.id;
          const paymentTerms = Number(clientCarrier.payment_terms) || 0;
          const carrierName = carrier.name || `Carrier-${clientCarrierId}`;

          if (!carrierAgeCounts[clientCarrierId]) {
            carrierAgeCounts[clientCarrierId] = {
              name: carrierName,
              paymentTerms: paymentTerms,
              zerotoseven: { count: 0, priority: "" },
              eighttofifteen: { count: 0, priority: "" },
              sixteentothirty: { count: 0, priority: "" },
              thirtyonetosixty: { count: 0, priority: "" },
              sixtyonetoninety: { count: 0, priority: "" },
              ninetyonetohundredandtwenty: { count: 0, priority: "" },
              hundredandtwentyplus: { count: 0, priority: "" },
            };
          }

          if (detail.age && Array.isArray(detail.age)) {
            detail.age.forEach((num) => {
              const bucket = determineBucket(num);
              carrierAgeCounts[clientCarrierId][bucket].count++;
            });
          }
        });
      }
    });
  });

  return carrierAgeCounts;
};

export const assignPriorityToBuckets = (entry, paymentTerms) => {
  const bucketRanges = [
    { name: "zerotoseven", max: 7 },
    { name: "eighttofifteen", max: 15 },
    { name: "sixteentothirty", max: 30 },
    { name: "thirtyonetosixty", max: 60 },
    { name: "sixtyonetoninety", max: 90 },
    { name: "ninetyonetohundredandtwenty", max: 120 },
    { name: "hundredandtwentyplus", max: Infinity },
  ];

  const populatedBuckets = bucketRanges.filter((b) => entry[b.name].count > 0);

  const exceedingBuckets = populatedBuckets.filter((b) => b.max > paymentTerms);
  const nonExceedingBuckets = populatedBuckets.filter(
    (b) => b.max <= paymentTerms
  );

  // 1️⃣ Handle exceeding buckets (these get "High" priority)
  if (exceedingBuckets.length > 0) {
    exceedingBuckets.forEach((b) => {
      entry[b.name].priority = "High";
    });
  }

  // 2️⃣ Handle non-exceeding buckets (should only be "Medium" or "Low")
  if (nonExceedingBuckets.length > 0) {
    // Sort in descending order to find the closest one
    const sortedNonExceeding = nonExceedingBuckets.sort(
      (a, b) => b.max - a.max
    );

    sortedNonExceeding.forEach((b, index) => {
      if (index === 0) {
        entry[b.name].priority = "Medium"; // Closest one to payment terms gets "Medium"
      } else {
        entry[b.name].priority = "Low"; // Others get "Low"
      }
    });
  }
};
