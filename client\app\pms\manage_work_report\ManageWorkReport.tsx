"use client";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import React, { useState } from "react";
import ExportReport from "./ExportReport";
import ViewWorkReport from "./ViewWorkReport";
import { WorkReportContext } from "./WorkReportContext";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import { useSearchParams } from "next/navigation";

const ManageWorkReport = ({ data, permissions ,params}: any) => {
  const [filterdata, setFilterData] = useState([]);
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  //  (permissions);
  const [deleteData, setDeletedData] = useState(false);

  const searchParams = useSearchParams();
  const pageSizedata = parseInt(searchParams.get("pageSize"))
   const totaldatalength=(Math.ceil(data?.datalength / (pageSizedata ? pageSizedata : 50)));

  return (
    <WorkReportContext.Provider
      value={{
        filterdata,
        setFilterData,
        fromDate,
        setFromDate,
        toDate,
        setToDate,
        deleteData,
        setDeletedData,
      }}
    >
      <div className="w-full p-2 pl-4">
        <div className="h-9 flex items-center">
          <AdminNavBar
            link={"/pms/manage_work_report"}
            name={"Manage Work Report"}
          />
        </div>
        <div className="space-y-2 ">
          <h2 className="text-2xl">Manage Work Report</h2>
          <p className="text-sm text-gray-700">
            Here you can manage work report
          </p>
        </div>
        <div className="w-full ">
          {data?.length !== 0 && (
            <div className="w-full animate-in fade-in duration-1000 flex justify-end mb-2">
              <ExportReport data={data} permissions={permissions} params={params}/>
            </div>
          )}
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["view-workReport"]}
          >
            {/* {data?.length !== 0 && (
              <div className="flex justify-end mb-2">
                <ExportReport data={data} />
              </div>
            )} */}
            <div className="w-full  animate-in fade-in duration-1000">
              <ViewWorkReport dataProp={data} permissions={permissions} totaldatalength={totaldatalength}  />
            </div>

            {/* <div className="w-full  animate-in fade-in duration-1000">
            <ViewWorkReport />
          </div> */}
          </PermissionWrapper>
        </div>
      </div>
    </WorkReportContext.Provider>
  );
};

export default ManageWorkReport;