import React from 'react';
import { Timer } from 'lucide-react';

const ComingSoonCard = () => {
  return (
    <div className="max-w-md mx-auto bg-white rounded-xl shadow-lg overflow-hidden ">
      <div className="p-8">
        <div className="flex items-center justify-center mb-6">
          <Timer className="h-12 w-12 text-blue-500" />
        </div>
        
        <h2 className="text-2xl font-bold text-center text-gray-800 mb-4">
          Rolling Out Shortly
        </h2>
        
        <p className="text-gray-600 text-center mb-6">
          Soon,you'll have one more reason to love Oi.360.
        </p>
        
        {/* <div className="space-y-4">
          <input
            type="email"
            placeholder="Enter your email for updates"
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          
          <button className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors duration-200">
            Notify Me
          </button>
        </div> */}
      </div>
    </div>
  );
};

export default ComingSoonCard;