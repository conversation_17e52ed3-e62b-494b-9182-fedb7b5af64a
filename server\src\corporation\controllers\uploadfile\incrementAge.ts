import cron from "node-cron";

export const incrementAge = async () => {
  const dpd = await prisma.dailyPlanningDetails.findMany();
  for (const dp of dpd) {
    const updateAge = dp.age.map((age: number | null) => {
      if (age === null) return null;
      return age + 1;
    });
    await prisma.dailyPlanningDetails.update({
      where: { id: dp.id },
      data: { age: updateAge },
    });
  }
};

cron.schedule(
  "0 0 * * *",
  async () => {
    try {
      await incrementAge();
    } catch (error) {
      console.log("Error running incrementAge cron job:", error);
      throw error;
    }
  },
  { scheduled: true, timezone: "Asia/Kolkata" }
);
