
import { Response } from "express";

export const createTransactions = async ({
  model01,
  fieldName01,
  fields01,
  logging_relationship01,
  model02,
  fieldName02,
  fields02,
  logging_relationship02,
  model03,
  fieldName03,
  fields03,
  logging_relationship03,
  res,
  req,
  successMessage,
}: {
  res: Response;
  req: any;
  model01: string;
  fieldName01: string;
  fields01: Record<string, any>;
  logging_relationship01: string;
  model02: string;
  fieldName02: string;
  fields02: Record<string, any>[];
  logging_relationship02: string;
  model03?: string;
  fieldName03?: string;
  fields03?: Record<string, any>;
  logging_relationship03?: string;
  successMessage: string;
}) => {
  try {
    // const { freightadminid, freightuserid } = req;
    // const user_role =
    //   freightadminid && freightuserid ? "FREIGHT_ADMIN_USER" : "FREIGHT_ADMIN";
    // const userId =
    //   freightadminid && freightuserid ? freightuserid : freightadminid;

    await prisma.$transaction(async (tx) => {
      const result = await tx[model01].create({
        data: {
          ...fields01,
        },
      });
      const id = result[fieldName01];

      for (let i = 0; i < fields02.length; i++) {
        await tx[model02].create({
          data: {
            ...fields02[i],
            [fieldName01]: id,
          },
        });
      }

      return res.status(200).json({
        success: true,
        message: successMessage,
      });
    });
  } catch (error) {
  }
};
