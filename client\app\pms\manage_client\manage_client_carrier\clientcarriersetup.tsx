"use client";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import FormTextarea from "@/app/_component/TextArea";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { formSubmit } from "@/lib/helpers";
import { IoSettings } from "react-icons/io5";
import { client_routes, location_api, setup_routes } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { createClientCarrierSchema, createClientSchema } from "@/lib/zodSchema";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FaPlus } from "react-icons/fa";
import DialogHeading from "@/app/_component/DialogHeading";
import { useRouter } from "next/navigation";
import TriggerButton from "@/app/_component/TriggerButton";
import { revalidatePath } from "next/cache";
import { ComboboxDemo } from "./SearchInput";

function ClientCarrierSetup({
  allCarrier,
  client_id,
  allclient,
  allCarriersetup,
}: any) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [clientname, setClientname] = useState("");
  const router = useRouter();
  useEffect(() => {
    const foundClients = allclient.filter(
      (client: any) => client.id.toString() === client_id

      // client.client_id.toString() === client_id.toString()
    );
    setClientname(foundClients[0]?.client_name);
  }, [client_id, allclient]);
  const { form } = useDynamicForm(createClientCarrierSchema, {
    carrier_id: "",
    client_id: client_id?.toString() || "",
    payment_terms: "",
    client_name: clientname || "",
  });
  const { setValue, watch } = form;

  useEffect(() => {
    if (clientname) {
      setValue("client_name", clientname);
    }
  }, [clientname, setValue]);

  const clientNameValue = watch("client_name");
  async function onSubmit(values: any) {
    try {
      const formData = {
        carrier_id: values.carrier_id,
        client_id: client_id,
        payment_terms: values.payment_terms,
        // client_name: values.client_name,
      };

      const data = await formSubmit(
        setup_routes.CREATE_SETUP,
        "POST",
        formData
      );

      if (data.success) {
        toast.success(data.message);
        router.refresh();
        form.reset();
      } else {
        toast.error(
          data.message || "An error occurred while adding the client carrier."
        );
      }
    } catch (error) {
      toast.error("An error occurred while adding the client carrier.");
      console.error(error);
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="flex space-x-5 my-1 p-2 items-center ">
            <SelectComp
              form={form}
              label="Client Name"
              name="client_id"
              placeholder="Select client name"
              key={"role"}
              disabled={true}
              className=" w-[200px]"
              isRequired
            >
              {allclient?.map((role: any) => (
                <SelectItem value={role?.id?.toString()} key={role?.id}>
                  {role.client_name}
                </SelectItem>
              ))}
            </SelectComp>
            {/* <SelectComp
              form={form}
              label="Carrier Name"
              name="carrier_id"
              placeholder="Select carrier name"
              key={"role"}
              className=" w-[200px]"
              isRequired
            >
              {allCarrier?.map((role: any) => (
                <SelectItem
                  value={role?.id?.toString()}
                  key={role.id}
                >
                  {role.name}
                </SelectItem>
              ))}
            </SelectComp> */}

            <ComboboxDemo
              form={form}
              label="Carrier Name"
              name="carrier_id"
              placeholder="Search Carrier"
              key={"role"}
              className=" w-[300px] mb-2"
              isRequired
            />

            <FormInput
              form={form}
              label="Payment Terms"
              name="payment_terms"
              className=" w-[130px] mt-0"
              type="number"
              isRequired
            />

            <Button
              className="w-40 bg-primary text-secondary hover:bg-primary/90 mt-4 "
              type="submit"
            >
              Submit
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}

export default ClientCarrierSetup;
