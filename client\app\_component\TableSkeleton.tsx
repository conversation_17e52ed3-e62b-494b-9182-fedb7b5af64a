"use client";

import {
  <PERSON>,
  TableBody,
  Table<PERSON>ell,
  <PERSON><PERSON><PERSON><PERSON>,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";

const TableSkeleton = ({ heading }: { heading: string }) => {
  // Create an array of 5 items to represent loading rows
  const skeletonRows = Array(2).fill(null);

  return (
    <div className="">
      <Table>
        <TableHeader>
          <TableRow className="bg-muted/50">
            <TableHead className="w-[200px]">Carrier</TableHead>
            <TableHead className="text-right">{heading}</TableHead>
            <TableHead className="text-center">120+</TableHead>
            <TableHead className="text-center">91-120</TableHead>
            <TableHead className="text-center">61-90</TableHead>
            <TableHead className="text-center">31-60</TableHead>
            <TableHead className="text-center">16-30</TableHead>
            <TableHead className="text-center">8-15</TableHead>
            <TableHead className="text-center">0-7</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {skeletonRows.map((_, index) => (
            <TableRow
              key={index}
              className="hover:bg-muted/50 transition-colors"
            >
              <TableCell className="font-medium">
                <Skeleton className="h-5 w-[120px]" />
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end">
                  <Skeleton className="h-7 w-12 rounded" />
                </div>
              </TableCell>
              {/* Age range cells */}
              {Array(7)
                .fill(null)
                .map((_, cellIndex) => (
                  <TableCell key={cellIndex} className="text-center">
                    <div className="flex justify-center">
                      <Skeleton className="h-6 w-8 rounded-full" />
                    </div>
                  </TableCell>
                ))}
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow className="bg-muted/50">
            <TableCell className="font-bold">Total</TableCell>
            <TableCell className="text-right">
              <div className="flex justify-end">
                <Skeleton className="h-6 w-10" />
              </div>
            </TableCell>
            {/* Total cells for age ranges */}
            {Array(7)
              .fill(null)
              .map((_, cellIndex) => (
                <TableCell key={cellIndex} className="text-center">
                  <div className="flex justify-center">
                    <Skeleton className="h-5 w-8" />
                  </div>
                </TableCell>
              ))}
          </TableRow>
        </TableFooter>
      </Table>
    </div>
  );
};

export default TableSkeleton;
