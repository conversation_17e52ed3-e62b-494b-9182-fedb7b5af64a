import React from "react";
import {
  getAllData,
  getCookie,
  hasPermission,
  PermissionWrapper,
} from "@/lib/helpers";
import {
  associate_routes,
  branch_routes,
  carrier_routes,
  client_routes,
  employee_routes,
  search_routes,
  customFields_routes,
} from "@/lib/routePath";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { ViewCustomFields } from "./ViewCustomFields";
import AddCustomField from "./AddCustomField";
import ArrangementButton from "../arrange_custom_fields/ArrangementButton";


const CustomFieldPage = async ({
  searchParams,
}: {
  searchParams: {
    pageSize?: string;
    page?: string;
    "Client Name"?: string;
    Ownership: string;
    Associate: string;
    Branch: string;
    "Custom Field Name"?: string;
    "Field Type"?: string;
    "Client List"?: string;
    name?: string;
    type?: string;
  };
}
) => {
  const {
    page = "1",
    pageSize = "50",
    ["Client Name"]: <PERSON><PERSON><PERSON><PERSON>,
    Associate,
    Ownership,
    Branch,
    ["Custom Field Name"]: customFieldName,
    ["Field Type"]: fieldType,
    ["Client List"]: clientList,
    name,
    type,
  } = searchParams;

  // Build search parameters for client search
  const clientParams = new URLSearchParams();
  if (pageSize) clientParams.append("pageSize", pageSize);
  if (page) clientParams.append("page", page);
  if (ClientName) clientParams.append("client_name", ClientName);
  if (Associate) clientParams.append("associate.name", Associate);
  if (Ownership) clientParams.append("ownership.username", Ownership);
  if (Branch) clientParams.append("branch.branch_name", Branch);
  clientParams.append("includeRelations", "ownership, branch, associate");

  // Build search parameters for custom fields
  const customFieldParams = new URLSearchParams();
  if (pageSize) customFieldParams.append("pageSize", pageSize);
  if (page) customFieldParams.append("page", page);
  if (customFieldName) customFieldParams.append("Custom Field Name", customFieldName);
  if (fieldType) customFieldParams.append("Field Type", fieldType);
  if (clientList) customFieldParams.append("Client List", clientList);
  if (name) customFieldParams.append("name", name);
  if (type) customFieldParams.append("type", type);

  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const allBranch = await getAllData(branch_routes.GETALL_BRANCH);
  const allAssociate = await getAllData(associate_routes.GETALL_ASSOCIATE);
  const allUsers = await getAllData(employee_routes.GETALL_USERS);
  const allUser = allUsers?.data;

  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];

  const corporationCookie = await getCookie("corporationtoken");
  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  // Fetch custom fields with client usage and search parameters
  console.time("Time taken for custom fields");
  const customFieldsApiUrl = `${customFields_routes.GET_CUSTOM_FIELDS_WITH_CLIENTS}?${customFieldParams.toString()}`;
  const allCustomFields = await getAllData(customFieldsApiUrl);
  console.timeEnd("Time taken for custom fields");

  // Still need client data for ArrangementButton
  const api_url = `${search_routes.GET_SEARCH}/client?${clientParams?.toString()}`;
  const allClient = await getAllData(api_url);


  return (
    <>
      <div className="w-full pl-4">
        <div className="h-9 flex items-center">
          <AdminNavBar link={"/pms/addupdate_custom_fields"} name={"Add/Update Custom Fields"} />
        </div>
        <div className="space-y-2 ">
          <h1 className="text-2xl">Add/Update Custom Fields</h1>
          <p className="text-sm text-gray-700">Here you can add or update custom fields</p>
        </div>
        <div className="w-full pr-3">
          <div className="flex justify-end items-center gap-2 mt-4">
            <AddCustomField
              userData={userData}
            />
          </div>
          <div className="w-full py-4 animate-in fade-in duration-1000">
            {/* {hasPermission({
              permission_data: permissions,
              permission: "view"
            }) && } */}
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["view-client"]}
            >
              <ViewCustomFields
                alldata={allCustomFields}
                permissions={permissions}
                // allCarrier={allCarrier?.data}
                allBranch={allBranch}
                allUser={allUser}
                allAssociate={allAssociate}
                userData={userData}
              />
            </PermissionWrapper>
          </div>
        </div>
      </div>
    </>
  );
};

export default CustomFieldPage;