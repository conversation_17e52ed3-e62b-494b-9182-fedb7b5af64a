import { Router } from "express";
import { createBranch } from "../../controllers/branch/create";
import { authenticate } from "../../../middleware/authentication";
import { checkPermissionMiddleware } from "../../../middleware/checkPermission";
import { viewBranch } from "../../controllers/branch/view";
import { updateBranch } from "../../controllers/branch/update";
import { deleteBranch } from "../../controllers/branch/delete";



const router = Router();

router.post("/create-branch", authenticate, checkPermissionMiddleware("BRANCH MANAGEMENT", "create-branch"),
    createBranch);

router.get("/get-all-branch", authenticate, checkPermissionMiddleware("BRANCH MANAGEMENT", "view-branch"),
    viewBranch);

router.put("/update-branch/:id", authenticate, checkPermissionMiddleware("BRANCH MANAGEMENT", "update-branch"),
    updateBranch);

router.delete("/delete-branch/:id", authenticate, checkPermissionMiddleware("BRANCH MANAGEMENT", "delete-branch"),
    deleteBranch);

export default router;