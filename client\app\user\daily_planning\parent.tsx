"use client";
import React, { useState } from "react";
import { AddDailyPlanning } from "./AddDailyPlanning";
import ViewDailyPlanning from "./ViewDailyPlanning";
import BreadCrumbs from "@/app/_component/BreadCrumbs";
import { PermissionWrapper } from "@/lib/permissionWrapper";

const Parent = ({
  allDailyPlanning,
  permissions,
  allClient,
  userData,
}: any) => {
  const [refresh, setRefresh] = useState(0);
  return (
    <div className="w-full">
      <div className="absolute top-0">
        <BreadCrumbs
          breadcrumblist={[
            { link: "/user/daily_planning", name: "TaskRadar" },
          ]}
        />
      </div>

      <PermissionWrapper
        permissions={permissions}
        requiredPermissions={["create-dailyPlannig", "create-backdate-dailyplanning"]}
        condition="OR"
      >
        <AddDailyPlanning
        permissions={permissions}
          allClient={allClient}
          setRefresh={setRefresh}
          userData={userData}
        />
      </PermissionWrapper>

      <PermissionWrapper
        permissions={permissions}
        requiredPermissions={["DAILY PLANNING"]}
      >
        <ViewDailyPlanning
          DailyPlanningData={allDailyPlanning}
          permissions={permissions}
          allClient={allClient}
          refresh={refresh}
          userData={userData}
        />
      </PermissionWrapper>
    </div>
  );
};

export default Parent;
