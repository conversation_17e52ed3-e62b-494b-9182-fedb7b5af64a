/* eslint-disable react-hooks/rules-of-hooks */

import toast from "react-hot-toast";
import useS<PERSON> from "swr";
import CryptoJS from "crypto-js";

export const fetchDataInClient = (path: string) => {
  const fetcher = (...args: [any]) =>
    fetch(...args, { credentials: "include" }).then((res) => res.json());
  const { data, error } = useSWR<any[], Error>(`${path}`, fetcher);
  return data;
};

export const showingToast = async (
  data: any,
  form: any,
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>,
  isReset?: boolean
) => {
  if (data.success) {
    toast.success(data.message, {
      position: "top-right",
      duration: 5000,
      style: {
        background: "#fff",
      },
    });
    setOpen && setOpen(false);

    form && isReset && form.reset();
  } else {
    toast.error(data.message, {
      position: "top-right",
      style: {
        background: "#fff",
      },
    });
  }
};

export const formatDate = (originalDate: string | number | Date | null | undefined): string => {
  if (!originalDate) return "N/A";

  const dateObject = new Date(originalDate);
  if (isNaN(dateObject.getTime())) return "Invalid Date";

  const day = dateObject.getUTCDate().toString().padStart(2, "0");
  const month = (dateObject.getUTCMonth() + 1).toString().padStart(2, "0");
  const year = dateObject.getUTCFullYear().toString().slice(-2); // Get last two digits of the year
  const formattedDate = `${day}-${month}-${year}`;

  return formattedDate;
};

export const formatDuration = (timeSpent: any) => {
  if (timeSpent == null || isNaN(timeSpent)) {
    return "00:00:00"; // Default value if timeSpent is invalid
  }

  // Convert the timeSpent to total seconds
  const totalSeconds = Math.round(timeSpent * 60); // Convert minutes to seconds

  // Calculate hours, minutes, and seconds
  const hours = Math.floor(totalSeconds / 3600); // Get total hours
  const minutes = Math.floor((totalSeconds % 3600) / 60); // Get remaining minutes
  const seconds = totalSeconds % 60; // Get remaining seconds

  // Format hours, minutes, and seconds to always have 2 digits
  const formattedHours = String(hours).padStart(2, "0");
  const formattedMinutes = String(minutes).padStart(2, "0");
  const formattedSeconds = String(seconds).padStart(2, "0");

  // Return the formatted time in HH:mm:ss format
  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
};
export const generateHash = (data: any): string => {
  return CryptoJS.SHA256(JSON.stringify(data)).toString(CryptoJS.enc.Base64);
};

export const encryptData = (data: any): string => {
  return CryptoJS.AES.encrypt(
    JSON.stringify(data),
    process.env.NEXT_PUBLIC_SECRET_KEY!
  ).toString();
};

export const storeData = (key: string, data: any): void => {
  const hash = generateHash(data);
  const encryptedData = encryptData(data);
  const dataWithHash = { encryptedData, hash };
  localStorage.setItem(key, JSON.stringify(dataWithHash));
};

export function subtractTime(decimal: any) {
  const currentTime = new Date().getTime();
  let millisecondsToSubtract;
  if (decimal < 1) {
    millisecondsToSubtract = decimal * 60 * 1000;
  } else if (decimal >= 1 && decimal < 60) {
    millisecondsToSubtract = decimal * 60 * 1000;
  } else {
    millisecondsToSubtract = decimal * 60 * 60 * 1000;
  }
  const resultTime = currentTime - millisecondsToSubtract;
  return resultTime;
}

export function convertTimeToDate(timeStr) {
  // Handle empty or invalid time strings
  if (!timeStr) return new Date();

  // Convert 12-hour time format (e.g., "1:30 PM") to 24-hour format
  const [time, modifier] = timeStr.split(' '); // Split into time and AM/PM
  let [hours, minutes] = time.split(':').map(Number); // Split hours and minutes
  const date = new Date();

  // If it's PM and hours are less than 12, add 12 hours
  if (modifier === 'PM' && hours < 12) hours += 12;
  // If it's AM and hours are 12 (for 12 AM), set hours to 0 (midnight)
  if (modifier === 'AM' && hours === 12) hours = 0;

  // Set the date object with the parsed time
  date.setHours(hours, minutes, 0, 0);

  return date;
}

export function convertTimeToDates(timeStr) {
  if (!timeStr) return new Date(); // Return current date if timeStr is invalid or empty

  const regex = /^(0?[1-9]|1[0-2]):([0-5][0-9]) (AM|PM)$/i;
  const match = timeStr.trim().match(regex);

  if (!match) {
    return new Date(); // Return current date if time format is invalid
  }

  let [ , hours, minutes, modifier] = match;
  hours = Number(hours);
  minutes = Number(minutes);

  const date = new Date();
  if (modifier.toUpperCase() === 'PM' && hours < 12) hours += 12;
  if (modifier.toUpperCase() === 'AM' && hours === 12) hours = 0;

  date.setHours(hours, minutes, 0, 0);
  return date;
}

 // Date formatting function for start and finish time
  export  const formatTimeZone = (timeString: string) => {
        const time = new Date(timeString);
        if (!isNaN(time.getTime())) {
          return time.toLocaleTimeString("en-US", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: true,
          });
        }
        return "-"; // Return a placeholder if time is invalid
      };