import { Router } from "express";
import { createClient, excelClient } from "../../controllers/client/create";
import { viewClient } from "../../controllers/client/view";
import { updateClient } from "../../controllers/client/update";
import { deleteClient } from "../../controllers/client/delete";
import { authenticate } from "../../../middleware/authentication";
import { checkPermissionMiddleware } from "../../../middleware/checkPermission";
import multer from "multer";
import { exportClientService } from "../../controllers/client/exportClientService";
import { genericSearchController } from "../../../search/genericSearchController";

const router = Router();
const DIR = "./src/public";

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, DIR);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + "-" + file.originalname);
  },
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 },
}).single("file");

router.post("/excelClient", upload, excelClient); //client

router.post(
  "/create-client",
  authenticate,
  checkPermissionMiddleware("CLIENT MANAGEMENT", "create-client"),
  createClient
);

router.get(
  "/get-all-client",
  authenticate,
  checkPermissionMiddleware("CLIENT MANAGEMENT", "view-client"),
  viewClient
);


router.put(
  "/update-client/:id",
  authenticate,
  checkPermissionMiddleware("CLIENT MANAGEMENT", "update-client"),
  updateClient
);

router.delete(
  "/delete-client/:id",
  authenticate,
  checkPermissionMiddleware("CLIENT MANAGEMENT", "delete-client"),
  deleteClient
);

router.get("/export-client", exportClientService);

export default router;
