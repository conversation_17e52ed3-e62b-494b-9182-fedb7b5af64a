import { deleteItem } from "../../utils/operation";

export const deleteCorporation = async (req, res) => {
  const id = req.params.id;
  await deleteItem({
    model: "corporation",
    fieldName: "corporation_id",
    id: Number(id),
    res: res as Response,
    req: req,
    successMessage: "corporation has been deleted",
  });
};

export const deleteUser = async (req, res) => {
    const id = req.params.id;
    await deleteItem({
      model: "user",
      fieldName: "id",
      id: Number(id),
      res: res as Response,
      req: req,
      successMessage: "User has been deleted",
    });
  };