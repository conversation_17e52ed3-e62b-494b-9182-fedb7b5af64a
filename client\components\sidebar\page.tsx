import React from "react";
import Sidebar from "./Sidebar";
import { NextRequest, NextResponse } from "next/server";
import { corporation_routes, employee_routes, superadmin_routes } from "@/lib/routePath";
import { getAllData, getCookie } from "@/lib/helpers";
import { log } from "node:console";

const SPage = async (req: NextRequest) => {
  const currentUser = await getAllData(employee_routes.GETCURRENT_USER);

  
  const userPermissions =
    currentUser?.role?.role_permission.map(
      (item: any) => item.permission.module
    ) || [];
  const adminCookie = await getCookie("corporationtoken");
  // const userAccessPath=[ "/user/daily_planning", "/user/tracker"]
  // const isUserPath = userAccessPath.some((path) =>
  //   req.nextUrl.pathname.startsWith(path)
  // );
  // const adminPermission=  isUserPath && adminCookie ? ["no_access"]:["allow_all"];
  const permissions = adminCookie ? "allow_all" : userPermissions;
  //  (adminCookie, "adminCookie");
  //  (permissions, "permissions");

  return (
    <div>
      <Sidebar permissions={permissions} profile={currentUser} />
    </div>
  );
};

export default SPage;
