import { Router } from "express";
import { getClientCustomFieldsByClientId, getCustomFieldsWithClients } from "../../controllers/customfields/clientcustomfields/view";
import { createClientCustomField } from "../../controllers/customfields/clientcustomfields/create";
import { mandatoryFields } from "../../controllers/customfields/mandatoryfields/view";
import { getCustomFields } from "../../controllers/customfields/view";
import { createCustomField } from "../../controllers/customfields/create";
import { updateCustomFieldOrder } from "../../controllers/customfields/clientcustomfields/update";

const router = Router();

router.get("/client-custom-fields/:clientId", getClientCustomFieldsByClientId);
router.get("/custom-fields-with-clients", getCustomFieldsWithClients);

//client custom fields
router.post("/client-custom-fields", createClientCustomField);

//Mandatory fields
router.get("/mandatory-fields", mandatoryFields);

//custom fields
router.post("/custom-fields", createCustomField);
router.get("/custom-fields", getCustomFields);


//client custom fields arrangement
router.post("/client-custom-fields/order", updateCustomFieldOrder);

export default router;