import { formatDate } from "@/lib/clientHelpers";
import { ColumnDef } from "@tanstack/react-table";

export const Column = (): ColumnDef<any>[] => [
  {
    header: "Sr. No.",
    cell: ({ row }) => {
      return <span className="text-center pl-4">{row.index + 1}</span>;
    },
  },
  {
    accessorKey: "pause_date",
    header: "Pause Date",
    cell: ({ row }) => {
      return (
        <span className="">
          {row.original?.pause_date
            ? formatDate(row.original?.pause_date)
            : "-"}
        </span>
      );
    },
  },
  {
    accessorKey: "pause_date",
    header: "Pause Time",
    cell: ({ row }) => {
      return (
        <span className=" ">
          {row.original?.pause_date
            ? new Date(row.original?.pause_date).toLocaleTimeString("en-US", {
                hour: "2-digit",
                minute: "2-digit",
                second: "2-digit",
                hour12: true,
              })
            : "-"}
        </span>
      );
    },
  },
  {
    accessorKey: "resume_date",
    header: "Resume Date",
    cell: ({ row }) => {
      return (
        <span className=" ">
          {row.original?.resume_date
            ? formatDate(row.original?.resume_date)
            : "-"}
        </span>
      );
    },
  },
  {
    accessorKey: "resume_date",
    header: "Resume Time",
    cell: ({ row }) => {
      return (
        <span className=" ">
          {row.original.resume_date
            ? new Date(row.original.resume_date).toLocaleTimeString("en-US", {
                hour: "2-digit",
                minute: "2-digit",
                second: "2-digit",
                hour12: true,
              })
            : "-"}
        </span>
      );
    },
  },
  {
    header: "Duration",
    cell: ({ row }) => {
      // Check if both pause and resume dates exist
      if (row.original?.pause_date && row.original?.resume_date) {
        const pauseTime = new Date(row.original.pause_date).getTime();
        const resumeTime = new Date(row.original.resume_date).getTime();

        // Calculate the difference in milliseconds
        const diffInMs = resumeTime - pauseTime;

        // Skip negative differences (in case resume is before pause)
        if (diffInMs < 0) return "-";

        // Convert to hours, minutes, seconds
        const hours = Math.floor(diffInMs / (1000 * 60 * 60));
        const minutes = Math.floor((diffInMs % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diffInMs % (1000 * 60)) / 1000);

        // Format with padding to ensure 00:00:00 format
        const formattedHours = String(hours).padStart(2, "0");
        const formattedMinutes = String(minutes).padStart(2, "0");
        const formattedSeconds = String(seconds).padStart(2, "0");

        return (
          <span className="">
            {`${formattedHours}:${formattedMinutes}:${formattedSeconds}`}
          </span>
        );
      }
      return "-";
    },
  },
];
