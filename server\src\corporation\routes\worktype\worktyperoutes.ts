import { Router } from "express";
import { createWorktype } from "../../controllers/worktype/create";
import { viewWorktype } from "../../controllers/worktype/view";
import { updateWorktype } from "../../controllers/worktype/update";
import { deleteWorktype } from "../../controllers/worktype/delete";
import { authenticate } from "../../../middleware/authentication";
import { checkPermissionMiddleware } from "../../../middleware/checkPermission";

const router = Router();

router.post(
  "/create-worktype",
  authenticate,
  checkPermissionMiddleware("WORKTYPE MANAGEMENT", "create-workType"),
  createWorktype
);
router.get(
  "/get-all-worktype",
  authenticate,
  checkPermissionMiddleware("WORKTYPE MANAGEMENT", "view-workType"),
  viewWorktype
);
router.put(
  "/update-worktype/:id",
  authenticate,
  checkPermissionMiddleware("WORKTYPE MANAGEMENT", "update-workType"),
  updateWorktype
);
router.delete(
  "/delete-worktype/:id",
  authenticate,
  checkPermissionMiddleware("WORKTYPE MANAGEMENT", "delete-workType"),
  deleteWorktype
);

export default router;
