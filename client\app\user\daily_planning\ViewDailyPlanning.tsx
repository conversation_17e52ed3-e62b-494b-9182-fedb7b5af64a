"use client";
import React from "react";
import DataTable from "@/app/_component/DataTable";
import { columns } from "./columns";
import AllTables from "./AllTables";
import CustomTable from "./CustomTable";

const ViewDailyPlanning = ({
  DailyPlanningData,
  permissions,
  allClient,
  refresh,
  userData,
}: {
  DailyPlanningData: any[];
  permissions: any;
  allClient: any;
  refresh: any;
  userData: any;
}) => {
  return (
    <>
      <CustomTable
        DailyPlanningData={DailyPlanningData}
        permissions={permissions}
        allClient={allClient}
        refresh={refresh}
        userData={userData}
      />

      {/* <DataTable
      data={DailyPlanningData}
      columns={columns}
      filter
      filter_column="client_name" 
      showColDropDowns
      showPageEntries
      filter_column2="daily_planning_date"
      renderSubComponent={({ row }) => (
        <span className="w-full">
          <AllTables daily_planning_id={row.original.daily_planning_id} />
        </span>
      )}/> */}
    </>
  );
};

export default ViewDailyPlanning;
