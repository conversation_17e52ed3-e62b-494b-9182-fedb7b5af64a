import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTrigger,
} from "@/components/ui/dialog";
import DialogHeading from "./DialogHeading";
import CloseDialog from "./CloseDialog";
import { cn } from "@/lib/utils";
import TriggerButton from "./TriggerButton";

interface OpenDialogBoxProps {
  children: React.ReactNode;
  triggerButton?: React.ReactNode;
  heading: string;
  description: string;
  contentClassName?: string;
  type?: "add" | "edit";
  text?: string;
}

const OpenDialogBox = ({
  children,
  triggerButton,
  heading,
  description,
  contentClassName,
  type,
  text,
}: OpenDialogBoxProps) => {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Dialog
        open={open}
        onOpenChange={() => {
          setOpen(true);
        }}
      >
        <DialogTrigger>
          {triggerButton ? (
            triggerButton
          ) : (
            <TriggerButton type={type} text={text} />
          )}
        </DialogTrigger>
        <DialogContent
          className={cn("max-w-5xl  dark:bg-gray-800 cbar", contentClassName)}
          onOpenAutoFocus={(e) => e.preventDefault()}
        >
          <CloseDialog setOpen={setOpen} />
          <DialogHeader>
            <DialogHeading title={heading} description={description} />
          </DialogHeader>
          {children}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default OpenDialogBox;
