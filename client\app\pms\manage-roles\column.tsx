import { ColumnDef } from "@tanstack/react-table";
import UpdateRoles from "./UpdateRoles";
import { rolespermission_routes } from "@/lib/routePath";
import DeleteRow from "@/app/_component/DeleteRow";

interface Role {
  name: string;
  permissions: any[];
  id: any;
  role_permission: any[];
  corporation_id: any;
  created_at: any;
  updated_at: any;
}
export const column = (getAllPermission: any): ColumnDef<Role>[] => [
  {
    accessorKey: "Sr. No.",
    header: "Sr. No.",
    cell: ({ row }) => row.index + 1,
  },
  {
    accessorKey: "name",
    header: "Role",
    cell: ({ row }) => {
      const roleName = row.original?.name;
      return <span className="font-bold ">{roleName}</span>;
    },
  },
  {
    accessorKey: "permissions",
    header: "Permissions",
    cell: ({ row }) => {
      const rolePermissions = row.original?.role_permission;
      return (
        <div>
          <div className="flex flex-wrap items-center   gap-2 my-2">
            {rolePermissions && rolePermissions.length > 0 ? (
              rolePermissions?.map((perm: any, id) => (
                <div key={id}>
                  <span
                    className="bg-gray-500/20 text-xs my-2 p-1 rounded-md  uppercase"
                    key={perm.id}
                  >
                    {" "}
                    {perm.permission?.action}
                  </span>
                  {/* Accessing action and module within role_permission.permission */}
                </div>
              ))
            ) : (
              <span>No Permissions</span>
            )}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "action",
    header: "Action",
    id: "action",
    cell: ({ row }) => {
      const client = row?.original;
      //  (client);
      const roleId = client.id;
      return (
        <div className="flex  items-center">
          {/* Update and Delete actions (commented out here) */}
          <UpdateRoles getAllPermission={getAllPermission} client={client} />
          <DeleteRow
            route={`${rolespermission_routes.DELETE_ROLE}/${roleId}`}
          />
        </div>
      );
    },
  },
];
