model TrackSheetCustomFieldMapping {
    id String @id @default(uuid())

    tracksheetId Int?
    trackSheet   TrackSheets? @relation(fields: [tracksheetId], references: [id], onDelete: Cascade)

    customFieldId String?
    customField   CustomField? @relation(fields: [customFieldId], references: [id], onDelete: Cascade)
    value         String?
    createdAt     DateTime     @default(now())
    updatedAt     DateTime     @default(now()) @updatedAt

    @@map("track_sheet_custom_field_mappings")
}
