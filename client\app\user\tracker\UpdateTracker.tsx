import CheckBoxesComp from "@/app/_component/CheckBoxComp";
import DialogHeading from "@/app/_component/DialogHeading";
import FormInput from "@/app/_component/FormInput";
import FormRadio from "@/app/_component/FormRadio";
import FormTimePicker from "@/app/_component/FormTimePicker";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import TriggerButton from "@/app/_component/TriggerButton";
import {
  DialogContent,
  DialogHeader,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { showingToast } from "@/lib/clientHelpers";
import { formSubmit, getAllData } from "@/lib/helpers";
import {
  carrier_routes,
  category_routes,
  client_routes,
  employee_routes,
  workreport_routes,
  worktype_routes,
} from "@/lib/routePath";
import { convertTimeToDate, convertTimeToDates } from "@/lib/swrFetching";
import useDynamicForm from "@/lib/useDynamicForm";
import {
  addTaskSchemaPlus,
  addWorkReportSchema,
  updateWorkReportSchema,
} from "@/lib/zodSchema";
import { Dialog } from "@radix-ui/react-dialog";
import { add, format } from "date-fns";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import { z } from "zod";

const UpdateTracker = ({
  workReport,
}: any) => {
  const router = useRouter();

  const [open, setOpen] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [carrierByClient, setCarrierByClient] = useState([]);

  // Initialize the form with default values from the provided workReport data
  const { form } = useDynamicForm(updateWorkReportSchema, {
  
    startTime: workReport?.start_time
      ? format(new Date(workReport?.start_time), "hh:mm a")
      : "", // Removed seconds from the format
    endTime: workReport?.finish_time
      ? format(new Date(workReport?.finish_time), "hh:mm a")
      : "", // Removed seconds from the format
  });



  const onSubmit = async (values: any) => {

    try {
      const formData = {
        start_time: convertTimeToDates(values.startTime),
        end_time: convertTimeToDates(values.endTime),
      };

      //  (formData, "formData");

      const res = await formSubmit(
        `${workreport_routes.UPDATE_WORK_REPORT}/${workReport?.id}`,
        "PUT",
        formData
      );

      if (res.success) {
        toast.success(res.message);
        setIsDialogOpen(false);
        router.refresh();
        form.reset();
      } else {
        toast.error(res.message || "Something went wrong");
      }
    } catch (error) {
      toast.error("An error occurred while updating the work report.");
      console.error(error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger title="Update">
        <TriggerButton type="edit" />
      </DialogTrigger>
      <DialogContent className="max-w-4xl dark:bg-gray-800 space-y-5 overflow-y-auto">
        <DialogHeader>
          <DialogHeading
            title="Update Work Report"
            description="Update work report"
          />
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(
                (values) => onSubmit(values),
                (errors) => console.error("Validation Errors:", errors)
              )}
            >
              <div className="grid grid-cols-1 items-center space-y-5">

               <div className="grid grid-cols-2 gap-5 mt-5">
               <FormTimePicker
                    form={form}
                    label="Start Time"
                    name="startTime"
                    placeholder="hh:mm AM/PM"
                    isRequired
                  />

                  <FormTimePicker
                    form={form}
                    label="End Time"
                    name="endTime"
                    placeholder="hh:mm AM/PM"
                    isRequired
                  />

               </div>
               
                <div className="">
                  <SubmitBtn
                    text="Submit"
                    className=" bg-primary text-secondary hover:bg-primary/90 w-full"
                  />
                </div>
              </div>
            </form>
          </Form>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
};

export default UpdateTracker;
