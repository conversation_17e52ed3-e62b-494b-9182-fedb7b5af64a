/* eslint-disable react-hooks/rules-of-hooks */
import toast from "react-hot-toast";
export const showingToast = async ({
  data,
  form,
  setOpen,
}: {
  data: any;
  form?: any;
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  if (data.success) {
    toast.success(data.message, {
      position: "top-right",
      style: {
        background: "#fff",
      },
    });
    setOpen && setOpen(false);
    form && form?.reset();
  } else {
    toast.error(data.message, {
      position: "top-right",
      style: {
        background: "#fff",
      },
    });
  }
};

export const formatDate = (originalDate: any) => {
  if (!originalDate) return "-";
  const dateObject = new Date(originalDate);
  const day = dateObject.getDate().toString().padStart(2, "0");
  const month = (dateObject.getMonth() + 1).toString().padStart(2, "0");
  const year = dateObject.getFullYear().toString();
  const formattedDate = `${year}/${month}/${day}`;
  return formattedDate;
};

export const hasPermission = ({
  permission,
  permission_data,
}: {
  permission: string;
  permission_data: string[];
}) => {
  //  (permission_data)
  return (
    permission_data.includes("allow_all") ||
    permission_data.includes(permission)
  );
};

export const calculateChargeTotals = ({
  charges,
  freight_charge,
}: {
  // charges: {
  //   amount: string;
  //   charge_type_id: string;
  //   quantity_billed: string;
  //   charge_by: string;
  //   adj_amount?: string | undefined;
  //   charge_note?: string | undefined;
  //   adj_quantity?: string | undefined;
  //   charge_gl?: string | undefined;
  // }[];
  charges: any[];
  freight_charge: any[];
}) => {
  const chargeAmounts = charges.map((item: any) => item.amount);
  const freightChargeAmount = freight_charge.map((item) => item.billed_amnt);
  const total_freight_charge = freightChargeAmount
    .reduce((acc, curr) => Number(acc) + Number(curr), 0)
    .toFixed(2);

  const mergedCharges = [...freightChargeAmount, ...chargeAmounts];
  const total_charges = mergedCharges
    .reduce((acc, curr) => Number(acc) + Number(curr), 0)
    .toFixed(2);

  // Get all charge approved amounts
  const freightChargeApproved = freight_charge
    .map((item) => Number(item.approved_billed_amnt))
    .filter((adjAmount) => !isNaN(adjAmount) && adjAmount > 0);
  const chargeApproved = charges
    .map((item) => Number(item.adj_amount))
    .filter((adjAmount) => !isNaN(adjAmount) && adjAmount > 0);
  const mergedChargesApproved = [...freightChargeApproved, ...chargeApproved];

  if (mergedChargesApproved?.length === 0)
    // No approved charges
    return {
      total_charges,
      total_adj_charge: "",
      total_freight_charge,
      total_adj_freight_charge: "",
    };
  //>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<

  // Add adj amount property to all freight charges
  const t = freight_charge.map((item) => {
    let t_amount = item.billed_amnt;
    let t_adj_amount = item.approved_billed_amnt;
    return {
      ...item,
      // for charges that has approved amount adj_amount will contain approved amount value if not than it will contain billed amount value
      adj_amount: t_adj_amount ? t_adj_amount : t_amount,
    };
  });

  // Add adj amount property to all charges
  const t2 = charges.map((item) => {
    let t_amount = item.amount;
    let t_adj_amount = item.adj_amount;
    return {
      ...item,
      // for charges that has approved amount adj_amount will contain approved amount value if not than it will contain billed amount value
      adj_amount: t_adj_amount ? t_adj_amount : t_amount,
    };
  });

  // get all freight charges adj amount
  const freightChargeAdjAmounts = t
    .map((item) => Number(item.adj_amount))
    .filter((adj) => !isNaN(adj) && adj > 0);
  // get all charges adj amount
  const chargeAdjAmounts = t2
    .map((item) => Number(item.adj_amount))
    .filter((adj) => !isNaN(adj) && adj > 0);
  // merge all adj amount from freight charges and other charges to calculate total adj amount
  const mergedAdjAmounts = [...freightChargeAdjAmounts, ...chargeAdjAmounts];

  // calculate total adj amount using the merged adj amount array
  const total_adj_charge =
    mergedAdjAmounts.length > 0
      ? mergedAdjAmounts
          .reduce((acc, curr) => Number(acc) + Number(curr), 0)
          .toFixed(2)
      : "";

  // claculte freight charge total adj amount
  const total_adj_freight_charge =
    freightChargeApproved.length > 0
      ? freightChargeAdjAmounts
          .reduce((acc, curr) => Number(acc) + Number(curr), 0)
          .toFixed(2)
      : "";

  return {
    total_charges,
    total_adj_charge,
    total_freight_charge,
    total_adj_freight_charge,
  };
};

export const calculateTotalTaxCharge = ({
  taxCharges,
}: {
  taxCharges: {
    tax_id: string;
    tax_name?: string;
    amount: string;
    tax_approved_amount?: string;
    qty_billed?: string;
  }[];
}) => {
  const taxBillAmt = taxCharges.map((item) => item.amount);
  // Calculate total tax charge
  const total_tax_charge = taxBillAmt
    .reduce((a, b) => Number(a) + Number(b), 0)
    .toFixed(2);

  const watchedT = taxCharges
    .map((item) => Number(item.tax_approved_amount))
    .filter((adjAmount) => !isNaN(adjAmount) && adjAmount > 0).length;

  // If no adj amount return
  if (watchedT === 0)
    return { total_tax_charge: total_tax_charge, total_adj_tax_charge: "" };

  // Add tax adjusted amount property to taxCharges Objects
  const t = taxCharges.map((item) => {
    let t_amount = item.amount;
    let t_adj_amount = item.tax_approved_amount;
    return {
      ...item,
      tax_adjusted_amount: t_adj_amount ? t_adj_amount : t_amount,
    };
  });

  // Get all charges tax adjusted amount
  const totalAdjAmount = t
    .map((item) => Number(item.tax_approved_amount))
    .filter((adjAmount) => !isNaN(adjAmount) && adjAmount > 0);

  // Calculate adj tax charge
  const total_adj_tax_charge =
    totalAdjAmount.length > 0
      ? totalAdjAmount.reduce((a, b) => Number(a) + Number(b), 0).toFixed(2)
      : "";
  return { total_tax_charge, total_adj_tax_charge };
};

export const isValidJSON = (data: string | undefined | null) => {
  try {
    if (!data) return { isValid: false };
    const returnedData = JSON.parse(data);
    return { returnedData, isValid: true };
  } catch (error) {
    return { isValid: false };
  }
};

export const convertProvinceToProvinceCode = (province: string) => {
  if (province) {
    switch (
      province.toUpperCase() // Convert the input to uppercase
    ) {
      // Canada Provinces
      case "ONTARIO":
        return "ON";
      case "ALBERTA":
        return "AB";
      case "BRITISH COLUMBIA":
        return "BC";
      case "MANITOBA":
        return "MB";
      case "NEW BRUNSWICK":
        return "NB";
      case "NEWFOUNDLAND AND LABRADOR":
        return "NL";
      case "NOVA SCOTIA":
        return "NS";
      case "NUNAVUT":
        return "NU";
      case "PRINCE EDWARD ISLAND":
        return "PE";
      case "SASKATCHEWAN":
        return "SK";
      case "YUKON":
        return "YT";

      // U.S. States
      case "ALABAMA":
        return "AL";
      case "ALASKA":
        return "AK";
      case "ARIZONA":
        return "AZ";
      case "ARKANSAS":
        return "AR";
      case "CALIFORNIA":
        return "CA";
      case "COLORADO":
        return "CO";
      case "CONNECTICUT":
        return "CT";
      case "DELAWARE":
        return "DE";
      case "DISTRICT OF COLUMBIA":
        return "DC";
      case "FLORIDA":
        return "FL";
      case "GEORGIA":
        return "GA";
      case "HAWAII":
        return "HI";
      case "IDAHO":
        return "ID";
      case "ILLINOIS":
        return "IL";
      case "INDIANA":
        return "IN";
      case "IOWA":
        return "IA";
      case "KANSAS":
        return "KS";
      case "KENTUCKY":
        return "KY";
      case "LOUISIANA":
        return "LA";
      case "MAINE":
        return "ME";
      case "MARYLAND":
        return "MD";
      case "MASSACHUSETTS":
        return "MA";
      case "MICHIGAN":
        return "MI";
      case "MINNESOTA":
        return "MN";
      case "MISSISSIPPI":
        return "MS";
      case "MISSOURI":
        return "MO";
      case "MONTANA":
        return "MT";
      case "NEBRASKA":
        return "NE";
      case "NEVADA":
        return "NV";
      case "NEW HAMPSHIRE":
        return "NH";
      case "NEW JERSEY":
        return "NJ";
      case "NEW MEXICO":
        return "NM";
      case "NEW YORK":
        return "NY";
      case "NORTH CAROLINA":
        return "NC";
      case "NORTH DAKOTA":
        return "ND";
      case "OHIO":
        return "OH";
      case "OKLAHOMA":
        return "OK";
      case "OREGON":
        return "OR";
      case "PENNSYLVANIA":
        return "PA";
      case "RHODE ISLAND":
        return "RI";
      case "SOUTH CAROLINA":
        return "SC";
      case "SOUTH DAKOTA":
        return "SD";
      case "TENNESSEE":
        return "TN";
      case "TEXAS":
        return "TX";
      case "UTAH":
        return "UT";
      case "VERMONT":
        return "VT";
      case "VIRGINIA":
        return "VA";
      case "WASHINGTON":
        return "WA";
      case "WEST VIRGINIA":
        return "WV";
      case "WISCONSIN":
        return "WI";
      case "WYOMING":
        return "WY";
      default:
        return province; // Return as-is if no match is found
    }
  }
};

export const convertProvinceCodeToProvince = (provinceCode: string) => {
  if (provinceCode) {
    switch (
      provinceCode.toUpperCase() // Convert the input to uppercase
    ) {
      // Canada Provinces
      case "ON":
        return "Ontario";
      case "AB":
        return "Alberta";
      case "BC":
        return "British Columbia";
      case "MB":
        return "Manitoba";
      case "NB":
        return "New Brunswick";
      case "NL":
        return "Newfoundland and Labrador";
      case "NS":
        return "Nova Scotia";
      case "NU":
        return "Nunavut";
      case "PE":
        return "Prince Edward Island";
      case "SK":
        return "Saskatchewan";
      case "YT":
        return "Yukon";

      // U.S. States
      case "AL":
        return "Alabama";
      case "AK":
        return "Alaska";
      case "AZ":
        return "Arizona";
      case "AR":
        return "Arkansas";
      case "CA":
        return "California";
      case "CO":
        return "Colorado";
      case "CT":
        return "Connecticut";
      case "DE":
        return "Delaware";
      case "DC":
        return "District of Columbia";
      case "FL":
        return "Florida";
      case "GA":
        return "Georgia";
      case "HI":
        return "Hawaii";
      case "ID":
        return "Idaho";
      case "IL":
        return "Illinois";
      case "IN":
        return "Indiana";
      case "IA":
        return "Iowa";
      case "KS":
        return "Kansas";
      case "KY":
        return "Kentucky";
      case "LA":
        return "Louisiana";
      case "ME":
        return "Maine";
      case "MD":
        return "Maryland";
      case "MA":
        return "Massachusetts";
      case "MI":
        return "Michigan";
      case "MN":
        return "Minnesota";
      case "MS":
        return "Mississippi";
      case "MO":
        return "Missouri";
      case "MT":
        return "Montana";
      case "NE":
        return "Nebraska";
      case "NV":
        return "Nevada";
      case "NH":
        return "New Hampshire";
      case "NJ":
        return "New Jersey";
      case "NM":
        return "New Mexico";
      case "NY":
        return "New York";
      case "NC":
        return "North Carolina";
      case "ND":
        return "North Dakota";
      case "OH":
        return "Ohio";
      case "OK":
        return "Oklahoma";
      case "OR":
        return "Oregon";
      case "PA":
        return "Pennsylvania";
      case "RI":
        return "Rhode Island";
      case "SC":
        return "South Carolina";
      case "SD":
        return "South Dakota";
      case "TN":
        return "Tennessee";
      case "TX":
        return "Texas";
      case "UT":
        return "Utah";
      case "VT":
        return "Vermont";
      case "VA":
        return "Virginia";
      case "WA":
        return "Washington";
      case "WV":
        return "West Virginia";
      case "WI":
        return "Wisconsin";
      case "WY":
        return "Wyoming";
      default:
        return provinceCode; // Return as-is if no match is found
    }
  }
};
