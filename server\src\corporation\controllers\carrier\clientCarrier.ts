import { handleError } from "../../../utils/helpers";

export const Carrier = async (req, res) => {
  try {
    const { CarrierName } = req.query;

    const searchConditions: any[] = [];

    if (CarrierName?.trim()) {
      searchConditions.push({
        name: {
          contains: CarrierName.trim(),
          mode: "insensitive",
        },
      });
    }

    const whereClause = {
      AND: [],
    };

    if (searchConditions.length > 0) {
      whereClause.AND.push({
        AND: searchConditions,
      });
    }

    const data = await prisma.carrier.findMany({
      where: whereClause,
      include: {
        WorkReport: true,
      },
      orderBy: {
        id: "desc",
      },
    });

    const datalength = await prisma.carrier.count({
      where: whereClause,
    });

    return res.status(200).json({
      data,
      total: datalength,
    });
  } catch (error) {
    return handleError(res, error);
  }
};
