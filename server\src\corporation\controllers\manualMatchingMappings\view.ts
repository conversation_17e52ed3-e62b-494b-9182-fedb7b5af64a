import { handleError } from "../../../utils/helpers";


export const viewManualMatchingMapping = async (req, res) => {
  try {

    const data = await prisma.manualMatchingMapping.findMany({
      orderBy: { id: "desc" },
    });
    if (data) {
      return res.status(200).json(data);
    }
    return res.status(404).json({ message: "ManualMatchingMapping not found" });
  } catch (error) {
    return handleError(res, error);
  }
};

export const viewManualMatchingMappingById = async (req, res) => {
  try {
    const { id } = req.params;
    const data = await prisma.manualMatchingMapping.findUnique({
      where: { id: id },
    });
    if (data) {
      return res.status(200).json(data);
    }
    return res.status(404).json({ message: "ManualMatchingMapping not found" });
  } catch (error) {
    return handleError(res, error);
  }
};