import { updateItem } from "../../../utils/operation";

export const  updateUserTitle = async (req, res) => {
    const id= req.params.id;
  const fields = {
    title: req.body.title, 
    level: req.body.level, 
  };

  await updateItem({
    model: "userTitle",
    fieldName: "id",
    fields: fields,
    id: Number(id),
    res,
    req,
    successMessage: "User title has been updated",
  });
};
