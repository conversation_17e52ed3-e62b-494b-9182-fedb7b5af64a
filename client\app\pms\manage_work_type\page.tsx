import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import DialogHeading from "@/app/_component/DialogHeading";
import CreateEmployee from "@/app/pms/manage_employee/createEmployee";
import { FaPlus } from "react-icons/fa";
import { getAllData, getCookie } from "@/lib/helpers";
import { category_routes, employee_routes, worktype_routes } from "@/lib/routePath";
import AddWork from "./addWork";
import ViewWork from "./ViewWork";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { PermissionWrapper } from "@/lib/permissionWrapper";

const workPage = async () => {
  const allWorkType = await getAllData(worktype_routes.GETALL_WORKTYPE);
  const allCategory = await getAllData(category_routes.GETALL_CATEGORY);
  

  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];

  const corporationCookie = await getCookie("corporationtoken");

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  return (
    <>
      <div className="w-full p-2 pl-4">
        <div className="h-9 flex items-center ">
          <AdminNavBar
            link={"/pms/manage_work_type"} 
            name={"Work Type"}
          />
        </div>
        <div className="space-y-2 ">
          <h1 className="text-2xl">Manage Work Type</h1>
          <p className="text-sm text-gray-700">Here you can manage Your Work</p>
        </div>
        <div className="w-full">
          <div className="flex justify-end mb-4">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["create-workType"]}
            >
              <AddWork allCategory={allCategory} />
            </PermissionWrapper>
          </div>
          <div className="w-full  animate-in fade-in duration-1000">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["view-workType"]}
            >
              <ViewWork data={allWorkType} permissions={permissions} allCategory={allCategory}/>
            </PermissionWrapper>
          </div>
        </div>
      </div>
    </>
  );
};

export default workPage;