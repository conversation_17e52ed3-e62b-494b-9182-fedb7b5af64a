model ManualMatchingMapping {
    id                String       @id @default(uuid())
    division          String?      @db.VarChar()
    company           String?      @db.VarChar()
    brandDivisionName String?      @db.VarChar()
    ManualShipment    String?      @db.VarChar()
    corporationId     Int?
    corporation       Corporation? @relation(fields: [corporationId], references: [corporation_id], onDelete: Cascade)
    createdAt         DateTime     @default(now())
    updatedAt         DateTime     @default(now()) @updatedAt

    @@map("manual_matching_mappings")
}
