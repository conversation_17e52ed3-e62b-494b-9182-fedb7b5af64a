import { Request, Response } from "express";
import { updateItem } from "../../../utils/operation";

export const updateWorktype = async (req, res) => {
  const id = req.params.id;
  const fields = {
    work_type: req.body.work_type,
    category_id: Number(req.body.category),
    is_work_carrier_specific: req.body.is_work_carrier_specific,
    does_it_require_planning_number: req.body.does_it_require_planning_number,
    is_backlog_regular_required: req.body.is_backlog_regular_required,
  };
  await updateItem({
    model: "workType",
    fieldName: "id",
    fields: fields,
    id: Number(id),
    res,
    req,
    successMessage: "work Type has been updated",
  });
};
