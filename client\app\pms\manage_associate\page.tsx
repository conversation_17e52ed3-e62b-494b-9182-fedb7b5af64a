import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { getAllData, getCookie } from "@/lib/helpers";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import { associate_routes, employee_routes } from "@/lib/routePath";
import AddAssociate from "./AddAssociate";
import ViewAssociate from "./ViewAssociate";

const AssociatePage = async () => {
  const allAssociate = await getAllData(associate_routes.GETALL_ASSOCIATE);
  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];
  const corporationCookie = await getCookie("corporationtoken");

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  return (
    <>
      <div className="w-full pl-4 p-2">
        <div className="h-9 flex items-center">
          <AdminNavBar
            link={"/pms/manage_associate"}
            name={"Manage Associate"}
          />
        </div>
        <div className="space-y-2">
          <h1 className="text-2xl">Manage Associate</h1>
          <p className="text-sm text-gray-700">Here You Can Manage Associate</p>
        </div>
        <div className="w-full">
          <div className="flex justify-end">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["create-associate"]}
            >
              <AddAssociate  />
            </PermissionWrapper>
          </div>
          <div className="w-full py-4 animate-in fade-in duration-1000">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["view-associate"]}
            >
              <ViewAssociate data={allAssociate.data} permissions={permissions} />
            </PermissionWrapper>
          </div>
        </div>
      </div>
    </>
  );
};

export default AssociatePage;
