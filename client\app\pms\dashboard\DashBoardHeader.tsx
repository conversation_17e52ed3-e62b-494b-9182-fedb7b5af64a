"use client";
import React from "react";
export interface Client {
  client_name: string;
  owner_name: string;
  country: string;
  client_id: any;
  corporation_id: any;
  created_at: any;
  updated_at: any;
  // permissions: string[];
}

export interface Carrier {
  name: string;
  register1: string;
  code: string;
  country: string;
  state: string;
  city: string;
  address: string;
  phone: string;
  postalcode: string;
  carrier_id: any;
}
const DashBoardHeader = ({
  allClient,
  allCarrier,
  allEmployee,
  workDone,
  workPending
}: {
  allClient: Client[];
  allCarrier: Carrier[];
  allEmployee: any;
  allworkreport: any;
  allDailyPlanning: any;
  workDone: any
  workPending: any
}) => {
  

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 pb-4">
      <div className="bg-white p-6 rounded-xl shadow-md border border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-gray-700 font-medium ">Employee</h3>
           
        </div>
        <p className="text-2xl font-bold text-gray-900 mt-2">
          {allEmployee.length}
        </p>
      </div>

      <div className="bg-white p-6 rounded-xl shadow-md border border-gray-200">
        <div className="flex items-center justify-between">
           <h3 className="text-gray-700 font-medium ">Clients</h3>
           
        </div>
        <p className="text-2xl font-bold text-gray-900 mt-2">
          {allClient.length}
        </p>
      </div>
      <div className="bg-white p-6 rounded-xl shadow-md border border-gray-200">
        <div className="flex items-center justify-between">
           <h3 className="text-gray-700 font-medium ">Carriers</h3>
           
        </div>
        <p className="text-2xl font-bold text-gray-900 mt-2">
          {allCarrier.length}
        </p>
      </div>
      <div className="bg-white p-6 rounded-xl shadow-md border border-gray-200">
        <div className="flex items-center justify-between">
           <h3 className="text-gray-700 font-medium ">Work Done</h3>
           
        </div>
        <p className="text-2xl font-bold text-gray-900 mt-2">
          {workDone.length}
        </p>
      </div>

      <div className="bg-white p-6 rounded-xl shadow-md border border-gray-200">
        <div className="flex items-center justify-between">
           <h3 className="text-gray-700 font-medium ">Pending Work</h3>
          
        </div>
        <p className="text-2xl font-bold text-gray-900 mt-2">
          {workPending.length}
        </p>
      </div>
    </div>
  );
};

export default DashBoardHeader;
