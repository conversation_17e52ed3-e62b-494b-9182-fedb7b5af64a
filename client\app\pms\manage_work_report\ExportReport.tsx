"use client";
import React, { useContext, useState } from "react";
import { Button } from "@/components/ui/button";
import { WorkReportContext } from "./WorkReportContext";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import { workreport_routes } from "@/lib/routePath";
import toast from "react-hot-toast";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { useRouter } from "next/navigation";

const formatDate = (date: any) => {
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(d.getDate()).padStart(2, "0")}`;
};

const ExportReport = ({ permissions, data, params }: any) => {
  const exportParams = new URLSearchParams(params);

  exportParams.delete("pageSize");
  exportParams.delete("page");

  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const exportWorkReport = async () => {
    setIsLoading(true); // Start loading

    try {
      const response = await fetch(
        `${workreport_routes.EXCEL_REPORT}?${exportParams.toString()}`,
        {
          method: "GET",
        }
      );

      if (response.ok) {
        const blob = await response.blob();

        if (!blob || blob.size === 0) {
          throw new Error("Error: Received an empty or invalid file.");
        }

        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = `Work_Report_${formatDate(new Date())}.xlsx`;
        link.click();
        router.refresh();
        toast.success("Work Reports Exported Successfully");
      } else {
        throw new Error(`Unexpected response: ${response.statusText}`);
      }
    } catch (error) {
      console.error("Error exporting work reports:", error);
      toast.error(`Error exporting work reports: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <PermissionWrapper
        permissions={permissions}
        requiredPermissions={["view-workReport"]}
      >
        <Button
          onClick={exportWorkReport}
          className="bg-gradient-to-r from-main-color/70 to-[#065c6d] mb-1 px-3 mt-1 py-1.5 max-h-8 hover:to-main-color-foreground mr-2 text-white font-semibold uppercase"
          disabled={isLoading}
        >
          {isLoading ? (
            <span className="animate-spin">
              <AiOutlineLoading3Quarters />
            </span>
          ) : (
            "Download report"
          )}
          {isLoading ? "Exporting..." : ""}{" "}
        </Button>
      </PermissionWrapper>
    </>
  );
};

export default ExportReport;
