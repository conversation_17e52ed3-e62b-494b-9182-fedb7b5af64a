import BreadCrumbs from "@/app/_component/BreadCrumbs";
import React from "react";

interface AdminNavBarProps {
  link: string;
  name: string;
}

export const UserNavBar = ({ link, name }: AdminNavBarProps) => {
  const list = [
    {
      link: "/user/user-profile",
      name: "User Profile",
    },
    {
      link: link,
      name: name,
    },
  ];

  return <BreadCrumbs breadcrumblist={list} />;
};
