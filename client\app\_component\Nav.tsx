"use client";
import { Users } from "lucide-react";
import React from "react";
import NavLink from "./NavLink";
import {
  FaFileInvoiceDollar,
  FaRegMoneyBillAlt,
  FaTruck,
  FaUserCog,
} from "react-icons/fa";
import { FaListAlt } from "react-icons/fa";
import { FaUserShield } from "react-icons/fa";
import AccordionDown from "./AccordionDown";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { AiFillDollarCircle } from "react-icons/ai";
import { FaUsers } from "react-icons/fa";
import { RiDashboardLine } from "react-icons/ri";
import { IoMdAnalytics } from "react-icons/io";
import { hasPermission } from "@/lib/clientHelpers";
import { FaBook } from "react-icons/fa6";

const Nav = ({
  open,
  className,
  permissions,
}: {
  open: boolean;
  className?: string;
  permissions: string[];
}) => {
  const path = usePathname();
  let currentUser;
  if (path.startsWith("/superadmin")) {
    currentUser = "superadmin";
  } else {
    currentUser = "freightadmin";
  }

  const clientRoutes = [
    {
      label: "Client",
      path: "/freight_audit/manage_clients",
      permission: "clientProfile",
    },
    {
      label: "Client Carrier",
      path: "/freight_audit/client_carrier",
      permission: "clientSetup",
    },
  ];

  const permissionsClientRoutes = clientRoutes.filter((route) => {
    return hasPermission({
      permission: route.permission,
      permission_data: permissions,
    });
  });

  const rateRoutes = [
    {
      label: "Manage Rates Contract",
      path: "/freight_audit/manage_rates",
      permission: "rateHeaders",
    },
    {
      label: "Manage Fuel",
      path: "/freight_audit/manage_rates/fuel_management",
      permission: "addBulletin",
    },
    {
      label: "Find Rates",
      path: "/freight_audit/manage_rates/find_rates",
      permission: "findRate",
    },
    {
      label: "Rate Lookup",
      path: "/freight_audit/manage_rates/rate_lookup",
      permission: "rateLookup",
    },
  ];

  const userRoutes = [
    {
      label: "Manage User",
      path: "/freight_audit/manage_users",
      permission: "manageUsers",
    },
    {
      label: "Manage Roles",
      path: "/freight_audit/manage_roles",
      permission: "manageRoles",
    },
  ];

  const permissionsRateRoutes = rateRoutes.filter((route) => {
    return hasPermission({
      permission: route.permission,
      permission_data: permissions,
    });
  });

  const permissionsUserRoutes = userRoutes.filter((route) => {
    return hasPermission({
      permission: route.permission,
      permission_data: permissions,
    });
  });

  const iconClasses = "min-w-[20px] min-h-[20px] w-5 h-5 text-slate-200";

  return (
    <nav
      className={cn(
        "w-full h-[92vh] p-2 mx-1.5",
        "bg-slate-800 rounded-md",
        "flex flex-col gap-2",
        "transition-all duration-300 ease-in-out",
        "shadow-lg",
        className
      )}
    >
      {currentUser === "freightadmin" ? (
        <>
          <NavLink
            icon={<RiDashboardLine className={iconClasses} />}
            label={open ? "Dashboard" : ""}
            path="/freight_audit/dashboard"
            open={open}
          />
          <AccordionDown
            open={open}
            icon={<FaFileInvoiceDollar className={iconClasses} />}
            mainLabel={open ? "Invoice" : ""}
            routes={[
              {
                label: "Batch",
                path: "/freight_audit/invoice/batch",
              },
              {
                label: "Report",
                path: "/freight_audit/invoice/weekly_report",
              },
              {
                label: "Find Invoice",
                path: "/freight_audit/invoice/find_invoice",
              },
            ]}
          />
          {permissionsRateRoutes.length > 0 && (
            <AccordionDown
              open={open}
              icon={<AiFillDollarCircle className={iconClasses} />}
              mainLabel={open ? "Rates" : ""}
              routes={permissionsRateRoutes}
            />
          )}
          <AccordionDown
            open={open}
            icon={<FaRegMoneyBillAlt className={iconClasses} />}
            mainLabel={open ? "Invoice" : ""}
            routes={[
              {
                label: "Approve Invoices",
                path: "/freight_audit/invoice/approve_invoice",
              },
              {
                label: "To Be Paid Invoices",
                path: "/freight_audit/invoice/to_be_paid",
              },
              {
                label: "Journal",
                path: "/freight_audit/invoice/journal",
              },
            ]}
          />
          {hasPermission({
            permission: "coreTables",
            permission_data: permissions,
          }) && (
            <NavLink
              label={open ? "Freight Setup" : ""}
              path={"/freight_audit/freight_setup"}
              icon={<FaListAlt className={iconClasses} />}
              open={open}
            />
          )}
          {permissionsClientRoutes && permissionsClientRoutes.length > 0 && (
            <NavLink
              open={open}
              icon={<FaUsers className={iconClasses} />}
              label={open ? "Clients" : ""}
              path="/freight_audit/manage_clients"
            />
          )}
          {permissionsUserRoutes.length > 0 && (
            <AccordionDown
              open={open}
              icon={<FaUserCog className={iconClasses} />}
              mainLabel={open ? "Users" : ""}
              routes={permissionsUserRoutes}
            />
          )}
        </>
      ) : (
        <NavLink
          label={open ? "Freight Admin" : ""}
          path={"/superadmin/manage_freightadmins"}
          icon={<FaUserShield className={iconClasses} />}
          open={open}
        />
      )}
    </nav>
  );
};

export default Nav;
