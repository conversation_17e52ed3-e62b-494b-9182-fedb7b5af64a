import { Button } from "@/components/ui/button";
import { Minus, Plus } from "lucide-react";
import React from "react";

interface DynamicAddRemoveButtonProps {
  handleClick: () => void;
  type: "add" | "remove";
}
const DynamicAddRemoveButton = ({
  handleClick,
  type,
}: DynamicAddRemoveButtonProps) => {
  return (
    <>
      <Button
        type="button"
        onClick={handleClick}
        className=" p-2 bg-transparent border hover:bg-gray-200 duration-200"
      >
        {type === "add" ? (
          <Plus className="w-4 h-4 text-black" />
        ) : (
          <Minus className="w-4 h-4 text-red-500" />
        )}
      </Button>
    </>
  );
};

export default DynamicAddRemoveButton;
