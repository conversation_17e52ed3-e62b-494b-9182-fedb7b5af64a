import { NavBar } from "@/app/_component/NavBar";

import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import React from "react";
import Upload from "../Upload";
import Sidebar from "@/components/sidebar/Sidebar";
import { getAllData, getCookie } from "@/lib/helpers";
import { employee_routes } from "@/lib/routePath";
import BreadCrumbs from "@/app/_component/BreadCrumbs";

const page = async ({ params }: { params: { id: string } }) => {
  const { id } = params;
  // const importData = async ()=> {
  //   const data = await getAllData(upload_file.GET_ALL_UPLOADED_FILE);
  //    ;
  // }
  const userData = await getAllData(employee_routes.GETCURRENT_USER);
   const userPermissions = userData?.role?.role_permission || [];
 

  const corporationCookie = await getCookie("corporationtoken");

  // If corporationCookie doesn't exist, manually remove 'allow_all' from permissions
  // let permissions = userPermissions;
  const permissions = corporationCookie ? ["allow_all"] : userPermissions;
  return (
    <>
      <SidebarProvider>
        {/* <NavBar /> */}
        <Sidebar {...{ permissions }} profile={userData} />
        <div className="w-full h-full">
        <div className="absolute top-0 pl-4 p-2">
            <BreadCrumbs
              breadcrumblist={[
                { link: "/user/daily_planning", name: "Daily Planning" },
                {
                  link: `/user/daily_planning/import/${id}`,
                  name: "Upload Data",
                },
              ]}
            />
          </div>
          <Upload />
          {/* <ViewUploadedFile /> */}
        </div>
      </SidebarProvider>
    </>
  );
};

export default page;
