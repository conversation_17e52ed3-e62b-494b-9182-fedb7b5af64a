import { Router } from "express";
import { authenticate } from "../../../middleware/authentication";
import { createDailyPlanning } from "../../controllers/dailyPlanning/create";
import { viewDailyPlanning, viewDailyPlanningById, viewSpecificDailyPlanning } from "../../controllers/dailyPlanning/view";
import { updateDailyPlanning } from "../../controllers/dailyPlanning/update";
import { deleteDailyPlanning } from "../../controllers/dailyPlanning/delete";


const router = Router();

router.post("/create-dailyplanning",authenticate ,createDailyPlanning);
router.get("/get-all-dailyplanning", authenticate, viewDailyPlanning);
router.get("/get-specific-dailyplanning", authenticate, viewSpecificDailyPlanning);
router.get("/get-dailyplanning-by-id/:id", viewDailyPlanningById);
router.put("/update-dailyplanning/:id", authenticate, updateDailyPlanning);
router.delete("/delete-dailyplanning/:id", deleteDailyPlanning);


export default router;
