import { updateItem } from "../../../utils/operation";

export const updateBranch = async (req, res) => {
    const id  = req.params.id;
    
    const { corporation_id } = req;
    const fields = {
        branch_name: req.body.name,
        corporation_id: Number(corporation_id),
    };
    await updateItem({
        model: "branch",
        fieldName: "id",
        fields: fields,
        id: Number(id),
        res,
        req,
        successMessage: "branch has been updated",
    });
};