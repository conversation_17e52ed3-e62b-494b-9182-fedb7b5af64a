import { Request, Response } from "express";
import { PrismaClient, FieldType } from "@prisma/client";

const prisma = new PrismaClient();

export interface AuthenticatedRequest extends Request {
  user?: { username: string };
}

// --- GET: Fetch All Custom Fields with Client Usage ---
export const getCustomFieldsWithClients = async (req: Request, res: Response) => {
  try {
    // Extract search parameters
    const {
      page = "1",
      pageSize = "50",
      name,
      type,
      "Custom Field Name": customFieldName,
      "Field Type": fieldType,
      "Client List": clientList
    } = req.query;

    const pageNumber = parseInt(page as string);
    const pageSizeNumber = parseInt(pageSize as string);

    // Build where conditions for filtering
    const whereConditions: any = {};

    // Search by custom field name
    if (name || customFieldName) {
      const searchTerm = name || customFieldName;
      whereConditions.name = {
        contains: searchTerm as string,
        mode: "insensitive"
      };
    }

    // First, get ALL fields that match the basic where conditions (without pagination)
    const allFields = await prisma.customField.findMany({
      where: whereConditions,
      orderBy: { createdAt: "asc" },
      include: {
        ClientCustomFieldArrangement: {
          include: {
            Client: {
              select: {
                id: true,
                client_name: true,
                ownership: {
                  select: {
                    username: true
                  }
                },
                associate: {
                  select: {
                    name: true
                  }
                },
                branch: {
                  select: {
                    branch_name: true
                  }
                }
              }
            }
          }
        }
      },
    });

    // Transform ALL the data to show custom fields with their client usage
    let allFieldsWithClients = allFields.map(field => {
      // Create the formatted field type for search purposes
      let formattedType: string;
      if (field.type === "AUTO" && field.autoOption) {
        formattedType = `Auto - ${field.autoOption.charAt(0).toUpperCase() + field.autoOption.slice(1).toLowerCase()}`;
      } else if (field.type) {
        formattedType = field.type.charAt(0).toUpperCase() + field.type.slice(1).toLowerCase();
      } else {
        formattedType = "Text";
      }

      return {
        id: field.id,
        name: field.name,
        type: field.type,
        formattedType: formattedType,
        autoOption: field.autoOption,
        createdAt: field.createdAt,
        createdBy: field.createdBy,
        updatedAt: field.updatedAt,
        updatedBy: field.updatedBy,
        clients: field.ClientCustomFieldArrangement.map(arrangement => arrangement.Client),
        clientCount: field.ClientCustomFieldArrangement.length
      };
    });

    // Apply additional filters BEFORE pagination
    // Filter by field type if specified (search in formatted type)
    if (type || fieldType) {
      const searchTerm = (type || fieldType) as string;
      allFieldsWithClients = allFieldsWithClients.filter(field => {
        return field.formattedType.toLowerCase().includes(searchTerm.toLowerCase());
      });
    }

    // Filter by client list if specified
    if (clientList) {
      const searchTerm = (clientList as string).toLowerCase();
      allFieldsWithClients = allFieldsWithClients.filter(field => {
        const clientNames = field.clients.map(client => client.client_name.toLowerCase()).join(", ");
        return clientNames.includes(searchTerm);
      });
    }

    // Calculate the total count AFTER all filtering
    const totalFilteredCount = allFieldsWithClients.length;
    const totalPages = Math.ceil(totalFilteredCount / pageSizeNumber);

    // Apply pagination AFTER filtering
    const skip = (pageNumber - 1) * pageSizeNumber;
    const paginatedFields = allFieldsWithClients.slice(skip, skip + pageSizeNumber);

    res.status(200).json({
      data: paginatedFields,
      datalength: totalFilteredCount,
      page: pageNumber,
      pageSize: pageSizeNumber,
      totalPages: totalPages
    });
  } catch (error) {
    console.error("Error fetching custom fields with clients:", error);
    res.status(500).json({ error: "Server error" });
  }
};

// --- GET: Fetch Custom Fields by Client ID ---
export const getClientCustomFieldsByClientId = async (
  req: Request,
  res: Response
) => {
  const client_id = parseInt(req.params.clientId);

  if (isNaN(client_id)) {
    return res.status(400).json({ error: "Invalid client ID" });
  }

  try {
    // Get custom fields directly from arrangements table
    const arrangements = await prisma.clientCustomFieldArrangement.findMany({
      where: { client_id: client_id },
      include: {
        CustomField: {
          select: {
            id: true,
            name: true,
            type: true,
          }
        },
      },
      orderBy: { order: 'asc' },
    });

    if (arrangements.length === 0) {
      console.log('No custom fields found for client');
      return res.status(200).json({ props: [], custom_fields: [] });
    }

    const customFields = arrangements.map(arr => arr.CustomField);

    return res.status(200).json({
      props: [], // No longer using props
      custom_fields: customFields,
    });
  } catch (error) {
    console.error("Error fetching client custom fields:", error);
    return res.status(500).json({ error: "Server error" });
  }
};