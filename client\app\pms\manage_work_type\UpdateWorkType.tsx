"use client";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import useDynamicForm from "@/lib/useDynamicForm";
import { addWorkTypeSchema } from "@/lib/zodSchema";
import React, { useState } from "react";
import { toast } from "sonner";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { SelectItem } from "@/components/ui/select";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FaPlus } from "react-icons/fa";
import DialogHeading from "@/app/_component/DialogHeading";
import { Form } from "@/components/ui/form";
import { formSubmit } from "@/lib/helpers";
import { worktype_routes } from "@/lib/routePath";
import TriggerButton from "@/app/_component/TriggerButton";
import FormRadio from "@/app/_component/FormRadio";
import { useRouter } from "next/navigation";

const UpdateWorkType = ({ data, allCategory }: any) => {
  const router = useRouter();
  //  (data, "data");


  const { form } = useDynamicForm(addWorkTypeSchema, {
    work_type: data?.work_type || "",
    category: data?.category.id.toString() || "",
    does_it_require_planning_number:
      data?.does_it_require_planning_number.toString() || "",
    is_work_carrier_specific: data?.is_work_carrier_specific.toString() || "",
    is_backlog_regular_required:
      data?.is_backlog_regular_required.toString() || "",
  });
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  async function onSubmit(values: any) {
    try {
      const formData = {
        work_type: values.work_type,
        category: Number(values.category),
        is_work_carrier_specific:
          values.is_work_carrier_specific === "true" ? true : false,
        does_it_require_planning_number:
          values.does_it_require_planning_number === "true" ? true : false,
        is_backlog_regular_required:
          values.is_backlog_regular_required === "true" ? true : false,
      };

      const res = await formSubmit(
        `${worktype_routes.UPDATE_WORKTYPE}/${data.id}`,
        "PUT",
        formData
      );

      if (res.success) {
        toast.success(res.message);
        setIsDialogOpen(false);
        form.reset()
        router.refresh();
      } else {
        toast.error(
          res.error || "An error occurred while updating the work type."
        );
      }
    } catch (error) {
      toast.error("An error occurred while updating the work type.");
      console.error(error);
    }
  }

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger onClick={() => setIsDialogOpen(true)}>
          <TriggerButton type="edit" />
        </DialogTrigger>
        <DialogContent className="md:min-w-[40rem] min-w-[30rem]">
          <DialogHeading
            title="Update Work Type"
            description="Update Your Work Details"
          />
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="grid grid-cols-2 items-center gap-4">
                <FormInput
                  form={form}
                  label="Work Type"
                  name="work_type"
                  type="text"
                  isRequired
                />

                <SelectComp
                  form={form}
                  label="Category"
                  name="category"
                  isRequired
                  className=" w-full"
                  placeholder="Select Category"
                >
                  {allCategory?.map((item: any) => (
                    <SelectItem
                      key={item.id}
                      value={item.id.toString()}
                    >
                      {item.category_name}
                    </SelectItem>
                  ))}
                </SelectComp>
              </div>
              <div className="grid grid-cols-2 ">
                <FormRadio
                  form={form}
                  name="is_work_carrier_specific"
                  label="Is carrier required ?"
                  options={[
                    { value: "true", label: "YES" },
                    { value: "false", label: "NO" },
                  ]}
                />
                <FormRadio
                  form={form}
                  name="does_it_require_planning_number"
                  label="Actual No ?"
                  options={[
                    { value: "true", label: "YES" },
                    { value: "false", label: "NO" },
                  ]}
                  className="ml-3"
                />
                <FormRadio
                  form={form}
                  name="is_backlog_regular_required"
                  label="Is Backlog/Regular required ?"
                  options={[
                    { value: "true", label: "YES" },
                    { value: "false", label: "NO" },
                  ]}
                />
              </div>
              <SubmitBtn
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                text="Submit"
              />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdateWorkType;