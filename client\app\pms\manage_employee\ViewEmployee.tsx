"use client";
import React, { useState } from "react";
import { Employee, column } from "./column";
import DataTable from "@/app/_component/DataTable";
import DataGridTable from "@/app/_component/DataGridTable";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

const ViewEmployee = ({
  datas,
  data,
  permissions,
  allRoles,
  usertitle,
  allUsers,
  allClient,
  allBranch,
}: any
//  {
//   data: Employee[];
//   permissions: string[];
//   allRoles: any[];
//   usertitle: any;
//   allEmployee: any[];
// }
) => {
  const [totallength, setTotallength] = useState<number>(0);
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const pathname = usePathname();
  const { replace } = useRouter();

  
  const pageSizedata = parseInt(searchParams.get("pageSize"));
  const totaldatalength = Math.ceil(
    datas?.datalength / (pageSizedata ? pageSizedata : 50)
  );
  

  const pageSize = Number(searchParams.get("pageSize"));


  return (
    <div>
      <DataGridTable
        data={data}
        columns={column(permissions, allRoles, usertitle, data,allUsers, allClient, allBranch)}
        // filter
        // filter_column="username"
        showColDropDowns
        showPageEntries
        
        // filter2
        // filter_column2="email"
        pageSize={pageSize}
        totalPages={totallength ? totallength : totaldatalength}
      />
    </div>
  );
};

export default ViewEmployee;
