"use client";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  VisibilityState,
  getPaginationRowModel,
  PaginationState,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  RowSelectionState,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useContext, useEffect, useMemo, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ChevronLeft, ChevronRight, CloudHail } from "lucide-react";
import { FaFilter, FaSearch } from "react-icons/fa";
import { cn } from "@/lib/utils";
import DeleteMultipleRows from "./DeleteMultipleRows";
// import { rate_routes } from "@/lib/routePath";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { WorkReportContext } from "../pms/manage_work_report/WorkReportContext";
import { TrackerContext } from "../user/tracker/TrackerContext";
import Pagination from "./Pagination";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  isLoading?: boolean;
  showColDropDowns?: boolean;
  filter?: boolean;
  filter1PlaceHolder?: string;
  filter2?: boolean;
  filter3?: boolean;
  filter3view?: JSX.Element;
  total?: boolean;
  totalview?: JSX.Element;
  filter_column?: string;
  filter_column2?: string;
  filter_column3?: string;
  filter_column4?: string;
  overflow?: boolean;
  totalPages?: number;
  showPageEntries?: boolean;
  className?: string;
  showSearchColumn?: boolean;
  pageSize?: number;
  isTimerRunning?: any;
  setIsTimerRunning?: any;
  onFilteredDataChange?: (filteredData: TData[]) => void;
}
const ServerSideDataTable = <TData, TValue>({
  columns,
  data,
  isLoading,
  showColDropDowns,
  filter,
  filter2,
  filter3 = false,
  filter3view,
  total,
  totalview,
  filter_column,
  filter_column2,
  filter_column3,
  filter_column4,
  totalPages,
  showPageEntries,
  className,
  filter1PlaceHolder,
  showSearchColumn = true,
  pageSize,
  isTimerRunning,
  setIsTimerRunning,
  onFilteredDataChange,
}: DataTableProps<TData, TValue>) => {
  const [page, setPage] = useState<number>(pageSize || 50);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const pathname = usePathname();
  const { replace } = useRouter();

  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnSearchTerm, setColumnSearchTerm] = useState<any>();

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: page,
  });
  const { setFromDate, fromDate, toDate, setToDate } =
    useContext(WorkReportContext);
  const { setFDate, fDate, tDate, setTDate } = useContext(TrackerContext);
  const handlePageChange = (e: any) => {
    const newPageSize = parseInt(e.target.value);
    setPage(newPageSize);
    if (totalPages) {
      params.set("pageSize", newPageSize?.toString()); // set pageSize query
      replace(`${pathname}?${params.toString()}`);
    }
    setPagination((prevState) => ({
      ...prevState,
      pageSize: newPageSize,
    }));
  };
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const entriesPerPage = pagination.pageSize;
  const [ids, setIds] = useState<string[]>([]);
  useEffect(() => {
    const selectedIds = Object.keys(rowSelection).filter(
      (key) => rowSelection[key]
    );
    setIds(selectedIds);
  }, [rowSelection, setIds]);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnData, setColumnData] = useState("");
  const [header, setHeader] = useState("");
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  useEffect(() => {
    if (columnData !== "date") {
      setFromDate(""), setToDate("");
      setFDate(""), setTDate("");
    }
  }, [columnData]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),

    getFilteredRowModel: getFilteredRowModel(),
    onRowSelectionChange: setRowSelection, //hoist up the row selection state to your own scope

    getRowId: (row, index) => (row as any).rate_cell_id || index.toString(),
    state: {
      columnVisibility,
      pagination,
      sorting,
      columnFilters,
      rowSelection,
    },
    onColumnFiltersChange: setColumnFilters,
  });

  const handleColumnSelection = (columnKey: string, columnHeader: string) => {
    setSelectedColumns((prevSelectedColumns) => {
      let updatedColumns;
  
      if (prevSelectedColumns.includes(columnKey)) {
        updatedColumns = prevSelectedColumns.filter((col) => col !== columnKey);
  
        const updatedParams = new URLSearchParams(searchParams);
  
        if (columnHeader === 'Date') {
          updatedParams.delete('fDate');
          updatedParams.delete('tDate');
        } else {
          updatedParams.delete(columnHeader);
        }
  
        replace(`${pathname}?${updatedParams.toString()}`);
      } else {
        updatedColumns = [...prevSelectedColumns, columnKey];
  
        const updatedParams = new URLSearchParams(searchParams);
        updatedParams.set(columnHeader, ''); 
        replace(`${pathname}?${updatedParams.toString()}`);
      }
  
      return updatedColumns;
    });
  };
  
  const handleSearchChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    columnName: string
  ) => {
    const value = event.target.value.trim();
  
    if(value){
    //    const wordCount = value.trim().length;
    // if (wordCount >= 3) {
    
      const updatedParams = new URLSearchParams(searchParams);
      updatedParams.set(columnName, value);
      updatedParams.set("page", "1");
      //  ("updatedParams are:", updatedParams.toString())
      replace(`${pathname}?${updatedParams.toString()}`);
      
    // }
    }else{
      //  ("Will call else part with params",searchParams.toString())
      const updatedParams = new URLSearchParams(searchParams);
      updatedParams.delete(columnName);
      replace(`${pathname}?${updatedParams.toString()}`);
    }
  };

   const filterColumns = selectedColumns.length
    ? selectedColumns.map((columnKey) =>
        columns.find((col: any) => col.accessorKey === columnKey)
      )
    : [];
  return (
    <div className="animate-in fade-in duration-1000">
      <div className=" flex justify-between w-full  lg:w-full lg:flex lg:justify-end mb-3 animate-in fade-in duration-1000 gap-5  ">
        {showPageEntries && (
          <select
            onChange={handlePageChange}
            className="p-2 border rounded-lg !outline-main-color  bg-white"
          >
            {/* <option value={5}>5</option> */}
            <option value={50}>50</option>
            <option value={10}>10</option>
            <option value={15}>15</option>
            <option value={25}>25</option>
            <option value={100}>100</option>
            <option value={250}>250</option>
          </select>
        )}
        {showColDropDowns && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <p className="p-2 border-2 rounded-md flex items-center justify-center px-4 cursor-pointer">
                <FaFilter />
              </p>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white border-none   dark:bg-gray-900 dark:ring-1 dark:ring-gray-800" onSelect={(e) => e.preventDefault()}>
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize cursor-pointer"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value: any) =>
                      column.toggleVisibility(!!value)
                    }
                    onSelect={(e) => e.preventDefault()}
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
        {showSearchColumn && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <p className="p-2 border-2 rounded-md flex items-center justify-center px-4 cursor-pointer ">
                <FaSearch />
              </p>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white border-none dark:bg-gray-900 dark:ring-1 dark:ring-gray-800 " onSelect={(e) => e.preventDefault()}>
              <div className="px-3 py-2">
                <p className="font-semibold text-sm">Select Columns</p>
                {columns
                  .filter(
                    (item: any) =>
                      item.accessorKey !== "action" &&
                      item.accessorKey !== "Activity Log" &&
                      item.accessorKey !== "pause" && 
                      item.accessorKey !== "finish_time" &&
                      item.accessorKey !== "start_time" &&
                      item.accessorKey !== "time_spent" && 
                      item.accessorKey !== "Sr. No" &&
                      item.accessorKey !== "payment terms"
                  )
                  .map((item: any, id: any) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={id}
                        checked={selectedColumns.includes(item.accessorKey)}
                        onCheckedChange={() =>
                          handleColumnSelection(item.accessorKey,item.header)
                        }
                        className="capitalize cursor-pointer"
                        onSelect={(e) => e.preventDefault()}
                      >
                        {item.header}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {filterColumns.length > 0 && (
          <>
            {filterColumns.map((column: any, index: number) => {
              return column?.accessorKey !== "date" ? (
                <Input
                  key={index}
                  placeholder={`Search in ${column?.header}`}
                  // value={
                  //   table
                  //     .getColumn(`${column?.accessorKey}`)
                  //     ?.getFilterValue() as string
                  // }
                  // value={columnSearchTerm || ''}
                  onChange={(event) => handleSearchChange(event, column.header)}
                  // onChange={(event) =>
                  //   table
                  //     .getColumn(`${column?.accessorKey}`)
                  //     ?.setFilterValue(event.target.value)
                  // }
                  className="w-[20%] dark:bg-gray-700 !outline-main-color"
                />
              ) : (
                <div>{filter3view}</div>
              );
            })}
          </>
        )}

        {filter && filterColumns.length > 0 && (
          <>
            {filterColumns.map((column: any, index: number) => {
              <Input
                placeholder={
                  filter1PlaceHolder ? filter1PlaceHolder : filter_column
                }
                // value={columnSearchTerm|| ''}
                onChange={(event) => handleSearchChange(event, column.header)}
                // value={
                //   table.getColumn(`${filter_column}`)?.getFilterValue() as string
                // }
                // onChange={(event) =>
                //   table
                //     .getColumn(`${filter_column}`)
                //     ?.setFilterValue(event.target.value)
                // }
                className="w-[20%] dark:bg-gray-700  !outline-main-color"
              />;
            })}
          </>
        )}
        {filter2 && (
          <>
            {filterColumns.map((column: any, index: number) => {
              <Input
                placeholder={`${filter_column2}`}
                // value={
                //   table.getColumn(`${filter_column2}`)?.getFilterValue() as string
                // }
                // onChange={(event) =>
                //   table
                //     .getColumn(`${filter_column2}`)
                //     ?.setFilterValue(event.target.value)
                // }
                onChange={(event) => handleSearchChange(event, column.header)}
                className="w-[20%] dark:bg-gray-700 !outline-main-color"
              />;
            })}
          </>
        )}
        {/* {selectedColumns.includes("date") && filter3 && (
          <div>{filter3view}</div>
        )} */}
        {/* {ids.length > 0 && (
          <>
            <DeleteMultipleRows
              selectedIds={ids}
              path={rate_routes.DELETE_MULTIPLE_RATES}
              revPath="/"
              setIds={setIds}
              setRowSelection={setRowSelection}
            />
          </>
        )} */}
      </div>
      {total && (
        <div>
          {/*   className="mt-4 bg-gray-100 border border-gray-300 rounded-lg text-center w-60 py-2 flex flex-col justify-center items-center" */}
          {totalview}
        </div>
      )}
      <div className={cn("relative  sm:w-full  cbar ", className)}>
        <Table>
          <TableHeader className="">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className=" text-sm p-2">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="">
            {data ? (
                 <>
              {table.getRowModel()?.rows?.length ? (
              table.getRowModel().rows.map((row, index) => {
                //  ('row',row)
                const hasEmailField = row
                  .getVisibleCells()
                  .some((cell) => cell.column.columnDef.cell === "email");
                return (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    className={`${hasEmailField} ? "" :"capitalize" rounded-xl`}
                  >
                    {row.getVisibleCells().map((cell) => {
                      return (
                        <TableCell key={cell.id} className="text-xs ">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center   dark:bg-gray-700"
                >
                  No Data Found.
                </TableCell>
              </TableRow>
            )}
            </>
            ) : (
                <TableRow>
               <TableCell
                 colSpan={columns.length}
                 className="h-24 text-center   dark:bg-gray-700"
               >
                 No Data Found.
               </TableCell>
             </TableRow>
            )}
         
          
           
          </TableBody>
        </Table>
      </div>
      {/* <div className="flex items-center justify-end space-x-2 p-0.5 ">
        <p className="text-sm">
          Page {table?.getState().pagination.pageIndex + 1} of{" "}
          {table?.getPageCount()}
        </p>
        <Button
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
          className="tracking-wider text-gray-800 bg-white ring-gray-300 hover:bg-gray-50 dark:bg-gray-700 dark:ring-gray-600 dark:text-white !p-1"
        >
          <ChevronLeft />
        </Button>
        <Button
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
          className="tracking-wider text-gray-800 bg-white ring-gray-300 hover:bg-gray-50 dark:bg-gray-700 dark:ring-gray-600 dark:text-white !p-1"
        >
          <ChevronRight />
        </Button>
      </div> */}

      {/* <Pagination
        currentPage={table.getState().pagination.pageIndex + 1}
        totalPages={table.getPageCount()}
        onPageChange={(page: any) => table.setPageIndex(page - 1)}
        entriesPerPage={table.getState().pagination.pageSize}
      /> */}
      {
        data && (
<Pagination
        currentPage={
          totalPages
            ? Number(params.get("page")) || 1
            : table.getState().pagination.pageIndex + 1
        }
        totalPages={totalPages ? totalPages : table.getPageCount()} 
        onPageChange={(page: any) => {
          table.setPageIndex(page - 1);
          if (totalPages) {
            params.set("page", page?.toString());
            replace(`${pathname}?${params.toString()}`);
          }
        }}
      />
        )
      }
      
    </div>
  );
};

export default ServerSideDataTable;
