import { updateItem } from "../../../utils/operation";


export const updateLegrandMapping = async (req, res) => {
    const id = req.params.id;

    const { corporationID } = req;
    const fields = {
        businessUnit: req.body.businessUnit,
        legalName: req.body.legalName,
        customeCode: req.body.customeCode,
        shippingBillingName: req.body.shippingBillingName,
        shippingBillingAddress: req.body.shippingBillingAddress,
        location: req.body.location,
        zipPostal: req.body.zipPostal,
        aliasCity: req.body.aliasCity,
        aliasShippingNames: req.body.aliasShippingNames,
    };

    await updateItem({
        model: "LegrandMapping",
        fieldName: "id",
        fields: fields,
        id: String(id),
        res,
        req,
        successMessage: "LegrandMapping updated successfully",
    });
};