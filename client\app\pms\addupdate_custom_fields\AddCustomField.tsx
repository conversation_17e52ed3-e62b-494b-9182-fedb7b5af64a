"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Minus, X } from "lucide-react";
import { toast } from "sonner";
import axios from "axios";
import { useRouter } from "next/navigation";

const fieldTypes = ["TEXT", "NUMBER", "DATE", "AUTO"];
const autoOptions = ["DATE", "USERNAME"];

export default function AddCustomField({ userData }: { userData: any }) {
  const [newCustomFields, setNewCustomFields] = useState([
    { name: "", type: "", autoOption: "" },
  ]);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const router = useRouter();
  const updateCustomFieldInput = (
    index: number,
    key: "name" | "type" | "autoOption",
    value: string
  ) => {
    setNewCustomFields((prev) => {
      const updated = [...prev];
      updated[index][key] = value;
      // If field name is cleared, reset type and autoOption as well
      if (key === "name" && value.trim() === "") {
        updated[index].type = "";
        updated[index].autoOption = "";
      }
      // If type changes and is not "AUTO", reset autoOption
      if (key === "type" && value !== "AUTO") {
        updated[index].autoOption = "";
      }
      return updated;
    });
  };

  const handleAddCustomFields = async () => {
    const fieldsToAdd = newCustomFields.filter(
      (field) => field.name.trim() !== "" && field.type.trim() !== ""
    );
    if (fieldsToAdd.length === 0) {
      toast.error("Please provide at least one custom field name and type.");
      return;
    }

    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/custom-fields`
      );
      const existingFieldNames = new Set(
        res.data.map((f: any) => f.name.toLowerCase())
      );

      const duplicate = fieldsToAdd.find((field) =>
        existingFieldNames.has(field.name.trim().toLowerCase())
      );
      if (duplicate) {
        toast.error(`Field "${duplicate.name}" already exists.`);
        return;
      }

      const nameSet = new Set<string>();
      for (const field of fieldsToAdd) {
        const lower = field.name.trim().toLowerCase();
        if (nameSet.has(lower)) {
          toast.error(`Duplicate entry: "${field.name}"`);
          return;
        }
        nameSet.add(lower);
      }

      // Validation for AUTO type fields
      for (const field of fieldsToAdd) {
        if (field.type === "AUTO" && !field.autoOption) {
          toast.error(`Please select an auto type for field: "${field.name}"`);
          return;
        }
      }

      const payload = {
        fields: fieldsToAdd.map((field) => ({
          ...field,
          created_by: userData?.username || "Admin",
          updated_by: userData?.username || "Admin",
          autoOption: field.type === "AUTO" ? field.autoOption : null,
        })),
      };
      console.log("Payload to send:", payload);

      await axios.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/custom-fields`,
        payload
      );
      toast.success("Custom fields saved.");
      router.refresh();
      setNewCustomFields([{ name: "", type: "", autoOption: "" }]);
      setIsModalOpen(false);
    } catch (err) {
      console.error("Error saving custom fields:", err);
      toast.error("Failed to save custom fields.");
    }
  };

  // Disable Save button if no valid field exists
  const isSaveDisabled = !newCustomFields.some(
    (field) => field.name.trim() !== "" && field.type.trim() !== ""
  );

  return (
    <>
      <Button
        onClick={() => setIsModalOpen(true)}
        className="bg-primary text-white flex items-center gap-2"
      >
        <Plus size={16} />
        Add Custom Fields
      </Button>

      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white p-6 rounded-md shadow-md w-full max-w-3xl relative">
            <button
              onClick={() => setIsModalOpen(false)}
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
            >
              <X size={20} />
            </button>

            <h3 className="font-semibold text-lg mb-4">
              Add New Custom Field(s)
            </h3>

            <div className="space-y-3">
              {newCustomFields.map((field, idx) => (
                <div
                  key={idx}
                  className="flex items-center gap-2 flex-wrap md:flex-nowrap"
                >
                  <input
                    type="text"
                    placeholder="Field Name"
                    value={field.name}
                    onChange={(e) =>
                      updateCustomFieldInput(idx, "name", e.target.value.toUpperCase())
                    }
                    className="w-[224px] border px-3 py-2 rounded text-sm"
                  />

                  <select
                    value={field.type}
                    onChange={(e) =>
                      updateCustomFieldInput(idx, "type", e.target.value)
                    }
                    className="w-[144px] border px-2 py-2 rounded text-sm"
                  >
                    <option value="">Select Type</option>
                    {fieldTypes.map((type) => (
                      <option key={type} value={type}>
                        {type}
                      </option>
                    ))}
                  </select>

                  <select
                    value={field.type === "AUTO" ? field.autoOption : ""}
                    onChange={(e) =>
                      updateCustomFieldInput(idx, "autoOption", e.target.value)
                    }
                    className={`w-[155px] border px-2 py-2 rounded text-sm ${
                      field.type === "AUTO" ? "block" : "hidden"
                    }`}
                  >
                    <option value="">Select Auto Type</option>
                    {autoOptions.map((opt) => (
                      <option key={opt} value={opt}>
                        {opt}
                      </option>
                    ))}
                  </select>

                  <div className="flex items-center gap-2">
                    {newCustomFields.length > 1 && (
                      <button
                        type="button"
                        onClick={() =>
                          setNewCustomFields((prev) =>
                            prev.filter((_, i) => i !== idx)
                          )
                        }
                        className="w-8 h-8 rounded-full bg-red-100 hover:bg-red-200 text-red-600 flex items-center justify-center transition"
                      >
                        <Minus size={16} />
                      </button>
                    )}
                    {idx === newCustomFields.length - 1 && (
                      <button
                        type="button"
                        onClick={() =>
                          setNewCustomFields((prev) => [
                            ...prev,
                            { name: "", type: "", autoOption: "" },
                          ])
                        }
                        className="w-8 h-8 rounded-full bg-blue-100 hover:bg-blue-200 text-blue-600 flex items-center justify-center transition"
                      >
                        <Plus size={16} />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="flex justify-end mt-6">
              <Button
                type="button"
                onClick={handleAddCustomFields}
                className="bg-green-600 text-white hover:bg-green-700"
                disabled={isSaveDisabled}
              >
                Save Custom Fields
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
