import { createItem } from "../../../utils/operation";


export const createAssociate = async (req, res) => {
    const { corporation_id } = req;

  const fields = {
    name: req.body.name,
    corporation_id: Number(corporation_id),
  };

  await createItem({
    model: "associate",
    fieldName: "id",
    fields: fields,
    res: res as Response,
    req: req,
    successMessage: "Associate has been created",
  });
};